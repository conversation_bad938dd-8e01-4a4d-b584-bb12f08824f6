import 'dart:convert';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/medication.dart';
import '../models/medication_reminder.dart';
import 'notification_service.dart';

class MedicationService {
  static const String _medicationsKey = 'medications';
  static const String _remindersKey = 'medication_reminders';

  // Get all medications
  static Future<List<Medication>> getMedications() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final medicationsJson = prefs.getString(_medicationsKey);

      if (medicationsJson == null) return [];

      final List<dynamic> medicationsList = jsonDecode(medicationsJson);
      return medicationsList.map((json) => Medication.fromJson(json)).toList();
    } catch (e) {
      debugPrint('Error loading medications: $e');
      return [];
    }
  }

  // Get active medications only
  static Future<List<Medication>> getActiveMedications() async {
    final medications = await getMedications();
    return medications.where((med) => med.isActive).toList();
  }

  // Save medication
  static Future<void> saveMedication(Medication medication) async {
    try {
      final medications = await getMedications();
      final existingIndex = medications.indexWhere(
        (med) => med.id == medication.id,
      );

      if (existingIndex >= 0) {
        medications[existingIndex] = medication.copyWith(
          updatedAt: DateTime.now(),
        );
      } else {
        medications.add(medication);
      }

      await _saveMedications(medications);

      // Generate reminders for the medication
      await _generateRemindersForMedication(medication);

      // Schedule notifications if enabled
      if (medication.shouldSendNotifications) {
        await _scheduleNotificationsForMedication(medication);
      }
    } catch (e) {
      debugPrint('Error saving medication: $e');
      rethrow;
    }
  }

  // Delete medication
  static Future<void> deleteMedication(String medicationId) async {
    try {
      final medications = await getMedications();
      medications.removeWhere((med) => med.id == medicationId);
      await _saveMedications(medications);

      // Remove associated reminders
      await _deleteRemindersForMedication(medicationId);

      // Cancel associated notifications
      await _cancelNotificationsForMedication(medicationId);
    } catch (e) {
      debugPrint('Error deleting medication: $e');
      rethrow;
    }
  }

  // Get medication by ID
  static Future<Medication?> getMedicationById(String id) async {
    final medications = await getMedications();
    try {
      return medications.firstWhere((med) => med.id == id);
    } catch (e) {
      return null;
    }
  }

  // Save medications list
  static Future<void> _saveMedications(List<Medication> medications) async {
    final prefs = await SharedPreferences.getInstance();
    final medicationsJson = jsonEncode(
      medications.map((med) => med.toJson()).toList(),
    );
    await prefs.setString(_medicationsKey, medicationsJson);
  }

  // Generate unique ID
  static String generateId() {
    final random = Random();
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final randomNum = random.nextInt(999999);
    return '${timestamp}_$randomNum';
  }

  // Get reminders for a specific date
  static Future<List<MedicationReminder>> getRemindersForDate(
    DateTime date,
  ) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final remindersJson = prefs.getString(_remindersKey);

      if (remindersJson == null) return [];

      final List<dynamic> remindersList = jsonDecode(remindersJson);
      final reminders =
          remindersList
              .map((json) => MedicationReminder.fromJson(json))
              .toList();

      // Filter reminders for the specific date
      return reminders.where((reminder) {
        final reminderDate = reminder.scheduledTime;
        return reminderDate.year == date.year &&
            reminderDate.month == date.month &&
            reminderDate.day == date.day;
      }).toList();
    } catch (e) {
      debugPrint('Error loading reminders: $e');
      return [];
    }
  }

  // Get today's reminders
  static Future<List<MedicationReminder>> getTodayReminders() async {
    return getRemindersForDate(DateTime.now());
  }

  // Get upcoming reminders (next 7 days)
  static Future<List<MedicationReminder>> getUpcomingReminders() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final remindersJson = prefs.getString(_remindersKey);

      if (remindersJson == null) return [];

      final List<dynamic> remindersList = jsonDecode(remindersJson);
      final reminders =
          remindersList
              .map((json) => MedicationReminder.fromJson(json))
              .toList();

      final now = DateTime.now();
      final nextWeek = now.add(const Duration(days: 7));

      return reminders.where((reminder) {
          return reminder.scheduledTime.isAfter(now) &&
              reminder.scheduledTime.isBefore(nextWeek) &&
              reminder.status == ReminderStatus.pending;
        }).toList()
        ..sort((a, b) => a.scheduledTime.compareTo(b.scheduledTime));
    } catch (e) {
      debugPrint('Error loading upcoming reminders: $e');
      return [];
    }
  }

  // Update reminder status
  static Future<void> updateReminderStatus(
    String reminderId,
    ReminderStatus status, {
    String? notes,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final remindersJson = prefs.getString(_remindersKey);

      if (remindersJson == null) return;

      final List<dynamic> remindersList = jsonDecode(remindersJson);
      final reminders =
          remindersList
              .map((json) => MedicationReminder.fromJson(json))
              .toList();

      final reminderIndex = reminders.indexWhere((r) => r.id == reminderId);
      if (reminderIndex >= 0) {
        reminders[reminderIndex] = reminders[reminderIndex].copyWith(
          status: status,
          takenAt: status == ReminderStatus.taken ? DateTime.now() : null,
          notes: notes,
          updatedAt: DateTime.now(),
        );

        await _saveReminders(reminders);
      }
    } catch (e) {
      debugPrint('Error updating reminder status: $e');
      rethrow;
    }
  }

  // Generate reminders for a medication
  static Future<void> _generateRemindersForMedication(
    Medication medication,
  ) async {
    if (!medication.isActive || !medication.reminderEnabled) return;

    try {
      final reminders = await _getAllReminders();

      // Remove existing future reminders for this medication
      reminders.removeWhere(
        (reminder) =>
            reminder.medicationId == medication.id &&
            reminder.scheduledTime.isAfter(DateTime.now()),
      );

      // Generate reminders for the next 30 days
      final now = DateTime.now();
      for (int day = 0; day < 30; day++) {
        final date = now.add(Duration(days: day));

        // Skip if medication has an end date and this date is after it
        if (medication.endDate != null && date.isAfter(medication.endDate!)) {
          break;
        }

        // Generate reminders for each time in the medication schedule
        for (final medicationTime in medication.times) {
          final scheduledTime = DateTime(
            date.year,
            date.month,
            date.day,
            medicationTime.time.hour,
            medicationTime.time.minute,
          );

          // Only create reminders for future times
          if (scheduledTime.isAfter(now)) {
            final reminder = MedicationReminder(
              id: generateId(),
              medicationId: medication.id,
              scheduledTime: scheduledTime,
              status: ReminderStatus.pending,
              createdAt: now,
              updatedAt: now,
            );

            reminders.add(reminder);
          }
        }
      }

      await _saveReminders(reminders);
    } catch (e) {
      debugPrint('Error generating reminders: $e');
    }
  }

  // Delete reminders for a medication
  static Future<void> _deleteRemindersForMedication(String medicationId) async {
    try {
      final reminders = await _getAllReminders();
      reminders.removeWhere(
        (reminder) => reminder.medicationId == medicationId,
      );
      await _saveReminders(reminders);
    } catch (e) {
      debugPrint('Error deleting reminders: $e');
    }
  }

  // Get all reminders
  static Future<List<MedicationReminder>> _getAllReminders() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final remindersJson = prefs.getString(_remindersKey);

      if (remindersJson == null) return [];

      final List<dynamic> remindersList = jsonDecode(remindersJson);
      return remindersList
          .map((json) => MedicationReminder.fromJson(json))
          .toList();
    } catch (e) {
      debugPrint('Error loading all reminders: $e');
      return [];
    }
  }

  // Save reminders list
  static Future<void> _saveReminders(List<MedicationReminder> reminders) async {
    final prefs = await SharedPreferences.getInstance();
    final remindersJson = jsonEncode(reminders.map((r) => r.toJson()).toList());
    await prefs.setString(_remindersKey, remindersJson);
  }

  // Get medication statistics
  static Future<MedicationStats> getMedicationStats() async {
    try {
      final medications = await getMedications();
      final todayReminders = await getTodayReminders();

      final activeMedications = medications.where((med) => med.isActive).length;
      final completedToday =
          todayReminders.where((r) => r.status == ReminderStatus.taken).length;
      final missedToday =
          todayReminders.where((r) => r.status == ReminderStatus.missed).length;

      // Calculate overall adherence (last 7 days)
      double overallAdherence = 0.0;
      int totalReminders = 0;
      int takenReminders = 0;

      for (int i = 0; i < 7; i++) {
        final date = DateTime.now().subtract(Duration(days: i));
        final dayReminders = await getRemindersForDate(date);
        totalReminders += dayReminders.length;
        takenReminders +=
            dayReminders.where((r) => r.status == ReminderStatus.taken).length;
      }

      if (totalReminders > 0) {
        overallAdherence = (takenReminders / totalReminders) * 100;
      }

      return MedicationStats(
        totalMedications: medications.length,
        activeMedications: activeMedications,
        todayReminders: todayReminders.length,
        completedToday: completedToday,
        missedToday: missedToday,
        overallAdherence: overallAdherence,
      );
    } catch (e) {
      debugPrint('Error calculating medication stats: $e');
      return const MedicationStats(
        totalMedications: 0,
        activeMedications: 0,
        todayReminders: 0,
        completedToday: 0,
        missedToday: 0,
        overallAdherence: 0.0,
      );
    }
  }

  // Schedule notifications for a medication
  static Future<void> _scheduleNotificationsForMedication(
    Medication medication,
  ) async {
    try {
      // Get future reminders for this medication
      final reminders = await _getAllReminders();
      final medicationReminders =
          reminders
              .where(
                (r) =>
                    r.medicationId == medication.id &&
                    r.scheduledTime.isAfter(DateTime.now()) &&
                    r.status == ReminderStatus.pending,
              )
              .toList();

      // Schedule notifications for each reminder
      await NotificationService.scheduleMedicationReminders(
        medicationReminders,
        medication,
      );
    } catch (e) {
      debugPrint('Error scheduling notifications for medication: $e');
    }
  }

  // Cancel notifications for a medication
  static Future<void> _cancelNotificationsForMedication(
    String medicationId,
  ) async {
    try {
      await NotificationService.cancelMedicationReminders(medicationId);
    } catch (e) {
      debugPrint('Error cancelling notifications for medication: $e');
    }
  }
}
