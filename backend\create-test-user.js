const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
require('dotenv').config();

// User schema (simplified version)
const userSchema = new mongoose.Schema({
    name: { type: String, required: true },
    email: { type: String, required: true, unique: true },
    password: { type: String, required: true },
    phoneNumber: { type: String, required: true, unique: true },
    isPhoneVerified: { type: Boolean, default: false },
    createdAt: { type: Date, default: Date.now }
});

// Hash password before saving
userSchema.pre('save', async function(next) {
    if (!this.isModified('password')) {
        next();
    }
    const salt = await bcrypt.genSalt(10);
    this.password = await bcrypt.hash(this.password, salt);
});

// Generate JWT token
userSchema.methods.getSignedJwtToken = function() {
    return jwt.sign({ id: this._id }, process.env.JWT_SECRET, {
        expiresIn: process.env.JWT_EXPIRE || '30d'
    });
};

const User = mongoose.model('User', userSchema);

// Glucose reading schema for test data
const glucoseReadingSchema = new mongoose.Schema({
    userId: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
    value: { type: Number, required: true },
    timestamp: { type: Date, default: Date.now },
    mealTiming: {
        type: String,
        enum: ['before_breakfast', 'after_breakfast', 'before_lunch', 'after_lunch', 'before_dinner', 'after_dinner', 'bedtime', 'other'],
        default: 'other'
    },
    notes: String
});

const GlucoseReading = mongoose.model('GlucoseReading', glucoseReadingSchema);

const createTestUser = async () => {
    try {
        console.log('🔗 Connecting to MongoDB...');
        await mongoose.connect(process.env.MONGODB_URI);
        console.log('✅ Connected to MongoDB\n');

        const testUser = {
            name: 'Test User',
            email: '<EMAIL>',
            password: 'TestPassword123!',
            phoneNumber: '+27123456789',
            isPhoneVerified: true // Skip phone verification for testing
        };

        // Check if user already exists
        let user = await User.findOne({ email: testUser.email });
        
        if (user) {
            console.log('ℹ️  Test user already exists');
        } else {
            console.log('👤 Creating test user...');
            user = new User(testUser);
            await user.save();
            console.log('✅ Test user created successfully');
        }

        // Generate JWT token
        const token = user.getSignedJwtToken();
        console.log('\n🔑 JWT Token Generated:');
        console.log('==========================================');
        console.log(token);
        console.log('==========================================\n');

        // Create some test glucose readings
        console.log('📊 Creating test glucose readings...');
        
        const existingReadings = await GlucoseReading.countDocuments({ userId: user._id });
        
        if (existingReadings > 0) {
            console.log(`ℹ️  User already has ${existingReadings} glucose readings`);
        } else {
            const testReadings = [
                { userId: user._id, value: 95, timestamp: new Date('2024-01-01T08:00:00Z'), mealTiming: 'before_breakfast' },
                { userId: user._id, value: 140, timestamp: new Date('2024-01-01T10:00:00Z'), mealTiming: 'after_breakfast' },
                { userId: user._id, value: 110, timestamp: new Date('2024-01-01T12:00:00Z'), mealTiming: 'before_lunch' },
                { userId: user._id, value: 160, timestamp: new Date('2024-01-01T14:00:00Z'), mealTiming: 'after_lunch' },
                { userId: user._id, value: 105, timestamp: new Date('2024-01-01T18:00:00Z'), mealTiming: 'before_dinner' },
                { userId: user._id, value: 145, timestamp: new Date('2024-01-01T20:00:00Z'), mealTiming: 'after_dinner' },
                { userId: user._id, value: 120, timestamp: new Date('2024-01-01T22:00:00Z'), mealTiming: 'bedtime' },
                { userId: user._id, value: 88, timestamp: new Date('2024-01-02T08:00:00Z'), mealTiming: 'before_breakfast' },
                { userId: user._id, value: 135, timestamp: new Date('2024-01-02T10:00:00Z'), mealTiming: 'after_breakfast' },
                { userId: user._id, value: 115, timestamp: new Date('2024-01-02T12:00:00Z'), mealTiming: 'before_lunch' }
            ];

            await GlucoseReading.insertMany(testReadings);
            console.log(`✅ Created ${testReadings.length} test glucose readings`);
        }

        console.log('\n🎯 Test User Details:');
        console.log(`   Name: ${user.name}`);
        console.log(`   Email: ${user.email}`);
        console.log(`   Phone: ${user.phoneNumber}`);
        console.log(`   User ID: ${user._id}`);
        console.log(`   Phone Verified: ${user.isPhoneVerified}`);

        console.log('\n🚀 Ready to Test Export Functionality!');
        console.log('=====================================');
        console.log('1. Copy the JWT token above');
        console.log('2. Run: node simple-export-test.js "YOUR_JWT_TOKEN"');
        console.log('3. Or run: node test-export.js (after updating the token)');
        console.log('');
        console.log('📋 Quick Test Commands:');
        console.log(`curl -H "Authorization: Bearer ${token}" \\`);
        console.log('     "http://localhost:5000/api/glucose/export/chart?format=json&chartType=line"');
        console.log('');

        await mongoose.disconnect();
        console.log('✅ Disconnected from MongoDB');

    } catch (error) {
        console.error('❌ Error creating test user:', error.message);
        if (error.code === 11000) {
            console.log('   This usually means the user already exists');
            console.log('   Try deleting the existing user first or use different credentials');
        }
        process.exit(1);
    }
};

const deleteTestUser = async () => {
    try {
        console.log('🔗 Connecting to MongoDB...');
        await mongoose.connect(process.env.MONGODB_URI);
        console.log('✅ Connected to MongoDB\n');

        const user = await User.findOne({ email: '<EMAIL>' });
        
        if (user) {
            // Delete glucose readings first
            const deletedReadings = await GlucoseReading.deleteMany({ userId: user._id });
            console.log(`🗑️  Deleted ${deletedReadings.deletedCount} glucose readings`);

            // Delete user
            await User.findByIdAndDelete(user._id);
            console.log('🗑️  Deleted test user');
        } else {
            console.log('ℹ️  Test user not found');
        }

        await mongoose.disconnect();
        console.log('✅ Disconnected from MongoDB');

    } catch (error) {
        console.error('❌ Error deleting test user:', error.message);
        process.exit(1);
    }
};

// Check command line arguments
const command = process.argv[2];

if (command === 'delete') {
    deleteTestUser();
} else {
    console.log('🧪 GlucoMonitor Test User Creator');
    console.log('=================================\n');
    console.log('This script will:');
    console.log('1. Create a test user with verified phone');
    console.log('2. Generate sample glucose readings');
    console.log('3. Provide a JWT token for testing\n');
    
    createTestUser();
}

console.log('💡 Usage:');
console.log('   node create-test-user.js        # Create test user');
console.log('   node create-test-user.js delete # Delete test user');
