# Environment variables
.env
.env.*
!.env.example

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build output
dist/
build/
*.tsbuildinfo

# IDE specific files
.idea/
.vscode/
*.swp
*.swo
.DS_Store

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Debug logs
debug/

# Redis files
dump.rdb

# MongoDB local files
data/
*.mongodb


# Testing
coverage/
.nyc_output/

# TypeScript cache
*.tsbuildinfo

# Flutter/Dart generated folders (if in same repo)
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.packages
.pub-cache/
.pub/
