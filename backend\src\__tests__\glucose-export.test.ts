import request from 'supertest';
import app from '../index';
import User from '../models/User';
import GlucoseReading from '../models/GlucoseReading';
import jwt from 'jsonwebtoken';

describe('Glucose Export Endpoints', () => {
    let authToken: string;
    let userId: string;

    beforeAll(async () => {
        // Create a test user
        const testUser = await User.create({
            name: 'Test User',
            email: '<EMAIL>',
            password: 'password123',
            phoneNumber: '+27123456789',
            isPhoneVerified: true
        });
        userId = (testUser._id as any).toString();

        // Generate auth token
        authToken = jwt.sign(
            { id: userId },
            process.env.JWT_SECRET || 'test-secret',
            { expiresIn: '1h' }
        );

        // Create some test glucose readings
        const testReadings = [
            {
                userId: testUser._id as any,
                value: 95,
                timestamp: new Date('2024-01-01T08:00:00Z'),
                mealTiming: 'before_breakfast',
                context: 'fasting',
                isManual: true
            },
            {
                userId: testUser._id as any,
                value: 140,
                timestamp: new Date('2024-01-01T10:00:00Z'),
                mealTiming: 'after_breakfast',
                context: 'post_meal',
                isManual: true
            },
            {
                userId: testUser._id as any,
                value: 110,
                timestamp: new Date('2024-01-01T14:00:00Z'),
                mealTiming: 'before_lunch',
                context: 'fasting',
                isManual: true
            }
        ];

        await GlucoseReading.insertMany(testReadings);
    });

    afterAll(async () => {
        // Clean up test data
        await GlucoseReading.deleteMany({ userId });
        await User.findByIdAndDelete(userId);
    });

    describe('GET /api/glucose/export/chart', () => {
        it('should export chart data in JSON format', async () => {
            const response = await request(app)
                .get('/api/glucose/export/chart')
                .set('Authorization', `Bearer ${authToken}`)
                .query({
                    chartType: 'line',
                    format: 'json'
                });

            expect(response.status).toBe(200);
            expect(response.body.success).toBe(true);
            expect(response.body.data).toHaveProperty('chartType', 'line');
            expect(response.body.data).toHaveProperty('data');
            expect(response.body.data).toHaveProperty('metadata');
            expect(Array.isArray(response.body.data.data)).toBe(true);
            expect(response.body.data.dataPoints).toBeGreaterThan(0);
        });

        it('should export chart data in CSV format', async () => {
            const response = await request(app)
                .get('/api/glucose/export/chart')
                .set('Authorization', `Bearer ${authToken}`)
                .query({
                    chartType: 'line',
                    format: 'csv'
                });

            expect(response.status).toBe(200);
            expect(response.headers['content-type']).toContain('text/csv');
            expect(response.headers['content-disposition']).toContain('glucose_chart_line.csv');
            expect(typeof response.text).toBe('string');
            expect(response.text).toContain('Index,Value,Timestamp,Category');
        });

        it('should validate chart type parameter', async () => {
            const response = await request(app)
                .get('/api/glucose/export/chart')
                .set('Authorization', `Bearer ${authToken}`)
                .query({
                    chartType: 'invalid_type',
                    format: 'json'
                });

            expect(response.status).toBe(400);
            expect(response.body.success).toBe(false);
            expect(response.body.message).toContain('Invalid chart type');
        });

        it('should require authentication', async () => {
            const response = await request(app)
                .get('/api/glucose/export/chart')
                .query({
                    chartType: 'line',
                    format: 'json'
                });

            expect(response.status).toBe(401);
        });
    });

    describe('GET /api/glucose/analytics', () => {
        it('should return advanced analytics', async () => {
            const response = await request(app)
                .get('/api/glucose/analytics')
                .set('Authorization', `Bearer ${authToken}`);

            expect(response.status).toBe(200);
            expect(response.body.success).toBe(true);
            expect(response.body.data).toHaveProperty('timeInRange');
            expect(response.body.data).toHaveProperty('averageByTimeOfDay');
            expect(response.body.data).toHaveProperty('totalReadings');
        });
    });

    describe('GET /api/glucose/insights', () => {
        it('should return glucose insights', async () => {
            const response = await request(app)
                .get('/api/glucose/insights')
                .set('Authorization', `Bearer ${authToken}`);

            expect(response.status).toBe(200);
            expect(response.body.success).toBe(true);
            expect(response.body.data).toHaveProperty('averageByTimeOfDay');
            expect(response.body.data).toHaveProperty('patterns');
            expect(response.body.data).toHaveProperty('trends');
            expect(response.body.data).toHaveProperty('alerts');
        });
    });
});
