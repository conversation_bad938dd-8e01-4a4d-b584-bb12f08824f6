import { Request, Response } from 'express';
import mongoose from 'mongoose';
import Medication, { IMedication, MedicationFrequency, IMedicationTime } from '../models/Medication';
import MedicationReminder, { IMedicationReminder, ReminderStatus } from '../models/MedicationReminder';

// Extend Request interface to include user
interface AuthenticatedRequest extends Request {
    user?: {
        id: string;
    };
}

// Helper function to generate reminder times based on frequency
const generateReminderTimes = (medication: IMedication): IMedicationTime[] => {
    const { frequency, customTimes } = medication;

    if (frequency === MedicationFrequency.CUSTOM) {
        return customTimes;
    }

    const defaultTimes: Record<MedicationFrequency, IMedicationTime[]> = {
        [MedicationFrequency.ONCE_DAILY]: [{ time: '08:00', label: 'Morning' }],
        [MedicationFrequency.TWICE_DAILY]: [
            { time: '08:00', label: 'Morning' },
            { time: '20:00', label: 'Evening' }
        ],
        [MedicationFrequency.THREE_TIMES_DAILY]: [
            { time: '08:00', label: 'Morning' },
            { time: '14:00', label: 'Afternoon' },
            { time: '20:00', label: 'Evening' }
        ],
        [MedicationFrequency.FOUR_TIMES_DAILY]: [
            { time: '08:00', label: 'Morning' },
            { time: '12:00', label: 'Noon' },
            { time: '16:00', label: 'Afternoon' },
            { time: '20:00', label: 'Evening' }
        ],
        [MedicationFrequency.EVERY_OTHER_DAY]: [{ time: '08:00', label: 'Morning' }],
        [MedicationFrequency.WEEKLY]: [{ time: '08:00', label: 'Morning' }],
        [MedicationFrequency.AS_NEEDED]: [],
        [MedicationFrequency.CUSTOM]: customTimes
    };

    return defaultTimes[frequency] || [];
};

// Helper function to generate reminders for a medication
const generateReminders = async (medication: IMedication, days: number = 30): Promise<void> => {
    if (!medication.reminderEnabled || medication.frequency === MedicationFrequency.AS_NEEDED) {
        return;
    }

    const reminderTimes = generateReminderTimes(medication);
    if (reminderTimes.length === 0) {
        return;
    }

    const reminders: Partial<IMedicationReminder>[] = [];
    const startDate = new Date(medication.startDate);
    const endDate = medication.endDate || new Date(Date.now() + days * 24 * 60 * 60 * 1000);

    let currentDate = new Date(startDate);
    let dayCounter = 0;

    while (currentDate <= endDate) {
        let shouldCreateReminder = true;

        // Handle frequency-specific logic
        if (medication.frequency === MedicationFrequency.EVERY_OTHER_DAY) {
            shouldCreateReminder = dayCounter % 2 === 0;
        } else if (medication.frequency === MedicationFrequency.WEEKLY) {
            shouldCreateReminder = dayCounter % 7 === 0;
        }

        if (shouldCreateReminder) {
            for (const timeSlot of reminderTimes) {
                const [hours, minutes] = timeSlot.time.split(':').map(Number);
                const scheduledTime = new Date(currentDate);
                scheduledTime.setHours(hours, minutes, 0, 0);

                // Only create reminders for future times
                if (scheduledTime > new Date()) {
                    reminders.push({
                        userId: medication.userId,
                        medicationId: medication._id as mongoose.Types.ObjectId,
                        scheduledTime,
                        status: ReminderStatus.PENDING,
                        notificationSent: false
                    });
                }
            }
        }

        currentDate.setDate(currentDate.getDate() + 1);
        dayCounter++;
    }

    if (reminders.length > 0) {
        await MedicationReminder.insertMany(reminders);
    }
};

// @desc    Get all medications for user
// @route   GET /api/medications
// @access  Private
export const getMedications = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    try {
        const userId = req.user?.id;
        if (!userId) {
            res.status(401).json({ success: false, message: 'User not authenticated' });
            return;
        }

        const { active, page = 1, limit = 50 } = req.query;
        const filter: any = { userId: new mongoose.Types.ObjectId(userId) };

        if (active !== undefined) {
            filter.isActive = active === 'true';
        }

        const medications = await Medication.find(filter)
            .sort({ createdAt: -1 })
            .limit(Number(limit) * Number(page))
            .skip((Number(page) - 1) * Number(limit))
            .populate('userId', 'name email');

        const total = await Medication.countDocuments(filter);

        res.status(200).json({
            success: true,
            count: medications.length,
            total,
            page: Number(page),
            pages: Math.ceil(total / Number(limit)),
            data: medications
        });
    } catch (error) {
        console.error('Get medications error:', error);
        res.status(500).json({
            success: false,
            message: 'Server error while fetching medications'
        });
    }
};

// @desc    Get single medication
// @route   GET /api/medications/:id
// @access  Private
export const getMedication = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    try {
        const userId = req.user?.id;
        const { id } = req.params;

        if (!userId) {
            res.status(401).json({ success: false, message: 'User not authenticated' });
            return;
        }

        if (!mongoose.Types.ObjectId.isValid(id)) {
            res.status(400).json({ success: false, message: 'Invalid medication ID' });
            return;
        }

        const medication = await Medication.findOne({
            _id: id,
            userId: new mongoose.Types.ObjectId(userId)
        }).populate('userId', 'name email');

        if (!medication) {
            res.status(404).json({ success: false, message: 'Medication not found' });
            return;
        }

        res.status(200).json({
            success: true,
            data: medication
        });
    } catch (error) {
        console.error('Get medication error:', error);
        res.status(500).json({
            success: false,
            message: 'Server error while fetching medication'
        });
    }
};

// @desc    Create new medication
// @route   POST /api/medications
// @access  Private
export const createMedication = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    try {
        const userId = req.user?.id;
        if (!userId) {
            res.status(401).json({ success: false, message: 'User not authenticated' });
            return;
        }

        const medicationData = {
            ...req.body,
            userId: new mongoose.Types.ObjectId(userId)
        };

        const medication = await Medication.create(medicationData);

        // Generate reminders for the medication
        await generateReminders(medication);

        res.status(201).json({
            success: true,
            data: medication,
            message: 'Medication created successfully'
        });
    } catch (error: any) {
        console.error('Create medication error:', error);
        
        if (error.name === 'ValidationError') {
            const messages = Object.values(error.errors).map((err: any) => err.message);
            res.status(400).json({
                success: false,
                message: 'Validation error',
                errors: messages
            });
            return;
        }

        res.status(500).json({
            success: false,
            message: 'Server error while creating medication'
        });
    }
};

// @desc    Update medication
// @route   PUT /api/medications/:id
// @access  Private
export const updateMedication = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    try {
        const userId = req.user?.id;
        const { id } = req.params;

        if (!userId) {
            res.status(401).json({ success: false, message: 'User not authenticated' });
            return;
        }

        if (!mongoose.Types.ObjectId.isValid(id)) {
            res.status(400).json({ success: false, message: 'Invalid medication ID' });
            return;
        }

        const medication = await Medication.findOneAndUpdate(
            { _id: id, userId: new mongoose.Types.ObjectId(userId) },
            req.body,
            { new: true, runValidators: true }
        );

        if (!medication) {
            res.status(404).json({ success: false, message: 'Medication not found' });
            return;
        }

        // If reminder settings changed, regenerate reminders
        if (req.body.reminderEnabled !== undefined || req.body.frequency || req.body.customTimes) {
            // Delete existing pending reminders
            await MedicationReminder.deleteMany({
                medicationId: medication._id,
                status: ReminderStatus.PENDING,
                scheduledTime: { $gte: new Date() }
            });

            // Generate new reminders
            await generateReminders(medication);
        }

        res.status(200).json({
            success: true,
            data: medication,
            message: 'Medication updated successfully'
        });
    } catch (error: any) {
        console.error('Update medication error:', error);
        
        if (error.name === 'ValidationError') {
            const messages = Object.values(error.errors).map((err: any) => err.message);
            res.status(400).json({
                success: false,
                message: 'Validation error',
                errors: messages
            });
            return;
        }

        res.status(500).json({
            success: false,
            message: 'Server error while updating medication'
        });
    }
};

// @desc    Delete medication
// @route   DELETE /api/medications/:id
// @access  Private
export const deleteMedication = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    try {
        const userId = req.user?.id;
        const { id } = req.params;

        if (!userId) {
            res.status(401).json({ success: false, message: 'User not authenticated' });
            return;
        }

        if (!mongoose.Types.ObjectId.isValid(id)) {
            res.status(400).json({ success: false, message: 'Invalid medication ID' });
            return;
        }

        const medication = await Medication.findOneAndDelete({
            _id: id,
            userId: new mongoose.Types.ObjectId(userId)
        });

        if (!medication) {
            res.status(404).json({ success: false, message: 'Medication not found' });
            return;
        }

        // Delete all associated reminders
        await MedicationReminder.deleteMany({ medicationId: medication._id });

        res.status(200).json({
            success: true,
            message: 'Medication deleted successfully'
        });
    } catch (error) {
        console.error('Delete medication error:', error);
        res.status(500).json({
            success: false,
            message: 'Server error while deleting medication'
        });
    }
};

// @desc    Get medication reminders
// @route   GET /api/medications/:id/reminders
// @access  Private
export const getMedicationReminders = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    try {
        const userId = req.user?.id;
        const { id } = req.params;
        const { startDate, endDate, status, page = 1, limit = 50 } = req.query;

        if (!userId) {
            res.status(401).json({ success: false, message: 'User not authenticated' });
            return;
        }

        if (!mongoose.Types.ObjectId.isValid(id)) {
            res.status(400).json({ success: false, message: 'Invalid medication ID' });
            return;
        }

        // Verify medication belongs to user
        const medication = await Medication.findOne({
            _id: id,
            userId: new mongoose.Types.ObjectId(userId)
        });

        if (!medication) {
            res.status(404).json({ success: false, message: 'Medication not found' });
            return;
        }

        const filter: any = { medicationId: new mongoose.Types.ObjectId(id) };

        if (startDate && endDate) {
            filter.scheduledTime = {
                $gte: new Date(startDate as string),
                $lte: new Date(endDate as string)
            };
        }

        if (status) {
            filter.status = status;
        }

        const reminders = await MedicationReminder.find(filter)
            .sort({ scheduledTime: -1 })
            .limit(Number(limit) * Number(page))
            .skip((Number(page) - 1) * Number(limit))
            .populate('medicationId', 'name brandName dosage dosageUnit');

        const total = await MedicationReminder.countDocuments(filter);

        res.status(200).json({
            success: true,
            count: reminders.length,
            total,
            page: Number(page),
            pages: Math.ceil(total / Number(limit)),
            data: reminders
        });
    } catch (error) {
        console.error('Get medication reminders error:', error);
        res.status(500).json({
            success: false,
            message: 'Server error while fetching reminders'
        });
    }
};

// @desc    Get all reminders for user
// @route   GET /api/medications/reminders
// @access  Private
export const getAllReminders = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    try {
        const userId = req.user?.id;
        const { date, status, page = 1, limit = 50 } = req.query;

        if (!userId) {
            res.status(401).json({ success: false, message: 'User not authenticated' });
            return;
        }

        const filter: any = { userId: new mongoose.Types.ObjectId(userId) };

        if (date) {
            const targetDate = new Date(date as string);
            const startOfDay = new Date(targetDate);
            startOfDay.setHours(0, 0, 0, 0);
            const endOfDay = new Date(targetDate);
            endOfDay.setHours(23, 59, 59, 999);

            filter.scheduledTime = {
                $gte: startOfDay,
                $lte: endOfDay
            };
        }

        if (status) {
            filter.status = status;
        }

        const reminders = await MedicationReminder.find(filter)
            .sort({ scheduledTime: 1 })
            .limit(Number(limit) * Number(page))
            .skip((Number(page) - 1) * Number(limit))
            .populate('medicationId', 'name brandName dosage dosageUnit color type');

        const total = await MedicationReminder.countDocuments(filter);

        res.status(200).json({
            success: true,
            count: reminders.length,
            total,
            page: Number(page),
            pages: Math.ceil(total / Number(limit)),
            data: reminders
        });
    } catch (error) {
        console.error('Get all reminders error:', error);
        res.status(500).json({
            success: false,
            message: 'Server error while fetching reminders'
        });
    }
};

// @desc    Update reminder status
// @route   PUT /api/medications/reminders/:id
// @access  Private
export const updateReminderStatus = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    try {
        const userId = req.user?.id;
        const { id } = req.params;
        const { status, notes, actualTime } = req.body;

        if (!userId) {
            res.status(401).json({ success: false, message: 'User not authenticated' });
            return;
        }

        if (!mongoose.Types.ObjectId.isValid(id)) {
            res.status(400).json({ success: false, message: 'Invalid reminder ID' });
            return;
        }

        if (!Object.values(ReminderStatus).includes(status)) {
            res.status(400).json({ success: false, message: 'Invalid status' });
            return;
        }

        const updateData: any = { status };

        if (notes) updateData.notes = notes;
        if (actualTime) updateData.actualTime = new Date(actualTime);
        else if (status === ReminderStatus.TAKEN) updateData.actualTime = new Date();

        const reminder = await MedicationReminder.findOneAndUpdate(
            { _id: id, userId: new mongoose.Types.ObjectId(userId) },
            updateData,
            { new: true, runValidators: true }
        ).populate('medicationId', 'name brandName dosage dosageUnit color');

        if (!reminder) {
            res.status(404).json({ success: false, message: 'Reminder not found' });
            return;
        }

        res.status(200).json({
            success: true,
            data: reminder,
            message: 'Reminder updated successfully'
        });
    } catch (error) {
        console.error('Update reminder status error:', error);
        res.status(500).json({
            success: false,
            message: 'Server error while updating reminder'
        });
    }
};

// @desc    Get medication statistics
// @route   GET /api/medications/stats
// @access  Private
export const getMedicationStats = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    try {
        const userId = req.user?.id;
        const { startDate, endDate } = req.query;

        if (!userId) {
            res.status(401).json({ success: false, message: 'User not authenticated' });
            return;
        }

        const start = startDate ? new Date(startDate as string) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
        const end = endDate ? new Date(endDate as string) : new Date();

        const stats = await MedicationReminder.getAdherenceStats(
            new mongoose.Types.ObjectId(userId),
            start,
            end
        );

        res.status(200).json({
            success: true,
            data: stats
        });
    } catch (error) {
        console.error('Get medication stats error:', error);
        res.status(500).json({
            success: false,
            message: 'Server error while fetching statistics'
        });
    }
};

// @desc    Bulk update reminder statuses
// @route   PUT /api/medications/reminders/bulk
// @access  Private
export const bulkUpdateReminders = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    try {
        const userId = req.user?.id;
        const { updates } = req.body; // Array of { id, status, notes?, actualTime? }

        if (!userId) {
            res.status(401).json({ success: false, message: 'User not authenticated' });
            return;
        }

        if (!Array.isArray(updates) || updates.length === 0) {
            res.status(400).json({ success: false, message: 'Updates array is required' });
            return;
        }

        const results = [];
        const errors = [];

        for (const update of updates) {
            try {
                const { id, status, notes, actualTime } = update;

                if (!mongoose.Types.ObjectId.isValid(id)) {
                    errors.push({ id, error: 'Invalid reminder ID' });
                    continue;
                }

                if (!Object.values(ReminderStatus).includes(status)) {
                    errors.push({ id, error: 'Invalid status' });
                    continue;
                }

                const updateData: any = { status };
                if (notes) updateData.notes = notes;
                if (actualTime) updateData.actualTime = new Date(actualTime);
                else if (status === ReminderStatus.TAKEN) updateData.actualTime = new Date();

                const reminder = await MedicationReminder.findOneAndUpdate(
                    { _id: id, userId: new mongoose.Types.ObjectId(userId) },
                    updateData,
                    { new: true, runValidators: true }
                );

                if (reminder) {
                    results.push(reminder);
                } else {
                    errors.push({ id, error: 'Reminder not found' });
                }
            } catch (error) {
                errors.push({ id: update.id, error: 'Update failed' });
            }
        }

        res.status(200).json({
            success: true,
            data: {
                updated: results,
                errors: errors
            },
            message: `${results.length} reminders updated successfully`
        });
    } catch (error) {
        console.error('Bulk update reminders error:', error);
        res.status(500).json({
            success: false,
            message: 'Server error while updating reminders'
        });
    }
};

// @desc    Generate reminders for medication
// @route   POST /api/medications/:id/generate-reminders
// @access  Private
export const generateMedicationReminders = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    try {
        const userId = req.user?.id;
        const { id } = req.params;
        const { days = 30 } = req.body;

        if (!userId) {
            res.status(401).json({ success: false, message: 'User not authenticated' });
            return;
        }

        if (!mongoose.Types.ObjectId.isValid(id)) {
            res.status(400).json({ success: false, message: 'Invalid medication ID' });
            return;
        }

        const medication = await Medication.findOne({
            _id: id,
            userId: new mongoose.Types.ObjectId(userId)
        });

        if (!medication) {
            res.status(404).json({ success: false, message: 'Medication not found' });
            return;
        }

        // Delete existing future reminders
        await MedicationReminder.deleteMany({
            medicationId: medication._id,
            status: ReminderStatus.PENDING,
            scheduledTime: { $gte: new Date() }
        });

        // Generate new reminders
        await generateReminders(medication, Number(days));

        res.status(200).json({
            success: true,
            message: `Reminders generated for ${days} days`
        });
    } catch (error) {
        console.error('Generate reminders error:', error);
        res.status(500).json({
            success: false,
            message: 'Server error while generating reminders'
        });
    }
};
