import 'package:flutter/material.dart';
import '../../constants/app_colors.dart';
import 'package:provider/provider.dart';
import '../../providers/auth_provider.dart';
import '../../providers/language_provider.dart';
import '../onboarding/welcome_screen.dart';

class MoreScreen extends StatelessWidget {
  const MoreScreen({super.key});

  String _getLanguageName(String code) {
    final Map<String, String> languages = {
      'af': 'Afrikaans',
      'en': 'English',
      'nr': 'isiNdebele',
      'xh': 'isiXhosa',
      'zu': 'isiZulu',
      'nso': 'Sepedi',
      'st': 'Sesotho',
      'tn': 'Setswana',
      'ss': 'siSwati',
      've': 'Tshivenda',
      'ts': 'Xitsonga',
    };
    return languages[code] ?? code;
  }

  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context);
    final languageProvider = Provider.of<LanguageProvider>(context);

    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text('Profile'),
        backgroundColor: AppColors.background,
        foregroundColor: AppColors.onBackground,
      ),
      body: ListView(
        children: [
          // Profile Section
          ListTile(
            leading: const Icon(Icons.person, color: AppColors.onBackground),
            title: const Text(
              'Profile',
              style: TextStyle(color: AppColors.onBackground),
            ),
            subtitle: Text(
              authProvider.name ?? 'Not set',
              style: const TextStyle(color: AppColors.textSecondary),
            ),
            trailing: const Icon(
              Icons.chevron_right,
              color: AppColors.onBackground,
            ),
            onTap: () {
              // TODO: Navigate to profile screen
            },
          ),
          const Divider(),

          // Language Settings
          ListTile(
            leading: const Icon(Icons.language, color: AppColors.onBackground),
            title: const Text(
              'Language',
              style: TextStyle(color: AppColors.onBackground),
            ),
            subtitle: Text(
              _getLanguageName(languageProvider.currentLanguage),
              style: const TextStyle(color: AppColors.textSecondary),
            ),
            trailing: const Icon(
              Icons.chevron_right,
              color: AppColors.onBackground,
            ),
            onTap: () {
              // TODO: Show language selection dialog
            },
          ),
          const Divider(),

          // Emergency Contacts
          ListTile(
            leading: const Icon(Icons.emergency, color: AppColors.onBackground),
            title: const Text(
              'Emergency Contacts',
              style: TextStyle(color: AppColors.onBackground),
            ),
            trailing: const Icon(
              Icons.chevron_right,
              color: AppColors.onBackground,
            ),
            onTap: () {
              // TODO: Navigate to emergency contacts screen
            },
          ),
          const Divider(),

          // AI Food Recognition
          ListTile(
            leading: const Icon(
              Icons.auto_awesome,
              color: AppColors.onBackground,
            ),
            title: const Text(
              'AI Food Recognition',
              style: TextStyle(color: AppColors.onBackground),
            ),
            subtitle: const Text(
              'Explore AI-powered food identification features',
              style: TextStyle(color: AppColors.textSecondary),
            ),
            trailing: const Icon(
              Icons.chevron_right,
              color: AppColors.onBackground,
            ),
            onTap: () {
              Navigator.of(context).pushNamed('/enhanced-ai-demo');
            },
          ),

          // AI Recognition Settings
          ListTile(
            leading: const Icon(Icons.tune, color: AppColors.onBackground),
            title: const Text(
              'AI Recognition Settings',
              style: TextStyle(color: AppColors.onBackground),
            ),
            subtitle: const Text(
              'Adjust confidence thresholds and AI preferences',
              style: TextStyle(color: AppColors.textSecondary),
            ),
            trailing: const Icon(
              Icons.chevron_right,
              color: AppColors.onBackground,
            ),
            onTap: () {
              Navigator.of(context).pushNamed('/ai-settings');
            },
          ),
          const Divider(),

          // POPIA Consent (Only show when online)
          if (!authProvider.isOfflineMode) ...[
            ListTile(
              leading: const Icon(Icons.shield, color: AppColors.onBackground),
              title: const Text(
                'POPIA Consent',
                style: TextStyle(color: AppColors.onBackground),
              ),
              subtitle: Text(
                authProvider.hasPopiaConsent
                    ? 'Consented on ${authProvider.popiaConsentTimestamp?.toLocal().toString().split(' ')[0]}'
                    : 'Not consented',
                style: const TextStyle(color: AppColors.textSecondary),
              ),
              trailing: const Icon(
                Icons.chevron_right,
                color: AppColors.onBackground,
              ),
              onTap: () {
                // TODO: Show POPIA consent details
              },
            ),
            const Divider(),
          ],

          // Sync Settings
          ListTile(
            leading: const Icon(Icons.sync, color: AppColors.onBackground),
            title: const Text(
              'Sync Settings',
              style: TextStyle(color: AppColors.onBackground),
            ),
            subtitle: Text(
              authProvider.isOfflineMode
                  ? 'Currently offline'
                  : 'Online sync enabled',
              style: const TextStyle(color: AppColors.textSecondary),
            ),
            trailing: Switch(
              value: !authProvider.isOfflineMode,
              onChanged: (value) async {
                await authProvider.setOfflineMode(!value);
              },
            ),
          ),
          const Divider(),

          // Help & Support
          ListTile(
            leading: const Icon(Icons.help, color: AppColors.onBackground),
            title: const Text(
              'Help & Support',
              style: TextStyle(color: AppColors.onBackground),
            ),
            trailing: const Icon(
              Icons.chevron_right,
              color: AppColors.onBackground,
            ),
            onTap: () {
              // TODO: Navigate to help screen
            },
          ),
          const Divider(),

          // Logout
          ListTile(
            leading: const Icon(Icons.logout, color: Colors.red),
            title: const Text('Logout', style: TextStyle(color: Colors.red)),
            onTap: () async {
              await authProvider.logout();
              if (context.mounted) {
                Navigator.of(context).pushAndRemoveUntil(
                  MaterialPageRoute(builder: (_) => const WelcomeScreen()),
                  (route) => false,
                );
              }
            },
          ),
        ],
      ),
    );
  }
}
