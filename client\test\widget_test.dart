// This is a basic Flutter widget test for GlucoMonitor app.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';

import 'package:glucomonitor/main.dart';
import 'package:glucomonitor/providers/language_provider.dart';
import 'package:glucomonitor/providers/auth_provider.dart';
import 'package:glucomonitor/services/connectivity_service.dart';

void main() {
  testWidgets('GlucoMonitor app smoke test', (WidgetTester tester) async {
    // Create mock services for testing
    final connectivityService = ConnectivityService();

    // Build our app with required providers
    await tester.pumpWidget(
      MultiProvider(
        providers: [
          ChangeNotifierProvider(create: (_) => LanguageProvider()),
          ChangeNotifierProvider(create: (_) => AuthProvider()),
          Provider.value(value: connectivityService),
        ],
        child: const MyApp(),
      ),
    );

    // Verify that the app loads without crashing
    expect(find.byType(MyApp), findsOneWidget);

    // Allow the app to settle
    await tester.pumpAndSettle();

    // Verify that we can find some basic UI elements
    // (This will depend on what's shown on the welcome/initial screen)
    expect(find.byType(MaterialApp), findsOneWidget);
  });
}
