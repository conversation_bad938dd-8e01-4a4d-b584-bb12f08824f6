import mongoose from 'mongoose';
import FoodDatabase from '../models/FoodDatabase';
import { FoodCategory, GlycemicIndex } from '../models/FoodEntry';
import { env } from '../config/env';
import { southAfricanFoodsData } from '../data/southAfricanFoodsData';
import { internationalFoodsData } from '../data/internationalFoodsData';

/**
 * Enhanced Food Database Seeder
 * Populates the database with comprehensive South African and international foods
 * with multilingual support and detailed nutrition data
 */

export async function seedEnhancedFoodDatabase(): Promise<void> {
    try {
        console.log('🌱 Starting enhanced food database seeding...');

        // Connect to database if not already connected
        if (mongoose.connection.readyState === 0) {
            await mongoose.connect(env.MONGODB_URI);
            console.log('📦 Connected to MongoDB for seeding');
        }

        // Check if foods already exist to avoid duplicates
        const existingFoodsCount = await FoodDatabase.countDocuments({ isVerified: true });
        
        if (existingFoodsCount > 0) {
            console.log(`📊 Found ${existingFoodsCount} existing verified foods in database`);
            console.log('⚠️  Database already contains food data. Use --force flag to re-seed.');
            
            // Check if force flag is provided
            const forceReseed = process.argv.includes('--force');
            if (!forceReseed) {
                console.log('💡 To re-seed the database, run: npm run seed:foods -- --force');
                return;
            }
            
            console.log('🗑️  Force flag detected. Clearing existing food database...');
            await FoodDatabase.deleteMany({ isVerified: true });
            console.log('✅ Cleared existing verified food entries');
        }

        // Combine all food data
        const allFoodsData = [
            ...southAfricanFoodsData,
            ...internationalFoodsData
        ];

        console.log(`📋 Preparing to seed ${allFoodsData.length} food items...`);
        console.log(`   - South African foods: ${southAfricanFoodsData.length}`);
        console.log(`   - International foods: ${internationalFoodsData.length}`);

        // Insert foods in batches for better performance
        const batchSize = 50;
        let insertedCount = 0;
        
        for (let i = 0; i < allFoodsData.length; i += batchSize) {
            const batch = allFoodsData.slice(i, i + batchSize);
            
            try {
                const insertedBatch = await FoodDatabase.insertMany(batch, { 
                    ordered: false // Continue inserting even if some fail
                });
                insertedCount += insertedBatch.length;
                console.log(`✅ Inserted batch ${Math.floor(i / batchSize) + 1}: ${insertedBatch.length} foods`);
            } catch (error: any) {
                console.warn(`⚠️  Some items in batch ${Math.floor(i / batchSize) + 1} failed to insert:`, error.message);
                // Count successful insertions even with some failures
                if (error.insertedDocs) {
                    insertedCount += error.insertedDocs.length;
                }
            }
        }

        console.log(`✅ Successfully seeded ${insertedCount} foods`);

        // Create comprehensive text indexes for multilingual search
        console.log('🔍 Creating search indexes...');
        
        try {
            // Drop existing text index if it exists
            await FoodDatabase.collection.dropIndex('name_text_brand_text_localNames.name_text_nameTranslations.english_text_nameTranslations.zulu_text_nameTranslations.afrikaans_text_nameTranslations.sesotho_text_nameTranslations.xhosa_text_description_text');
        } catch (error) {
            // Index might not exist, which is fine
        }

        // Create new comprehensive text index
        await FoodDatabase.collection.createIndex({ 
            name: 'text', 
            brand: 'text', 
            'localNames.name': 'text',
            'nameTranslations.english': 'text',
            'nameTranslations.zulu': 'text',
            'nameTranslations.afrikaans': 'text',
            'nameTranslations.sesotho': 'text',
            'nameTranslations.xhosa': 'text',
            'nameTranslations.sepedi': 'text',
            'nameTranslations.setswana': 'text',
            description: 'text' 
        }, {
            name: 'multilingual_search_index',
            default_language: 'english',
            language_override: 'language'
        });

        // Create additional performance indexes
        await FoodDatabase.collection.createIndex({ category: 1, glycemicIndex: 1 });
        await FoodDatabase.collection.createIndex({ isTraditionalSA: 1, category: 1 });
        await FoodDatabase.collection.createIndex({ origin: 1, availability: 1 });
        await FoodDatabase.collection.createIndex({ dietaryTags: 1 });
        await FoodDatabase.collection.createIndex({ carbohydratesPer100g: 1, glycemicIndex: 1 });
        await FoodDatabase.collection.createIndex({ proteinPer100g: -1 });
        await FoodDatabase.collection.createIndex({ caloriesPer100g: 1 });

        console.log('✅ Created search and performance indexes');

        // Generate comprehensive database statistics
        console.log('📈 Generating database statistics...');
        
        const stats = await generateDatabaseStats();
        console.log('📊 Enhanced Food Database Statistics:');
        console.log(`   Total Foods: ${stats.totalFoods}`);
        console.log(`   Traditional SA Foods: ${stats.traditionalSAFoods}`);
        console.log(`   International Foods: ${stats.internationalFoods}`);
        console.log('');
        console.log('📋 Foods by Category:');
        stats.categoryBreakdown.forEach(cat => {
            console.log(`   ${cat._id}: ${cat.count} items`);
        });
        console.log('');
        console.log('🌍 Foods by Origin:');
        stats.originBreakdown.forEach(origin => {
            console.log(`   ${origin._id}: ${origin.count} items`);
        });
        console.log('');
        console.log('🏷️  Foods by Dietary Tags:');
        stats.dietaryTagsBreakdown.forEach(tag => {
            console.log(`   ${tag._id}: ${tag.count} items`);
        });

        console.log('🎉 Enhanced food database seeding completed successfully!');
        console.log('💡 Available search endpoints:');
        console.log('   - GET /api/food-diary/search - Basic text search');
        console.log('   - GET /api/food-diary/search/multilingual - Multilingual search');
        console.log('   - GET /api/food-diary/search/nutrition - Advanced nutrition search');
        console.log('   - GET /api/food-diary/foods/origin/:origin - Search by origin');
        console.log('   - GET /api/food-diary/foods/dietary - Search by dietary tags');

    } catch (error) {
        console.error('❌ Error seeding enhanced food database:', error);
        throw error;
    }
}

async function generateDatabaseStats() {
    const totalFoods = await FoodDatabase.countDocuments({ isVerified: true });
    const traditionalSAFoods = await FoodDatabase.countDocuments({ isTraditionalSA: true, isVerified: true });
    const internationalFoods = await FoodDatabase.countDocuments({ isTraditionalSA: false, isVerified: true });

    const categoryBreakdown = await FoodDatabase.aggregate([
        { $match: { isVerified: true } },
        { $group: { _id: '$category', count: { $sum: 1 } } },
        { $sort: { count: -1 } }
    ]);

    const originBreakdown = await FoodDatabase.aggregate([
        { $match: { isVerified: true } },
        { $group: { _id: '$origin', count: { $sum: 1 } } },
        { $sort: { count: -1 } }
    ]);

    const dietaryTagsBreakdown = await FoodDatabase.aggregate([
        { $match: { isVerified: true, dietaryTags: { $exists: true, $ne: [] } } },
        { $unwind: '$dietaryTags' },
        { $group: { _id: '$dietaryTags', count: { $sum: 1 } } },
        { $sort: { count: -1 } }
    ]);

    return {
        totalFoods,
        traditionalSAFoods,
        internationalFoods,
        categoryBreakdown,
        originBreakdown,
        dietaryTagsBreakdown
    };
}

// Utility function to clear database
export async function clearFoodDatabase(): Promise<void> {
    try {
        console.log('🗑️  Clearing food database...');
        
        if (mongoose.connection.readyState === 0) {
            await mongoose.connect(env.MONGODB_URI);
        }

        const deletedCount = await FoodDatabase.deleteMany({});
        console.log(`✅ Cleared ${deletedCount.deletedCount} food entries from database`);
    } catch (error) {
        console.error('❌ Error clearing food database:', error);
        throw error;
    }
}

// Run seeder if called directly
if (require.main === module) {
    const command = process.argv[2];
    
    if (command === 'clear') {
        clearFoodDatabase()
            .then(() => {
                console.log('✅ Database clearing completed');
                process.exit(0);
            })
            .catch((error) => {
                console.error('❌ Database clearing failed:', error);
                process.exit(1);
            });
    } else {
        seedEnhancedFoodDatabase()
            .then(() => {
                console.log('✅ Enhanced seeding completed');
                process.exit(0);
            })
            .catch((error) => {
                console.error('❌ Enhanced seeding failed:', error);
                process.exit(1);
            });
    }
}
