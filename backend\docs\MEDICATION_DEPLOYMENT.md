# Medication Reminder System - Deployment Guide

## Overview
This guide covers the deployment and configuration of the Medication Reminder System for the GlucoMonitor backend application.

## Prerequisites

### System Requirements
- Node.js 18+ 
- MongoDB 5.0+
- Docker and Docker Compose (optional)
- SMS service provider account (SMSPortal or similar)

### Environment Variables
Create a `.env` file in the backend directory with the following variables:

```env
# Server Configuration
NODE_ENV=production
PORT=5000
HOST=0.0.0.0

# Database
MONGODB_URI=mongodb://localhost:27017/glucomonitor
MONGODB_TEST_URI=mongodb://localhost:27017/glucomonitor_test

# JWT Configuration
JWT_SECRET=your_super_secure_jwt_secret_key_here
JWT_EXPIRE=30d

# SMS Configuration (SMSPortal)
SMS_USERNAME=your_sms_username
SMS_PASSWORD=your_sms_password
SMS_API_URL=https://rest.smsportal.com/v1

# Notification Settings
NOTIFICATION_ENABLED=true
NOTIFICATION_ADVANCE_MINUTES=5
NOTIFICATION_RETRY_ATTEMPTS=3
NOTIFICATION_RETRY_INTERVAL=300000

# Development Settings (set to false in production)
DEVELOPMENT_MODE=false
```

## Installation

### 1. Clone and Install Dependencies
```bash
git clone <repository-url>
cd GlucoMonitor/backend
npm install
```

### 2. Database Setup
```bash
# Start MongoDB service
sudo systemctl start mongod

# Create database indexes (optional - will be created automatically)
npm run db:setup
```

### 3. Build Application
```bash
npm run build
```

### 4. Run Database Migrations (if any)
```bash
npm run migrate
```

## Deployment Options

### Option 1: Direct Node.js Deployment

#### 1. Install PM2 (Process Manager)
```bash
npm install -g pm2
```

#### 2. Create PM2 Ecosystem File
Create `ecosystem.config.js`:
```javascript
module.exports = {
  apps: [{
    name: 'glucomonitor-backend',
    script: 'dist/index.js',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 5000
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true
  }]
};
```

#### 3. Start Application
```bash
# Create logs directory
mkdir logs

# Start with PM2
pm2 start ecosystem.config.js

# Save PM2 configuration
pm2 save

# Setup PM2 startup script
pm2 startup
```

### Option 2: Docker Deployment

#### 1. Build Docker Image
```bash
docker build -t glucomonitor-backend .
```

#### 2. Run with Docker Compose
Create `docker-compose.prod.yml`:
```yaml
version: '3.8'

services:
  backend:
    build: .
    ports:
      - "5000:5000"
    environment:
      - NODE_ENV=production
      - MONGODB_URI=mongodb://mongo:27017/glucomonitor
    depends_on:
      - mongo
    restart: unless-stopped
    volumes:
      - ./logs:/app/logs

  mongo:
    image: mongo:5.0
    ports:
      - "27017:27017"
    volumes:
      - mongo_data:/data/db
    restart: unless-stopped

volumes:
  mongo_data:
```

#### 3. Deploy
```bash
docker-compose -f docker-compose.prod.yml up -d
```

## Medication System Configuration

### 1. Notification Service Setup
The medication notification service runs automatically when the server starts. Configure notification settings in your environment:

```env
# Enable/disable notifications
NOTIFICATION_ENABLED=true

# How many minutes before scheduled time to send reminder
NOTIFICATION_ADVANCE_MINUTES=5

# Retry configuration for failed notifications
NOTIFICATION_RETRY_ATTEMPTS=3
NOTIFICATION_RETRY_INTERVAL=300000  # 5 minutes in milliseconds
```

### 2. SMS Provider Configuration
Configure your SMS provider (SMSPortal example):

```env
SMS_USERNAME=your_username
SMS_PASSWORD=your_password
SMS_API_URL=https://rest.smsportal.com/v1
```

For other SMS providers, update the `sendSMS` function in `src/services/messaging.ts`.

### 3. Database Indexes
The system automatically creates the following indexes for optimal performance:

**Medication Collection:**
- `{ userId: 1, isActive: 1 }`
- `{ userId: 1, type: 1 }`
- `{ userId: 1, startDate: 1, endDate: 1 }`

**MedicationReminder Collection:**
- `{ userId: 1, scheduledTime: 1 }`
- `{ medicationId: 1, scheduledTime: 1 }`
- `{ userId: 1, status: 1 }`
- `{ scheduledTime: 1, status: 1 }` (for notification queries)

## Monitoring and Maintenance

### 1. Health Checks
The system provides health check endpoints:

```bash
# Basic health check
curl http://localhost:5000/api/health

# Database connectivity check
curl http://localhost:5000/api/health/db

# Medication system status
curl http://localhost:5000/api/health/medications
```

### 2. Logging
Application logs are structured and include:
- Request/response logs
- Database operation logs
- Notification service logs
- Error tracking

Log locations:
- PM2 deployment: `./logs/`
- Docker deployment: Container logs via `docker logs`

### 3. Monitoring Queries
Monitor system performance with these MongoDB queries:

```javascript
// Check medication reminder distribution
db.medicationreminders.aggregate([
  { $group: { _id: "$status", count: { $sum: 1 } } }
]);

// Check notification performance
db.medicationreminders.find({
  scheduledTime: { $gte: new Date(Date.now() - 24*60*60*1000) },
  status: "missed"
}).count();

// Check active medications per user
db.medications.aggregate([
  { $match: { isActive: true } },
  { $group: { _id: "$userId", count: { $sum: 1 } } }
]);
```

### 4. Backup Strategy
Implement regular database backups:

```bash
# Daily backup script
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
mongodump --uri="mongodb://localhost:27017/glucomonitor" --out="/backups/glucomonitor_$DATE"

# Compress backup
tar -czf "/backups/glucomonitor_$DATE.tar.gz" "/backups/glucomonitor_$DATE"
rm -rf "/backups/glucomonitor_$DATE"

# Keep only last 30 days of backups
find /backups -name "glucomonitor_*.tar.gz" -mtime +30 -delete
```

## Security Considerations

### 1. Environment Security
- Use strong JWT secrets (minimum 32 characters)
- Rotate JWT secrets regularly
- Store sensitive environment variables securely
- Use HTTPS in production

### 2. Database Security
- Enable MongoDB authentication
- Use connection string with credentials
- Implement database-level access controls
- Regular security updates

### 3. API Security
- Rate limiting is implemented
- Input validation on all endpoints
- Authentication required for all medication endpoints
- User data isolation enforced

## Troubleshooting

### Common Issues

#### 1. Notification Service Not Starting
```bash
# Check logs for notification service errors
pm2 logs glucomonitor-backend | grep "notification"

# Verify environment variables
echo $NOTIFICATION_ENABLED
echo $SMS_USERNAME
```

#### 2. Database Connection Issues
```bash
# Test MongoDB connection
mongo --eval "db.adminCommand('ismaster')"

# Check connection string format
echo $MONGODB_URI
```

#### 3. High Memory Usage
```bash
# Monitor memory usage
pm2 monit

# Restart if needed
pm2 restart glucomonitor-backend
```

### Performance Optimization

#### 1. Database Optimization
- Ensure proper indexes are created
- Monitor slow queries
- Implement connection pooling
- Regular database maintenance

#### 2. Application Optimization
- Use clustering with PM2
- Implement caching for frequently accessed data
- Optimize notification scheduling
- Monitor and profile performance

## Scaling Considerations

### Horizontal Scaling
- Use load balancer (nginx, HAProxy)
- Implement session-less authentication (JWT)
- Use MongoDB replica sets
- Consider microservices architecture

### Vertical Scaling
- Increase server resources
- Optimize database queries
- Implement caching layers
- Use CDN for static assets

## Support and Maintenance

### Regular Maintenance Tasks
1. Monitor system health and performance
2. Review and rotate security credentials
3. Update dependencies and security patches
4. Backup and test restore procedures
5. Review and optimize database performance
6. Monitor notification delivery rates
7. Clean up old reminder data (optional)

### Emergency Procedures
1. System outage response plan
2. Data recovery procedures
3. Notification service failover
4. Database corruption recovery
5. Security incident response

For additional support, refer to the API documentation and system logs.
