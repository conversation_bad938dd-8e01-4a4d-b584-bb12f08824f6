import { Response } from 'express';
import GlucoseReading from '../models/GlucoseReading';
import {
    AuthRequest,
    CreateGlucoseReadingRequestBody,
    UpdateGlucoseReadingRequestBody,
    GlucoseReadingQueryParams,
    IGlucoseReading
} from '../types';
import PDFDocument from 'pdfkit';
import ExcelJS from 'exceljs';
import nodemailer from 'nodemailer';
import { google } from 'googleapis';
import { Dropbox } from 'dropbox';
import fs from 'fs';
import path from 'path';

// @desc    Create new glucose reading
// @route   POST /api/glucose
// @access  Private
export const createGlucoseReading = async (req: AuthRequest, res: Response): Promise<void> => {
    try {
        const { value, timestamp, mealTiming, notes, tags }: CreateGlucoseReadingRequestBody = req.body;

        // Validate required fields
        if (!value) {
            res.status(400).json({
                success: false,
                message: 'Glucose value is required'
            });
            return;
        }

        // Validate glucose value range
        if (value < 20 || value > 600) {
            res.status(400).json({
                success: false,
                message: 'Glucose value must be between 20 and 600 mg/dL'
            });
            return;
        }

        const glucoseReading = await GlucoseReading.create({
            userId: req.user!._id,
            value,
            timestamp: timestamp || new Date(),
            mealTiming: mealTiming || 'other',
            notes: notes?.trim(),
            tags: tags?.map(tag => tag.trim()).filter(tag => tag.length > 0)
        });

        res.status(201).json({
            success: true,
            message: 'Glucose reading created successfully',
            data: glucoseReading
        });
    } catch (error: any) {
        console.error('Error creating glucose reading:', error);
        res.status(500).json({
            success: false,
            message: 'Server error',
            error: error.message
        });
    }
};

// @desc    Get glucose readings for user
// @route   GET /api/glucose
// @access  Private
export const getGlucoseReadings = async (req: AuthRequest, res: Response): Promise<void> => {
    try {
        const { 
            startDate, 
            endDate, 
            mealTiming, 
            limit = '50', 
            page = '1',
            sortBy = 'timestamp',
            sortOrder = 'desc'
        }: GlucoseReadingQueryParams = req.query;

        // Build query
        const query: any = { userId: req.user!._id };

        // Date range filter
        if (startDate || endDate) {
            query.timestamp = {};
            if (startDate) {
                query.timestamp.$gte = new Date(startDate);
            }
            if (endDate) {
                query.timestamp.$lte = new Date(endDate);
            }
        }

        // Meal timing filter
        if (mealTiming) {
            query.mealTiming = mealTiming;
        }

        // Pagination
        const limitNum = parseInt(limit);
        const pageNum = parseInt(page);
        const skip = (pageNum - 1) * limitNum;

        // Sort options
        const sortOptions: any = {};
        sortOptions[sortBy] = sortOrder === 'asc' ? 1 : -1;

        // Execute query
        const [readings, total] = await Promise.all([
            GlucoseReading.find(query)
                .sort(sortOptions)
                .limit(limitNum)
                .skip(skip)
                .lean(),
            GlucoseReading.countDocuments(query)
        ]);

        res.status(200).json({
            success: true,
            message: 'Glucose readings retrieved successfully',
            data: {
                readings,
                pagination: {
                    current: pageNum,
                    pages: Math.ceil(total / limitNum),
                    total,
                    limit: limitNum
                }
            }
        });
    } catch (error: any) {
        console.error('Error getting glucose readings:', error);
        res.status(500).json({
            success: false,
            message: 'Server error',
            error: error.message
        });
    }
};

// @desc    Get single glucose reading
// @route   GET /api/glucose/:id
// @access  Private
export const getGlucoseReading = async (req: AuthRequest, res: Response): Promise<void> => {
    try {
        const reading = await GlucoseReading.findOne({
            _id: req.params.id,
            userId: req.user!._id
        });

        if (!reading) {
            res.status(404).json({
                success: false,
                message: 'Glucose reading not found'
            });
            return;
        }

        res.status(200).json({
            success: true,
            message: 'Glucose reading retrieved successfully',
            data: reading
        });
    } catch (error: any) {
        console.error('Error getting glucose reading:', error);
        res.status(500).json({
            success: false,
            message: 'Server error',
            error: error.message
        });
    }
};

// @desc    Update glucose reading
// @route   PUT /api/glucose/:id
// @access  Private
export const updateGlucoseReading = async (req: AuthRequest, res: Response): Promise<void> => {
    try {
        const { value, timestamp, mealTiming, notes, tags }: UpdateGlucoseReadingRequestBody = req.body;

        // Validate glucose value if provided
        if (value !== undefined && (value < 20 || value > 600)) {
            res.status(400).json({
                success: false,
                message: 'Glucose value must be between 20 and 600 mg/dL'
            });
            return;
        }

        const updateData: any = {};
        if (value !== undefined) updateData.value = value;
        if (timestamp !== undefined) updateData.timestamp = timestamp;
        if (mealTiming !== undefined) updateData.mealTiming = mealTiming;
        if (notes !== undefined) updateData.notes = notes?.trim();
        if (tags !== undefined) {
            updateData.tags = tags.map(tag => tag.trim()).filter(tag => tag.length > 0);
        }

        const reading = await GlucoseReading.findOneAndUpdate(
            { _id: req.params.id, userId: req.user!._id },
            updateData,
            { new: true, runValidators: true }
        );

        if (!reading) {
            res.status(404).json({
                success: false,
                message: 'Glucose reading not found'
            });
            return;
        }

        res.status(200).json({
            success: true,
            message: 'Glucose reading updated successfully',
            data: reading
        });
    } catch (error: any) {
        console.error('Error updating glucose reading:', error);
        res.status(500).json({
            success: false,
            message: 'Server error',
            error: error.message
        });
    }
};

// @desc    Delete glucose reading
// @route   DELETE /api/glucose/:id
// @access  Private
export const deleteGlucoseReading = async (req: AuthRequest, res: Response): Promise<void> => {
    try {
        const reading = await GlucoseReading.findOneAndDelete({
            _id: req.params.id,
            userId: req.user!._id
        });

        if (!reading) {
            res.status(404).json({
                success: false,
                message: 'Glucose reading not found'
            });
            return;
        }

        res.status(200).json({
            success: true,
            message: 'Glucose reading deleted successfully'
        });
    } catch (error: any) {
        console.error('Error deleting glucose reading:', error);
        res.status(500).json({
            success: false,
            message: 'Server error',
            error: error.message
        });
    }
};

// @desc    Get glucose statistics
// @route   GET /api/glucose/stats
// @access  Private
export const getGlucoseStats = async (req: AuthRequest, res: Response): Promise<void> => {
    try {
        const { startDate, endDate } = req.query;

        // Build date filter
        const dateFilter: any = { userId: req.user!._id };
        if (startDate || endDate) {
            dateFilter.timestamp = {};
            if (startDate) {
                dateFilter.timestamp.$gte = new Date(startDate as string);
            }
            if (endDate) {
                dateFilter.timestamp.$lte = new Date(endDate as string);
            }
        }

        // Get statistics using aggregation
        const stats = await GlucoseReading.aggregate([
            { $match: dateFilter },
            {
                $group: {
                    _id: null,
                    count: { $sum: 1 },
                    average: { $avg: '$value' },
                    min: { $min: '$value' },
                    max: { $max: '$value' },
                    lowCount: {
                        $sum: { $cond: [{ $lt: ['$value', 70] }, 1, 0] }
                    },
                    normalCount: {
                        $sum: { $cond: [{ $and: [{ $gte: ['$value', 70] }, { $lte: ['$value', 140] }] }, 1, 0] }
                    },
                    highCount: {
                        $sum: { $cond: [{ $and: [{ $gt: ['$value', 140] }, { $lte: ['$value', 200] }] }, 1, 0] }
                    },
                    veryHighCount: {
                        $sum: { $cond: [{ $gt: ['$value', 200] }, 1, 0] }
                    }
                }
            }
        ]);

        const result = stats[0] || {
            count: 0,
            average: 0,
            min: 0,
            max: 0,
            lowCount: 0,
            normalCount: 0,
            highCount: 0,
            veryHighCount: 0
        };

        res.status(200).json({
            success: true,
            message: 'Glucose statistics retrieved successfully',
            data: result
        });
    } catch (error: any) {
        console.error('Error getting glucose statistics:', error);
        res.status(500).json({
            success: false,
            message: 'Server error',
            error: error.message
        });
    }
};

// Helper function to convert readings to CSV format
const convertToCSV = (readings: any[]): string => {
    if (readings.length === 0) return '';

    const headers = [
        'Date', 'Time', 'Value (mg/dL)', 'Category', 'Meal Timing',
        'Context', 'Notes', 'Tags', 'Symptoms', 'Location', 'Device ID',
        'Manual Entry', 'Confidence'
    ];

    const csvRows = [headers.join(',')];

    readings.forEach(reading => {
        const date = new Date(reading.timestamp);
        const row = [
            date.toISOString().split('T')[0], // Date
            date.toTimeString().split(' ')[0], // Time
            reading.value,
            reading.category || 'unknown',
            reading.mealTiming || '',
            reading.context || '',
            `"${(reading.notes || '').replace(/"/g, '""')}"`, // Escape quotes
            `"${(reading.tags || []).join(', ')}"`,
            `"${(reading.symptoms || []).join(', ')}"`,
            reading.location || '',
            reading.deviceId || '',
            reading.isManual ? 'Yes' : 'No',
            reading.confidence || ''
        ];
        csvRows.push(row.join(','));
    });

    return csvRows.join('\n');
};

// Helper function to format chart data based on chart type
const formatChartData = (readings: any[], chartType: string) => {
    const sortedReadings = readings.sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());

    switch (chartType) {
        case 'line':
        case 'area':
            return sortedReadings.map((reading, index) => ({
                x: index,
                y: reading.value,
                timestamp: reading.timestamp,
                category: reading.category || getGlucoseCategory(reading.value),
                mealTiming: reading.mealTiming
            }));

        case 'bar':
            return sortedReadings.map((reading, index) => ({
                x: index,
                y: reading.value,
                timestamp: reading.timestamp,
                category: reading.category || getGlucoseCategory(reading.value),
                color: getCategoryColor(reading.category || getGlucoseCategory(reading.value))
            }));

        case 'scatter':
            return sortedReadings.map((reading, index) => ({
                x: index,
                y: reading.value,
                timestamp: reading.timestamp,
                category: reading.category || getGlucoseCategory(reading.value),
                size: 4
            }));

        default:
            return sortedReadings.map((reading, index) => ({
                x: index,
                y: reading.value,
                timestamp: reading.timestamp
            }));
    }
};

// Helper function to get glucose category
const getGlucoseCategory = (value: number): string => {
    if (value < 70) return 'low';
    if (value <= 140) return 'normal';
    if (value <= 200) return 'high';
    return 'very_high';
};

// Helper function to get category color
const getCategoryColor = (category: string): string => {
    switch (category) {
        case 'low': return '#2196F3';
        case 'normal': return '#4CAF50';
        case 'high': return '#FF9800';
        case 'very_high': return '#F44336';
        default: return '#9E9E9E';
    }
};

// Helper function to convert chart data to CSV
const convertChartDataToCSV = (chartData: any[], chartType: string): string => {
    if (chartData.length === 0) return '';

    const headers = ['Index', 'Value', 'Timestamp', 'Category'];
    if (chartType === 'bar') headers.push('Color');
    if (chartType === 'scatter') headers.push('Size');

    const csvRows = [headers.join(',')];

    chartData.forEach(point => {
        const row = [
            point.x,
            point.y,
            new Date(point.timestamp).toISOString(),
            point.category
        ];

        if (chartType === 'bar' && point.color) row.push(point.color);
        if (chartType === 'scatter' && point.size) row.push(point.size);

        csvRows.push(row.join(','));
    });

    return csvRows.join('\n');
};

// Helper function to create PDF report
const createPDFReport = async (readings: any[], chartData: any[], chartType: string): Promise<Buffer> => {
    return new Promise((resolve, reject) => {
        try {
            const doc = new PDFDocument({ margin: 50 });
            const buffers: Buffer[] = [];

            doc.on('data', buffers.push.bind(buffers));
            doc.on('end', () => {
                const pdfData = Buffer.concat(buffers);
                resolve(pdfData);
            });

            // Header
            doc.fontSize(20).text('GlucoMonitor - Glucose Readings Report', { align: 'center' });
            doc.moveDown();

            // Summary statistics
            const values = readings.map(r => r.value);
            const average = values.reduce((sum, val) => sum + val, 0) / values.length;
            const min = Math.min(...values);
            const max = Math.max(...values);

            doc.fontSize(14).text('Summary Statistics:', { underline: true });
            doc.fontSize(12)
                .text(`Total Readings: ${readings.length}`)
                .text(`Average Glucose: ${average.toFixed(1)} mg/dL`)
                .text(`Minimum: ${min} mg/dL`)
                .text(`Maximum: ${max} mg/dL`)
                .text(`Date Range: ${new Date(readings[0]?.timestamp).toLocaleDateString()} - ${new Date(readings[readings.length - 1]?.timestamp).toLocaleDateString()}`);

            doc.moveDown();

            // Chart type info
            doc.fontSize(14).text(`Chart Type: ${chartType.charAt(0).toUpperCase() + chartType.slice(1)}`, { underline: true });
            doc.moveDown();

            // Recent readings table
            doc.fontSize(14).text('Recent Readings:', { underline: true });
            doc.fontSize(10);

            const tableTop = doc.y;
            const itemHeight = 20;

            // Table headers
            doc.text('Date', 50, tableTop);
            doc.text('Time', 120, tableTop);
            doc.text('Value', 180, tableTop);
            doc.text('Category', 230, tableTop);
            doc.text('Meal Timing', 300, tableTop);
            doc.text('Notes', 400, tableTop);

            // Table rows (last 20 readings)
            const recentReadings = readings.slice(-20);
            recentReadings.forEach((reading, index) => {
                const y = tableTop + (index + 1) * itemHeight;
                const date = new Date(reading.timestamp);

                doc.text(date.toLocaleDateString(), 50, y);
                doc.text(date.toLocaleTimeString(), 120, y);
                doc.text(`${reading.value} mg/dL`, 180, y);
                doc.text(getGlucoseCategory(reading.value), 230, y);
                doc.text(reading.mealTiming || 'N/A', 300, y);
                doc.text((reading.notes || '').substring(0, 20), 400, y);
            });

            doc.end();
        } catch (error) {
            reject(error);
        }
    });
};

// Helper function to create Excel file
const createExcelFile = async (readings: any[], chartData: any[], chartType: string): Promise<Buffer> => {
    const workbook = new ExcelJS.Workbook();

    // Summary sheet
    const summarySheet = workbook.addWorksheet('Summary');
    const values = readings.map(r => r.value);
    const average = values.reduce((sum, val) => sum + val, 0) / values.length;

    summarySheet.addRow(['GlucoMonitor - Glucose Readings Report']);
    summarySheet.addRow([]);
    summarySheet.addRow(['Total Readings', readings.length]);
    summarySheet.addRow(['Average Glucose', `${average.toFixed(1)} mg/dL`]);
    summarySheet.addRow(['Minimum', `${Math.min(...values)} mg/dL`]);
    summarySheet.addRow(['Maximum', `${Math.max(...values)} mg/dL`]);
    summarySheet.addRow(['Chart Type', chartType]);
    summarySheet.addRow(['Export Date', new Date().toISOString()]);

    // Raw data sheet
    const dataSheet = workbook.addWorksheet('Raw Data');
    dataSheet.addRow(['Date', 'Time', 'Value (mg/dL)', 'Category', 'Meal Timing', 'Context', 'Notes', 'Tags']);

    readings.forEach(reading => {
        const date = new Date(reading.timestamp);
        dataSheet.addRow([
            date.toLocaleDateString(),
            date.toLocaleTimeString(),
            reading.value,
            getGlucoseCategory(reading.value),
            reading.mealTiming || '',
            reading.context || '',
            reading.notes || '',
            (reading.tags || []).join(', ')
        ]);
    });

    // Chart data sheet
    const chartSheet = workbook.addWorksheet('Chart Data');
    const headers = ['Index', 'Value', 'Timestamp', 'Category'];
    if (chartType === 'bar') headers.push('Color');
    if (chartType === 'scatter') headers.push('Size');

    chartSheet.addRow(headers);
    chartData.forEach(point => {
        const row = [point.x, point.y, new Date(point.timestamp).toISOString(), point.category];
        if (chartType === 'bar' && point.color) row.push(point.color);
        if (chartType === 'scatter' && point.size) row.push(point.size);
        chartSheet.addRow(row);
    });

    return await workbook.xlsx.writeBuffer() as Buffer;
};

// Helper function to upload to Google Drive
const uploadToGoogleDrive = async (fileBuffer: Buffer, fileName: string, mimeType: string): Promise<string> => {
    try {
        // Support both file path and JSON content
        let authConfig: any = {
            scopes: ['https://www.googleapis.com/auth/drive.file']
        };

        if (process.env.GOOGLE_SERVICE_ACCOUNT_JSON) {
            // Option 2: JSON content as environment variable
            authConfig.credentials = JSON.parse(process.env.GOOGLE_SERVICE_ACCOUNT_JSON);
        } else if (process.env.GOOGLE_SERVICE_ACCOUNT_KEY) {
            // Option 1: File path
            authConfig.keyFile = process.env.GOOGLE_SERVICE_ACCOUNT_KEY;
        } else {
            throw new Error('Google Service Account credentials not configured');
        }

        const auth = new google.auth.GoogleAuth(authConfig);

        const drive = google.drive({ version: 'v3', auth });

        const fileMetadata = {
            name: fileName,
            parents: [process.env.GOOGLE_DRIVE_FOLDER_ID || 'root']
        };

        const media = {
            mimeType: mimeType,
            body: require('stream').Readable.from(fileBuffer)
        };

        const response = await drive.files.create({
            requestBody: fileMetadata,
            media: media,
            fields: 'id,webViewLink'
        });

        return response.data.webViewLink || `https://drive.google.com/file/d/${response.data.id}/view`;
    } catch (error) {
        console.error('Google Drive upload error:', error);
        throw new Error('Failed to upload to Google Drive');
    }
};

// Helper function to upload to Dropbox
const uploadToDropbox = async (fileBuffer: Buffer, fileName: string): Promise<string> => {
    try {
        const dbx = new Dropbox({
            accessToken: process.env.DROPBOX_ACCESS_TOKEN,
            fetch: require('node-fetch')
        });

        const response = await dbx.filesUpload({
            path: `/GlucoMonitor/${fileName}`,
            contents: fileBuffer,
            mode: { '.tag': 'overwrite' },
            autorename: true
        });

        const shareResponse = await dbx.sharingCreateSharedLinkWithSettings({
            path: response.result.path_display!
        });

        return shareResponse.result.url;
    } catch (error) {
        console.error('Dropbox upload error:', error);
        throw new Error('Failed to upload to Dropbox');
    }
};

// Helper function to send email with attachment
const sendEmailWithAttachment = async (
    userEmail: string,
    fileBuffer: Buffer,
    fileName: string,
    mimeType: string,
    chartType: string
): Promise<void> => {
    try {
        const transporter = nodemailer.createTransport({
            service: 'gmail',
            auth: {
                user: process.env.EMAIL_USER,
                pass: process.env.EMAIL_PASSWORD
            }
        });

        const mailOptions = {
            from: process.env.EMAIL_USER,
            to: userEmail,
            subject: `GlucoMonitor - Your ${chartType} Chart Export`,
            html: `
                <h2>GlucoMonitor Export</h2>
                <p>Hello,</p>
                <p>Your glucose readings export is ready! Please find your ${chartType} chart data attached.</p>
                <p>This export was generated on ${new Date().toLocaleDateString()} at ${new Date().toLocaleTimeString()}.</p>
                <p>Best regards,<br>GlucoMonitor Team</p>
            `,
            attachments: [
                {
                    filename: fileName,
                    content: fileBuffer,
                    contentType: mimeType
                }
            ]
        };

        await transporter.sendMail(mailOptions);
    } catch (error) {
        console.error('Email sending error:', error);
        throw new Error('Failed to send email');
    }
};

// Helper function to save file locally (for backup)
const saveFileLocally = async (fileBuffer: Buffer, fileName: string): Promise<string> => {
    try {
        const uploadsDir = path.join(process.cwd(), 'uploads', 'exports');

        // Create directory if it doesn't exist
        if (!fs.existsSync(uploadsDir)) {
            fs.mkdirSync(uploadsDir, { recursive: true });
        }

        const filePath = path.join(uploadsDir, fileName);
        fs.writeFileSync(filePath, fileBuffer);

        return filePath;
    } catch (error) {
        console.error('Local file save error:', error);
        throw new Error('Failed to save file locally');
    }
};

// @desc    Export chart data with multiple formats and destinations
// @route   GET /api/glucose/export/chart
// @access  Private
export const exportChartData = async (req: AuthRequest, res: Response): Promise<void> => {
    try {
        const {
            startDate,
            endDate,
            chartType = 'line',
            aggregation = 'none',
            format = 'json',
            destination = 'download',
            email,
            includeCharts = 'false'
        } = req.query;

        // Validate query parameters
        const validChartTypes = ['line', 'area', 'bar', 'scatter'];
        const validFormats = ['json', 'csv', 'pdf', 'excel'];
        const validDestinations = ['download', 'email', 'googledrive', 'dropbox', 'local'];

        if (chartType && !validChartTypes.includes(chartType as string)) {
            res.status(400).json({
                success: false,
                message: `Invalid chart type. Must be one of: ${validChartTypes.join(', ')}`
            });
            return;
        }

        if (format && !validFormats.includes(format as string)) {
            res.status(400).json({
                success: false,
                message: `Invalid format. Must be one of: ${validFormats.join(', ')}`
            });
            return;
        }

        if (destination && !validDestinations.includes(destination as string)) {
            res.status(400).json({
                success: false,
                message: `Invalid destination. Must be one of: ${validDestinations.join(', ')}`
            });
            return;
        }

        if (destination === 'email' && !email) {
            res.status(400).json({
                success: false,
                message: 'Email address is required when destination is email'
            });
            return;
        }

        // Build query
        const query: any = { userId: req.user!._id };

        // Date range filter
        if (startDate || endDate) {
            query.timestamp = {};
            if (startDate) {
                query.timestamp.$gte = new Date(startDate as string);
            }
            if (endDate) {
                query.timestamp.$lte = new Date(endDate as string);
            }
        }

        // Get readings
        const readings = await GlucoseReading.find(query)
            .sort({ timestamp: 1 })
            .lean();

        if (readings.length === 0) {
            res.status(200).json({
                success: true,
                message: 'No data available for export',
                data: {
                    chartType,
                    aggregation,
                    dataPoints: 0,
                    data: []
                }
            });
            return;
        }

        // Format data based on chart type
        const chartData = formatChartData(readings, chartType as string);

        // Generate filename with timestamp
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const baseFileName = `glucose_chart_${chartType}_${timestamp}`;

        let fileBuffer: Buffer;
        let fileName: string;
        let mimeType: string;

        // Generate file based on format
        switch (format) {
            case 'csv':
                const csvData = convertChartDataToCSV(chartData, chartType as string);
                fileBuffer = Buffer.from(csvData, 'utf8');
                fileName = `${baseFileName}.csv`;
                mimeType = 'text/csv';
                break;

            case 'pdf':
                fileBuffer = await createPDFReport(readings, chartData, chartType as string);
                fileName = `${baseFileName}.pdf`;
                mimeType = 'application/pdf';
                break;

            case 'excel':
                fileBuffer = await createExcelFile(readings, chartData, chartType as string);
                fileName = `${baseFileName}.xlsx`;
                mimeType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
                break;

            default: // json
                const jsonData = {
                    chartType,
                    aggregation,
                    dataPoints: chartData.length,
                    data: chartData,
                    metadata: {
                        dateRange: {
                            start: readings[0]?.timestamp,
                            end: readings[readings.length - 1]?.timestamp
                        },
                        totalReadings: readings.length,
                        exportedAt: new Date()
                    }
                };
                fileBuffer = Buffer.from(JSON.stringify(jsonData, null, 2), 'utf8');
                fileName = `${baseFileName}.json`;
                mimeType = 'application/json';
                break;
        }

        // Handle different destinations
        switch (destination) {
            case 'email':
                await sendEmailWithAttachment(
                    email as string,
                    fileBuffer,
                    fileName,
                    mimeType,
                    chartType as string
                );
                res.status(200).json({
                    success: true,
                    message: `Export sent to ${email} successfully`,
                    data: { fileName, format, destination }
                });
                break;

            case 'googledrive':
                const driveLink = await uploadToGoogleDrive(fileBuffer, fileName, mimeType);
                res.status(200).json({
                    success: true,
                    message: 'Export uploaded to Google Drive successfully',
                    data: { fileName, format, destination, link: driveLink }
                });
                break;

            case 'dropbox':
                const dropboxLink = await uploadToDropbox(fileBuffer, fileName);
                res.status(200).json({
                    success: true,
                    message: 'Export uploaded to Dropbox successfully',
                    data: { fileName, format, destination, link: dropboxLink }
                });
                break;

            case 'local':
                const localPath = await saveFileLocally(fileBuffer, fileName);
                res.status(200).json({
                    success: true,
                    message: 'Export saved locally successfully',
                    data: { fileName, format, destination, path: localPath }
                });
                break;

            default: // download
                res.setHeader('Content-Type', mimeType);
                res.setHeader('Content-Disposition', `attachment; filename=${fileName}`);
                res.status(200).send(fileBuffer);
                break;
        }
    } catch (error: any) {
        console.error('Error exporting chart data:', error);
        res.status(500).json({
            success: false,
            message: 'Server error',
            error: error.message
        });
    }
};

// @desc    Bulk export to multiple destinations
// @route   POST /api/glucose/export/bulk
// @access  Private
export const bulkExportChartData = async (req: AuthRequest, res: Response): Promise<void> => {
    try {
        const {
            startDate,
            endDate,
            chartType = 'line',
            formats = ['json'],
            destinations = ['download'],
            email,
            includeAllFormats = false
        } = req.body;

        // Validate inputs
        const validChartTypes = ['line', 'area', 'bar', 'scatter'];
        const validFormats = ['json', 'csv', 'pdf', 'excel'];
        const validDestinations = ['download', 'email', 'googledrive', 'dropbox', 'local'];

        if (!validChartTypes.includes(chartType)) {
            res.status(400).json({
                success: false,
                message: `Invalid chart type. Must be one of: ${validChartTypes.join(', ')}`
            });
            return;
        }

        const formatsToExport = includeAllFormats ? validFormats : formats;
        const destinationsToUse = destinations;

        // Build query
        const query: any = { userId: req.user!._id };
        if (startDate || endDate) {
            query.timestamp = {};
            if (startDate) query.timestamp.$gte = new Date(startDate);
            if (endDate) query.timestamp.$lte = new Date(endDate);
        }

        // Get readings
        const readings = await GlucoseReading.find(query).sort({ timestamp: 1 }).lean();
        if (readings.length === 0) {
            res.status(200).json({
                success: true,
                message: 'No data available for export',
                data: { exports: [] }
            });
            return;
        }

        const chartData = formatChartData(readings, chartType);
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const results: any[] = [];

        // Generate exports for each format and destination combination
        for (const format of formatsToExport) {
            for (const destination of destinationsToUse) {
                try {
                    const baseFileName = `glucose_chart_${chartType}_${format}_${timestamp}`;
                    let fileBuffer: Buffer;
                    let fileName: string;
                    let mimeType: string;

                    // Generate file based on format
                    switch (format) {
                        case 'csv':
                            const csvData = convertChartDataToCSV(chartData, chartType);
                            fileBuffer = Buffer.from(csvData, 'utf8');
                            fileName = `${baseFileName}.csv`;
                            mimeType = 'text/csv';
                            break;
                        case 'pdf':
                            fileBuffer = await createPDFReport(readings, chartData, chartType);
                            fileName = `${baseFileName}.pdf`;
                            mimeType = 'application/pdf';
                            break;
                        case 'excel':
                            fileBuffer = await createExcelFile(readings, chartData, chartType);
                            fileName = `${baseFileName}.xlsx`;
                            mimeType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
                            break;
                        default:
                            const jsonData = {
                                chartType, dataPoints: chartData.length, data: chartData,
                                metadata: {
                                    dateRange: { start: readings[0]?.timestamp, end: readings[readings.length - 1]?.timestamp },
                                    totalReadings: readings.length, exportedAt: new Date()
                                }
                            };
                            fileBuffer = Buffer.from(JSON.stringify(jsonData, null, 2), 'utf8');
                            fileName = `${baseFileName}.json`;
                            mimeType = 'application/json';
                            break;
                    }

                    // Handle destination
                    let result: any = { format, destination, fileName, status: 'success' };

                    switch (destination) {
                        case 'email':
                            if (email) {
                                await sendEmailWithAttachment(email, fileBuffer, fileName, mimeType, chartType);
                                result.message = `Sent to ${email}`;
                            } else {
                                result.status = 'skipped';
                                result.message = 'No email provided';
                            }
                            break;
                        case 'googledrive':
                            result.link = await uploadToGoogleDrive(fileBuffer, fileName, mimeType);
                            result.message = 'Uploaded to Google Drive';
                            break;
                        case 'dropbox':
                            result.link = await uploadToDropbox(fileBuffer, fileName);
                            result.message = 'Uploaded to Dropbox';
                            break;
                        case 'local':
                            result.path = await saveFileLocally(fileBuffer, fileName);
                            result.message = 'Saved locally';
                            break;
                        default:
                            result.message = 'Ready for download';
                            result.downloadData = fileBuffer.toString('base64');
                            break;
                    }

                    results.push(result);
                } catch (error: any) {
                    results.push({
                        format,
                        destination,
                        status: 'error',
                        message: error.message
                    });
                }
            }
        }

        res.status(200).json({
            success: true,
            message: 'Bulk export completed',
            data: {
                totalExports: results.length,
                successful: results.filter(r => r.status === 'success').length,
                failed: results.filter(r => r.status === 'error').length,
                exports: results
            }
        });

    } catch (error: any) {
        console.error('Error in bulk export:', error);
        res.status(500).json({
            success: false,
            message: 'Server error',
            error: error.message
        });
    }
};

// @desc    Get advanced analytics
// @route   GET /api/glucose/analytics
// @access  Private
export const getAdvancedAnalytics = async (req: AuthRequest, res: Response): Promise<void> => {
    try {
        const {
            startDate,
            endDate,
            period = 'week',
            groupBy = 'day'
        } = req.query;

        // Build query
        const query: any = { userId: req.user!._id };

        // Date range filter
        if (startDate || endDate) {
            query.timestamp = {};
            if (startDate) {
                query.timestamp.$gte = new Date(startDate as string);
            }
            if (endDate) {
                query.timestamp.$lte = new Date(endDate as string);
            }
        }

        // Get readings
        const readings = await GlucoseReading.find(query)
            .sort({ timestamp: 1 })
            .lean();

        if (readings.length === 0) {
            res.status(200).json({
                success: true,
                message: 'No data available for analytics',
                data: {
                    timeInRange: { low: 0, normal: 0, high: 0, veryHigh: 0 },
                    averageByTimeOfDay: {},
                    trends: [],
                    patterns: []
                }
            });
            return;
        }

        // Calculate time in range
        const timeInRange = {
            low: readings.filter(r => r.value < 70).length,
            normal: readings.filter(r => r.value >= 70 && r.value <= 140).length,
            high: readings.filter(r => r.value > 140 && r.value <= 200).length,
            veryHigh: readings.filter(r => r.value > 200).length
        };

        // Calculate average by time of day
        const timeGroups: { [key: string]: number[] } = {};
        readings.forEach(reading => {
            const hour = new Date(reading.timestamp).getHours();
            const timeKey = hour < 6 ? 'night' : hour < 12 ? 'morning' : hour < 18 ? 'afternoon' : 'evening';
            if (!timeGroups[timeKey]) timeGroups[timeKey] = [];
            timeGroups[timeKey].push(reading.value);
        });

        const averageByTimeOfDay: { [key: string]: number } = {};
        Object.keys(timeGroups).forEach(timeKey => {
            const values = timeGroups[timeKey];
            averageByTimeOfDay[timeKey] = values.reduce((sum, val) => sum + val, 0) / values.length;
        });

        res.status(200).json({
            success: true,
            message: 'Advanced analytics retrieved successfully',
            data: {
                timeInRange,
                averageByTimeOfDay,
                totalReadings: readings.length,
                dateRange: {
                    start: readings[0]?.timestamp,
                    end: readings[readings.length - 1]?.timestamp
                },
                generatedAt: new Date()
            }
        });
    } catch (error: any) {
        console.error('Error getting advanced analytics:', error);
        res.status(500).json({
            success: false,
            message: 'Server error',
            error: error.message
        });
    }
};

// @desc    Get glucose insights and recommendations
// @route   GET /api/glucose/insights
// @access  Private
export const getGlucoseInsights = async (req: AuthRequest, res: Response): Promise<void> => {
    try {
        const { startDate, endDate } = req.query;

        // Build query
        const query: any = { userId: req.user!._id };

        // Date range filter
        if (startDate || endDate) {
            query.timestamp = {};
            if (startDate) {
                query.timestamp.$gte = new Date(startDate as string);
            }
            if (endDate) {
                query.timestamp.$lte = new Date(endDate as string);
            }
        }

        // Get readings
        const readings = await GlucoseReading.find(query)
            .sort({ timestamp: 1 })
            .lean();

        if (readings.length === 0) {
            res.status(200).json({
                success: true,
                message: 'No data available for insights',
                data: {
                    averageByTimeOfDay: {},
                    patterns: [],
                    trends: [],
                    alerts: []
                }
            });
            return;
        }

        // Generate insights
        const averageByTimeOfDay: { [key: string]: number } = {};
        const timeGroups: { [key: string]: number[] } = {};

        readings.forEach(reading => {
            const hour = new Date(reading.timestamp).getHours();
            const timeKey = hour < 6 ? 'night' : hour < 12 ? 'morning' : hour < 18 ? 'afternoon' : 'evening';
            if (!timeGroups[timeKey]) timeGroups[timeKey] = [];
            timeGroups[timeKey].push(reading.value);
        });

        Object.keys(timeGroups).forEach(timeKey => {
            const values = timeGroups[timeKey];
            averageByTimeOfDay[timeKey] = values.reduce((sum, val) => sum + val, 0) / values.length;
        });

        // Generate patterns and alerts
        const patterns: string[] = [];
        const alerts: string[] = [];

        // Check for high morning readings
        if (averageByTimeOfDay.morning && averageByTimeOfDay.morning > 140) {
            patterns.push('High morning glucose levels detected');
            alerts.push('Consider reviewing your evening meal timing and composition');
        }

        // Check for frequent low readings
        const lowReadings = readings.filter(r => r.value < 70);
        if (lowReadings.length > readings.length * 0.15) {
            alerts.push('Frequent low glucose readings detected. Consult your healthcare provider');
        }

        // Check for high variability
        const values = readings.map(r => r.value);
        const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
        const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
        const standardDeviation = Math.sqrt(variance);

        if (standardDeviation > 50) {
            patterns.push('High glucose variability detected');
            alerts.push('Consider more consistent meal timing and portion sizes');
        }

        res.status(200).json({
            success: true,
            message: 'Glucose insights generated successfully',
            data: {
                averageByTimeOfDay,
                patterns,
                trends: [`Average glucose: ${mean.toFixed(1)} mg/dL`, `Standard deviation: ${standardDeviation.toFixed(1)} mg/dL`],
                alerts,
                totalReadings: readings.length,
                analysisDate: new Date()
            }
        });
    } catch (error: any) {
        console.error('Error getting glucose insights:', error);
        res.status(500).json({
            success: false,
            message: 'Server error',
            error: error.message
        });
    }
};
