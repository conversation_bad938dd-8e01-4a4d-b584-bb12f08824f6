import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../constants/app_colors.dart';

class GuardianContactForm extends StatefulWidget {
  final TextEditingController nameController;
  final TextEditingController phoneController;
  final TextEditingController emailController;
  final bool isRequired;

  const GuardianContactForm({
    super.key,
    required this.nameController,
    required this.phoneController,
    required this.emailController,
    this.isRequired = true,
  });

  @override
  State<GuardianContactForm> createState() => _GuardianContactFormState();
}

class _GuardianContactFormState extends State<GuardianContactForm> {
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppColors.surface,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: AppColors.onBackground.withValues(alpha: 0.2),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.family_restroom,
                    color: AppColors.onBackground,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Guardian Contact Information',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: AppColors.onBackground,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                widget.isRequired
                    ? 'Required for users under 18 years old'
                    : 'Optional guardian contact information',
                style: TextStyle(
                  fontSize: 12,
                  color: AppColors.onBackground.withValues(alpha: 0.7),
                ),
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: widget.nameController,
                style: const TextStyle(color: AppColors.onBackground),
                decoration: const InputDecoration(
                  labelText: 'Guardian Full Name',
                  labelStyle: TextStyle(color: AppColors.onBackground),
                  border: OutlineInputBorder(
                    borderSide: BorderSide(color: AppColors.onBackground),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: AppColors.onBackground),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: AppColors.onBackground),
                  ),
                  prefixIcon: Icon(Icons.person, color: AppColors.onBackground),
                ),
                textCapitalization: TextCapitalization.words,
                validator:
                    widget.isRequired
                        ? (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter guardian\'s name';
                          }
                          return null;
                        }
                        : null,
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: widget.phoneController,
                style: const TextStyle(color: AppColors.onBackground),
                decoration: InputDecoration(
                  labelText: 'Guardian Phone Number',
                  labelStyle: const TextStyle(color: AppColors.onBackground),
                  border: const OutlineInputBorder(
                    borderSide: BorderSide(color: AppColors.onBackground),
                  ),
                  enabledBorder: const OutlineInputBorder(
                    borderSide: BorderSide(color: AppColors.onBackground),
                  ),
                  focusedBorder: const OutlineInputBorder(
                    borderSide: BorderSide(color: AppColors.onBackground),
                  ),
                  prefixIcon: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 16,
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                          width: 24,
                          height: 16,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(2),
                            gradient: const LinearGradient(
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                              colors: [
                                Color(0xFF007A4D), // Green
                                Color(0xFFFFB612), // Gold
                                Color(0xFFDE3831), // Red
                              ],
                            ),
                          ),
                          child: const Center(
                            child: Text(
                              'ZA',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 8,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        const Text(
                          '+27',
                          style: TextStyle(
                            color: AppColors.onBackground,
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Container(
                          width: 1,
                          height: 20,
                          color: AppColors.onBackground.withValues(alpha: 0.3),
                        ),
                      ],
                    ),
                  ),
                  hintText: '60 123 4567',
                  hintStyle: TextStyle(
                    color: AppColors.onBackground.withValues(alpha: 0.5),
                  ),
                  helperText: 'Enter 9 digits after +27',
                  helperStyle: TextStyle(
                    color: AppColors.onBackground.withValues(alpha: 0.6),
                    fontSize: 12,
                  ),
                ),
                keyboardType: TextInputType.phone,
                inputFormatters: [
                  FilteringTextInputFormatter.digitsOnly,
                  LengthLimitingTextInputFormatter(9),
                  _PhoneNumberFormatter(),
                ],
                validator:
                    widget.isRequired
                        ? (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter guardian\'s phone number';
                          }
                          final digitsOnly = value.replaceAll(
                            RegExp(r'\D'),
                            '',
                          );
                          if (digitsOnly.length != 9) {
                            return 'Phone number must be 9 digits';
                          }
                          if (!RegExp(r'^[1-9]\d{8}$').hasMatch(digitsOnly)) {
                            return 'Phone number must start with 1-9';
                          }
                          return null;
                        }
                        : (value) {
                          if (value != null && value.isNotEmpty) {
                            final digitsOnly = value.replaceAll(
                              RegExp(r'\D'),
                              '',
                            );
                            if (digitsOnly.length != 9) {
                              return 'Phone number must be 9 digits';
                            }
                            if (!RegExp(r'^[1-9]\d{8}$').hasMatch(digitsOnly)) {
                              return 'Phone number must start with 1-9';
                            }
                          }
                          return null;
                        },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: widget.emailController,
                style: const TextStyle(color: AppColors.onBackground),
                decoration: const InputDecoration(
                  labelText: 'Guardian Email (Optional)',
                  labelStyle: TextStyle(color: AppColors.onBackground),
                  border: OutlineInputBorder(
                    borderSide: BorderSide(color: AppColors.onBackground),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: AppColors.onBackground),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: AppColors.onBackground),
                  ),
                  prefixIcon: Icon(Icons.email, color: AppColors.onBackground),
                ),
                keyboardType: TextInputType.emailAddress,
                validator: (value) {
                  if (value != null && value.isNotEmpty) {
                    if (!RegExp(
                      r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$',
                    ).hasMatch(value)) {
                      return 'Please enter a valid email address';
                    }
                  }
                  return null;
                },
              ),
            ],
          ),
        ),
      ],
    );
  }
}

class _PhoneNumberFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    final text = newValue.text;

    if (text.length <= 2) {
      return newValue;
    }

    String formatted = '';
    if (text.length >= 2) {
      formatted += text.substring(0, 2);
    }
    if (text.length >= 3) {
      formatted += ' ${text.substring(2, 3)}';
    }
    if (text.length >= 6) {
      formatted += '${text.substring(3, 6)} ${text.substring(6)}';
    } else if (text.length > 3) {
      formatted += text.substring(3);
    }

    return TextEditingValue(
      text: formatted,
      selection: TextSelection.collapsed(offset: formatted.length),
    );
  }
}
