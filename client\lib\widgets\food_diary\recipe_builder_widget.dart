import 'package:flutter/material.dart';
import '../../constants/app_colors.dart';
import '../../models/food_entry.dart';
import '../../models/recipe.dart';
import '../../services/recipe_service.dart';

class RecipeBuilderWidget extends StatefulWidget {
  final MealType mealType;
  final Function(FoodEntry) onFoodAdded;

  const RecipeBuilderWidget({
    super.key,
    required this.mealType,
    required this.onFoodAdded,
  });

  @override
  State<RecipeBuilderWidget> createState() => _RecipeBuilderWidgetState();
}

class _RecipeBuilderWidgetState extends State<RecipeBuilderWidget>
    with TickerProviderStateMixin {
  late TabController _tabController;
  List<Recipe> _userRecipes = [];
  List<Recipe> _templates = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadRecipes();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadRecipes() async {
    setState(() => _isLoading = true);
    try {
      final userRecipes = await RecipeService.getRecipes();
      final templates = RecipeService.getRecipeTemplates();

      setState(() {
        _userRecipes = userRecipes;
        _templates = templates;
      });
    } catch (e) {
      debugPrint('Error loading recipes: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Tab bar
        TabBar(
          controller: _tabController,
          labelColor: AppColors.primary,
          unselectedLabelColor: AppColors.textSecondary,
          indicatorColor: AppColors.primary,
          tabs: const [Tab(text: 'My Recipes'), Tab(text: 'Templates')],
        ),

        // Tab content
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [_buildMyRecipesTab(), _buildTemplatesTab()],
          ),
        ),
      ],
    );
  }

  Widget _buildMyRecipesTab() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_userRecipes.isEmpty) {
      return _buildEmptyState(
        icon: Icons.restaurant_menu,
        title: 'No Custom Recipes',
        message: 'Create your own recipes for quick meal logging',
        actionText: 'Create Recipe',
        onAction: _showCreateRecipeDialog,
      );
    }

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.restaurant_menu,
                color: AppColors.primary,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                '${_userRecipes.length} Custom Recipes',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: AppColors.onSurface,
                ),
              ),
              const Spacer(),
              IconButton(
                onPressed: _showCreateRecipeDialog,
                icon: const Icon(Icons.add, size: 20),
                tooltip: 'Create Recipe',
              ),
              IconButton(
                onPressed: _loadRecipes,
                icon: const Icon(Icons.refresh, size: 20),
                tooltip: 'Refresh',
              ),
            ],
          ),
          const SizedBox(height: 16),

          Expanded(
            child: ListView.builder(
              itemCount: _userRecipes.length,
              itemBuilder: (context, index) {
                final recipe = _userRecipes[index];
                return _buildRecipeTile(recipe, isTemplate: false);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTemplatesTab() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.library_books,
                color: AppColors.primary,
                size: 20,
              ),
              const SizedBox(width: 8),
              const Text(
                'Recipe Templates',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: AppColors.onSurface,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'Quick start templates for common recipes',
            style: TextStyle(
              color: AppColors.textSecondary.withValues(alpha: 0.8),
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 16),

          Expanded(
            child: ListView.builder(
              itemCount: _templates.length,
              itemBuilder: (context, index) {
                final template = _templates[index];
                return _buildRecipeTile(template, isTemplate: true);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecipeTile(Recipe recipe, {required bool isTemplate}) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CircleAvatar(
                  backgroundColor: AppColors.primary.withValues(alpha: 0.1),
                  child: Icon(
                    _getCategoryIcon(recipe.category),
                    color: AppColors.primary,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        recipe.name,
                        style: const TextStyle(
                          fontWeight: FontWeight.w600,
                          color: AppColors.onSurface,
                          fontSize: 16,
                        ),
                      ),
                      if (recipe.description != null)
                        Text(
                          recipe.description!,
                          style: const TextStyle(
                            color: AppColors.textSecondary,
                            fontSize: 14,
                          ),
                        ),
                    ],
                  ),
                ),
                if (isTemplate)
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: AppColors.primary.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Text(
                      'Template',
                      style: TextStyle(
                        color: AppColors.primary,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 12),

            // Recipe stats
            Row(
              children: [
                _buildStatChip(
                  icon: Icons.people,
                  label:
                      '${recipe.servings} serving${recipe.servings != 1 ? 's' : ''}',
                ),
                const SizedBox(width: 8),
                _buildStatChip(
                  icon: Icons.access_time,
                  label: '${recipe.totalTime} min',
                ),
                const SizedBox(width: 8),
                _buildStatChip(
                  icon: Icons.local_fire_department,
                  label: '${recipe.caloriesPerServing.toInt()} cal',
                ),
              ],
            ),
            const SizedBox(height: 12),

            // Nutrition summary
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.background,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildNutritionItem(
                    'Carbs',
                    '${recipe.carbsPerServing.toInt()}g',
                  ),
                  _buildNutritionItem(
                    'Protein',
                    '${recipe.proteinPerServing.toInt()}g',
                  ),
                  _buildNutritionItem(
                    'Fat',
                    '${recipe.fatPerServing.toInt()}g',
                  ),
                ],
              ),
            ),
            const SizedBox(height: 12),

            // Action buttons
            Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => _showRecipeDetails(recipe),
                    icon: const Icon(Icons.visibility, size: 16),
                    label: const Text('View'),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: AppColors.primary,
                      side: const BorderSide(color: AppColors.primary),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _addRecipeToMeal(recipe),
                    icon: const Icon(Icons.add, size: 16),
                    label: const Text('Add to Meal'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatChip({required IconData icon, required String label}) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: AppColors.textSecondary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: AppColors.textSecondary),
          const SizedBox(width: 4),
          Text(
            label,
            style: const TextStyle(
              color: AppColors.textSecondary,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNutritionItem(String label, String value) {
    return Column(
      children: [
        Text(
          value,
          style: const TextStyle(
            fontWeight: FontWeight.w600,
            color: AppColors.onSurface,
            fontSize: 16,
          ),
        ),
        Text(
          label,
          style: const TextStyle(color: AppColors.textSecondary, fontSize: 12),
        ),
      ],
    );
  }

  Widget _buildEmptyState({
    required IconData icon,
    required String title,
    required String message,
    required String actionText,
    required VoidCallback onAction,
  }) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 64,
              color: AppColors.textSecondary.withValues(alpha: 0.5),
            ),
            const SizedBox(height: 16),
            Text(
              title,
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.w600,
                color: AppColors.onSurface,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              message,
              style: TextStyle(
                fontSize: 16,
                color: AppColors.textSecondary.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: onAction,
              icon: const Icon(Icons.add),
              label: Text(actionText),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  IconData _getCategoryIcon(FoodCategory category) {
    switch (category) {
      case FoodCategory.fruits:
        return Icons.apple;
      case FoodCategory.vegetables:
        return Icons.eco;
      case FoodCategory.grains:
        return Icons.grain;
      case FoodCategory.proteins:
        return Icons.egg;
      case FoodCategory.dairy:
        return Icons.local_drink;
      case FoodCategory.beverages:
        return Icons.local_cafe;
      case FoodCategory.fats:
        return Icons.opacity;
      case FoodCategory.sweets:
        return Icons.cookie;
      case FoodCategory.other:
        return Icons.fastfood;
    }
  }

  void _showRecipeDetails(Recipe recipe) {
    // TODO: Implement recipe details dialog
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Recipe details for ${recipe.name}')),
    );
  }

  void _addRecipeToMeal(Recipe recipe) {
    // Convert recipe to food entry and add to meal
    final foodEntry = RecipeService.recipeToFoodEntry(
      recipe,
      mealType: widget.mealType,
      servings: 1.0,
    );

    widget.onFoodAdded(foodEntry);
  }

  void _showCreateRecipeDialog() {
    // TODO: Implement create recipe dialog
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Recipe creation coming soon!')),
    );
  }
}
