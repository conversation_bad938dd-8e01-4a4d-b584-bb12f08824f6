import { Response } from 'express';
import mongoose from 'mongoose';
import { AuthRequest } from '../middleware/auth';
import {
    NutritionAnalysisService,
    AnalysisType,
    INutritionAnalysisResult
} from '../services/nutritionAnalysisService';
import { catchAsync } from '../utils/errorHandler';
import logger from '../utils/logger';

/**
 * Perform comprehensive nutrition analysis
 */
export const performNutritionAnalysis = catchAsync(async (req: AuthRequest, res: Response): Promise<void> => {
    const userId = new mongoose.Types.ObjectId(req.user!._id as string);
    const {
        analysisType = AnalysisType.PERSONALIZED_RECOMMENDATIONS,
        timeframeDays = 30
    } = req.body;

    // Validate analysis type
    if (!Object.values(AnalysisType).includes(analysisType)) {
        res.status(400).json({
            success: false,
            message: 'Invalid analysis type'
        });
        return;
    }

    // Validate timeframe
    if (timeframeDays < 7 || timeframeDays > 365) {
        res.status(400).json({
            success: false,
            message: 'Timeframe must be between 7 and 365 days'
        });
        return;
    }

    // Perform analysis
    const analysisResult = await NutritionAnalysisService.performAnalysis(
        userId,
        analysisType,
        timeframeDays
    );

    logger.info('Nutrition analysis completed', {
        userId: userId.toString(),
        analysisType,
        timeframeDays,
        confidence: analysisResult.confidence,
        dataQuality: analysisResult.dataQuality.completeness
    });

    res.json({
        success: true,
        data: analysisResult,
        metadata: {
            analysisType,
            timeframeDays,
            generatedAt: new Date(),
            confidence: analysisResult.confidence
        }
    });
});

/**
 * Get meal impact predictions
 */
export const getMealImpactPredictions = catchAsync(async (req: AuthRequest, res: Response): Promise<void> => {
    const userId = new mongoose.Types.ObjectId(req.user!._id as string);
    const { timeframeDays = 14 } = req.query;

    const analysisResult = await NutritionAnalysisService.performAnalysis(
        userId,
        AnalysisType.MEAL_IMPACT_PREDICTION,
        Number(timeframeDays)
    );

    if (!analysisResult.mealImpactPredictions) {
        res.status(404).json({
            success: false,
            message: 'No meal impact data available for the specified timeframe'
        });
        return;
    }

    // Sort predictions by predicted impact (highest first)
    const sortedPredictions = analysisResult.mealImpactPredictions.sort((a, b) => 
        b.predictedBloodSugarPeak.value - a.predictedBloodSugarPeak.value
    );

    res.json({
        success: true,
        data: {
            predictions: sortedPredictions,
            summary: {
                totalMealsAnalyzed: sortedPredictions.length,
                averagePredictedPeak: sortedPredictions.reduce((sum, p) => 
                    sum + p.predictedBloodSugarPeak.value, 0) / sortedPredictions.length,
                highRiskMeals: sortedPredictions.filter(p => 
                    p.predictedBloodSugarPeak.value > 180).length,
                averageConfidence: sortedPredictions.reduce((sum, p) => 
                    sum + p.predictedBloodSugarPeak.confidence, 0) / sortedPredictions.length
            },
            timeframe: {
                days: Number(timeframeDays),
                start: analysisResult.timeframe.start,
                end: analysisResult.timeframe.end
            }
        }
    });
});

/**
 * Get nutrient deficiency analysis
 */
export const getNutrientDeficiencyAnalysis = catchAsync(async (req: AuthRequest, res: Response): Promise<void> => {
    const userId = new mongoose.Types.ObjectId(req.user!._id as string);
    const { timeframeDays = 30 } = req.query;

    const analysisResult = await NutritionAnalysisService.performAnalysis(
        userId,
        AnalysisType.NUTRIENT_DEFICIENCY,
        Number(timeframeDays)
    );

    if (!analysisResult.nutrientDeficiencies) {
        res.json({
            success: true,
            data: {
                deficiencies: [],
                summary: {
                    totalNutrientsAnalyzed: 0,
                    deficienciesFound: 0,
                    overallNutritionalStatus: 'insufficient_data'
                }
            }
        });
        return;
    }

    // Categorize deficiencies by severity
    const deficienciesBySeverity = {
        severe: analysisResult.nutrientDeficiencies.filter(d => d.deficiencyLevel === 'severe'),
        moderate: analysisResult.nutrientDeficiencies.filter(d => d.deficiencyLevel === 'moderate'),
        mild: analysisResult.nutrientDeficiencies.filter(d => d.deficiencyLevel === 'mild')
    };

    res.json({
        success: true,
        data: {
            deficiencies: analysisResult.nutrientDeficiencies,
            deficienciesBySeverity,
            summary: {
                totalNutrientsAnalyzed: 15, // Would be dynamic based on analysis
                deficienciesFound: analysisResult.nutrientDeficiencies.length,
                severeDeficiencies: deficienciesBySeverity.severe.length,
                moderateDeficiencies: deficienciesBySeverity.moderate.length,
                mildDeficiencies: deficienciesBySeverity.mild.length,
                overallNutritionalStatus: calculateNutritionalStatus(deficienciesBySeverity),
                priorityNutrients: deficienciesBySeverity.severe.concat(deficienciesBySeverity.moderate)
                    .map(d => d.nutrient).slice(0, 5)
            },
            recommendations: generateDeficiencyRecommendations(deficienciesBySeverity)
        }
    });
});

/**
 * Get blood sugar correlation analysis
 */
export const getBloodSugarCorrelations = catchAsync(async (req: AuthRequest, res: Response): Promise<void> => {
    const userId = new mongoose.Types.ObjectId(req.user!._id as string);
    const {
        timeframeDays = 30,
        minSampleSize = 3,
        sortBy = 'impact'
    } = req.query;

    const analysisResult = await NutritionAnalysisService.performAnalysis(
        userId,
        AnalysisType.BLOOD_SUGAR_CORRELATION,
        Number(timeframeDays)
    );

    if (!analysisResult.bloodSugarCorrelations) {
        res.json({
            success: true,
            data: {
                correlations: [],
                summary: {
                    totalFoodsAnalyzed: 0,
                    significantCorrelations: 0,
                    averageCorrelationStrength: 0
                }
            }
        });
        return;
    }

    // Filter by minimum sample size
    const filteredCorrelations = analysisResult.bloodSugarCorrelations.filter(c => 
        c.sampleSize >= Number(minSampleSize)
    );

    // Sort correlations
    const sortedCorrelations = sortCorrelations(filteredCorrelations, sortBy as string);

    // Categorize foods by impact
    const foodsByImpact = {
        highImpact: sortedCorrelations.filter(c => Math.abs(c.averageImpact) > 50),
        moderateImpact: sortedCorrelations.filter(c => Math.abs(c.averageImpact) > 20 && Math.abs(c.averageImpact) <= 50),
        lowImpact: sortedCorrelations.filter(c => Math.abs(c.averageImpact) <= 20)
    };

    res.json({
        success: true,
        data: {
            correlations: sortedCorrelations,
            foodsByImpact,
            summary: {
                totalFoodsAnalyzed: analysisResult.bloodSugarCorrelations.length,
                significantCorrelations: filteredCorrelations.length,
                averageCorrelationStrength: filteredCorrelations.reduce((sum, c) => 
                    sum + c.correlationStrength, 0) / filteredCorrelations.length,
                highImpactFoods: foodsByImpact.highImpact.length,
                problematicFoods: foodsByImpact.highImpact.filter(c => c.averageImpact > 0).length
            },
            insights: generateCorrelationInsights(foodsByImpact)
        }
    });
});

/**
 * Get dietary pattern analysis
 */
export const getDietaryPatternAnalysis = catchAsync(async (req: AuthRequest, res: Response): Promise<void> => {
    const userId = new mongoose.Types.ObjectId(req.user!._id as string);
    const { timeframeDays = 30 } = req.query;

    const analysisResult = await NutritionAnalysisService.performAnalysis(
        userId,
        AnalysisType.DIETARY_PATTERN,
        Number(timeframeDays)
    );

    if (!analysisResult.dietaryPatterns) {
        res.status(404).json({
            success: false,
            message: 'No dietary pattern data available'
        });
        return;
    }

    // Find the best matching pattern
    const bestPattern = analysisResult.dietaryPatterns.reduce((best, current) => 
        current.adherenceScore > best.adherenceScore ? current : best
    );

    res.json({
        success: true,
        data: {
            patterns: analysisResult.dietaryPatterns,
            bestMatch: bestPattern,
            summary: {
                totalPatternsAnalyzed: analysisResult.dietaryPatterns.length,
                bestPatternScore: bestPattern.adherenceScore,
                overallDietQuality: calculateDietQuality(analysisResult.dietaryPatterns),
                improvementPotential: calculateImprovementPotential(analysisResult.dietaryPatterns)
            },
            recommendations: generatePatternRecommendations(analysisResult.dietaryPatterns)
        }
    });
});

/**
 * Get metabolic health assessment
 */
export const getMetabolicHealthAssessment = catchAsync(async (req: AuthRequest, res: Response): Promise<void> => {
    const userId = new mongoose.Types.ObjectId(req.user!._id as string);
    const { timeframeDays = 30 } = req.query;

    const analysisResult = await NutritionAnalysisService.performAnalysis(
        userId,
        AnalysisType.METABOLIC_HEALTH,
        Number(timeframeDays)
    );

    if (!analysisResult.metabolicHealth) {
        res.status(404).json({
            success: false,
            message: 'Insufficient data for metabolic health assessment'
        });
        return;
    }

    const metabolicHealth = analysisResult.metabolicHealth;

    res.json({
        success: true,
        data: {
            assessment: metabolicHealth,
            healthScore: {
                overall: metabolicHealth.overallScore,
                bloodSugar: metabolicHealth.bloodSugarStability.score,
                nutrition: metabolicHealth.nutritionalBalance.score,
                mealTiming: metabolicHealth.mealTiming.score
            },
            riskAssessment: {
                riskLevel: calculateRiskLevel(metabolicHealth.overallScore),
                riskFactors: metabolicHealth.riskFactors,
                protectiveFactors: metabolicHealth.protectiveFactors,
                actionRequired: determineActionRequired(metabolicHealth)
            },
            trends: {
                // This would typically come from historical data
                scoreHistory: [65, 68, 72, 75, metabolicHealth.overallScore],
                improvementAreas: identifyImprovementAreas(metabolicHealth)
            }
        }
    });
});

/**
 * Get personalized recommendations
 */
export const getPersonalizedRecommendations = catchAsync(async (req: AuthRequest, res: Response): Promise<void> => {
    const userId = new mongoose.Types.ObjectId(req.user!._id as string);
    const {
        timeframeDays = 30,
        priority,
        category,
        limit = 10
    } = req.query;

    const analysisResult = await NutritionAnalysisService.performAnalysis(
        userId,
        AnalysisType.PERSONALIZED_RECOMMENDATIONS,
        Number(timeframeDays)
    );

    if (!analysisResult.personalizedRecommendations) {
        res.json({
            success: true,
            data: {
                recommendations: [],
                summary: {
                    totalRecommendations: 0,
                    highPriority: 0,
                    categories: []
                }
            }
        });
        return;
    }

    let recommendations = analysisResult.personalizedRecommendations;

    // Apply filters
    if (priority) {
        recommendations = recommendations.filter(r => r.priority === priority);
    }
    if (category) {
        recommendations = recommendations.filter(r => r.category === category);
    }

    // Limit results
    recommendations = recommendations.slice(0, Number(limit));

    // Group by category and priority
    const groupedRecommendations = {
        byPriority: {
            high: recommendations.filter(r => r.priority === 'high'),
            medium: recommendations.filter(r => r.priority === 'medium'),
            low: recommendations.filter(r => r.priority === 'low')
        },
        byCategory: recommendations.reduce((acc: any, rec) => {
            if (!acc[rec.category]) acc[rec.category] = [];
            acc[rec.category].push(rec);
            return acc;
        }, {})
    };

    res.json({
        success: true,
        data: {
            recommendations,
            groupedRecommendations,
            summary: {
                totalRecommendations: analysisResult.personalizedRecommendations.length,
                filteredRecommendations: recommendations.length,
                highPriority: groupedRecommendations.byPriority.high.length,
                mediumPriority: groupedRecommendations.byPriority.medium.length,
                lowPriority: groupedRecommendations.byPriority.low.length,
                categories: Object.keys(groupedRecommendations.byCategory),
                averageImplementationTime: calculateAverageImplementationTime(recommendations)
            }
        }
    });
});

/**
 * Generate comprehensive nutrition report
 */
export const generateNutritionReport = catchAsync(async (req: AuthRequest, res: Response): Promise<void> => {
    const userId = new mongoose.Types.ObjectId(req.user!._id as string);
    const { timeframeDays = 30, includeCharts = true } = req.body;

    // Perform all analyses
    const [
        mealImpact,
        deficiencies,
        correlations,
        patterns,
        metabolicHealth,
        recommendations
    ] = await Promise.all([
        NutritionAnalysisService.performAnalysis(userId, AnalysisType.MEAL_IMPACT_PREDICTION, timeframeDays),
        NutritionAnalysisService.performAnalysis(userId, AnalysisType.NUTRIENT_DEFICIENCY, timeframeDays),
        NutritionAnalysisService.performAnalysis(userId, AnalysisType.BLOOD_SUGAR_CORRELATION, timeframeDays),
        NutritionAnalysisService.performAnalysis(userId, AnalysisType.DIETARY_PATTERN, timeframeDays),
        NutritionAnalysisService.performAnalysis(userId, AnalysisType.METABOLIC_HEALTH, timeframeDays),
        NutritionAnalysisService.performAnalysis(userId, AnalysisType.PERSONALIZED_RECOMMENDATIONS, timeframeDays)
    ]);

    const comprehensiveReport = {
        reportId: `nutrition_report_${userId}_${Date.now()}`,
        generatedAt: new Date(),
        timeframe: {
            days: timeframeDays,
            start: mealImpact.timeframe.start,
            end: mealImpact.timeframe.end
        },
        overallScore: metabolicHealth.metabolicHealth?.overallScore || 0,
        dataQuality: {
            completeness: Math.max(
                mealImpact.dataQuality.completeness,
                deficiencies.dataQuality.completeness,
                correlations.dataQuality.completeness
            ),
            reliability: Math.max(
                mealImpact.dataQuality.reliability,
                deficiencies.dataQuality.reliability,
                correlations.dataQuality.reliability
            )
        },
        sections: {
            mealImpact: mealImpact.mealImpactPredictions,
            nutrientDeficiencies: deficiencies.nutrientDeficiencies,
            bloodSugarCorrelations: correlations.bloodSugarCorrelations,
            dietaryPatterns: patterns.dietaryPatterns,
            metabolicHealth: metabolicHealth.metabolicHealth,
            recommendations: recommendations.personalizedRecommendations
        },
        keyInsights: generateKeyInsights({
            mealImpact,
            deficiencies,
            correlations,
            patterns,
            metabolicHealth,
            recommendations
        }),
        actionPlan: generateActionPlan(recommendations.personalizedRecommendations || [])
    };

    logger.info('Comprehensive nutrition report generated', {
        userId: userId.toString(),
        timeframeDays,
        overallScore: comprehensiveReport.overallScore,
        dataQuality: comprehensiveReport.dataQuality.completeness
    });

    res.json({
        success: true,
        data: comprehensiveReport
    });
});

// Helper functions
function calculateNutritionalStatus(deficienciesBySeverity: any): string {
    if (deficienciesBySeverity.severe.length > 0) return 'poor';
    if (deficienciesBySeverity.moderate.length > 2) return 'needs_improvement';
    if (deficienciesBySeverity.mild.length > 3) return 'fair';
    return 'good';
}

function generateDeficiencyRecommendations(deficienciesBySeverity: any): string[] {
    const recommendations = [];

    if (deficienciesBySeverity.severe.length > 0) {
        recommendations.push('Consult with a healthcare provider about severe deficiencies');
        recommendations.push('Consider targeted supplementation');
    }

    if (deficienciesBySeverity.moderate.length > 0) {
        recommendations.push('Focus on nutrient-dense foods');
        recommendations.push('Plan meals to include deficient nutrients');
    }

    return recommendations;
}

function sortCorrelations(correlations: any[], sortBy: string): any[] {
    switch (sortBy) {
        case 'impact':
            return correlations.sort((a, b) => Math.abs(b.averageImpact) - Math.abs(a.averageImpact));
        case 'correlation':
            return correlations.sort((a, b) => b.correlationStrength - a.correlationStrength);
        case 'sample_size':
            return correlations.sort((a, b) => b.sampleSize - a.sampleSize);
        default:
            return correlations;
    }
}

function generateCorrelationInsights(foodsByImpact: any): string[] {
    const insights = [];

    if (foodsByImpact.highImpact.length > 0) {
        insights.push(`${foodsByImpact.highImpact.length} foods show high blood sugar impact`);
    }

    if (foodsByImpact.lowImpact.length > 0) {
        insights.push(`${foodsByImpact.lowImpact.length} foods are blood sugar friendly`);
    }

    return insights;
}

function calculateDietQuality(patterns: any[]): string {
    const averageScore = patterns.reduce((sum, p) => sum + p.adherenceScore, 0) / patterns.length;
    
    if (averageScore >= 80) return 'excellent';
    if (averageScore >= 60) return 'good';
    if (averageScore >= 40) return 'fair';
    return 'needs_improvement';
}

function calculateImprovementPotential(patterns: any[]): number {
    const bestScore = Math.max(...patterns.map(p => p.adherenceScore));
    const currentAverage = patterns.reduce((sum, p) => sum + p.adherenceScore, 0) / patterns.length;
    return Math.round(bestScore - currentAverage);
}

function generatePatternRecommendations(patterns: any[]): string[] {
    const recommendations = [];
    
    patterns.forEach(pattern => {
        if (pattern.adherenceScore < 60) {
            recommendations.push(`Improve ${pattern.patternType} adherence`);
        }
    });
    
    return recommendations;
}

function calculateRiskLevel(overallScore: number): string {
    if (overallScore >= 80) return 'low';
    if (overallScore >= 60) return 'moderate';
    if (overallScore >= 40) return 'high';
    return 'very_high';
}

function determineActionRequired(metabolicHealth: any): string[] {
    const actions = [];
    
    if (metabolicHealth.overallScore < 60) {
        actions.push('Immediate dietary changes recommended');
    }
    
    if (metabolicHealth.bloodSugarStability.score < 70) {
        actions.push('Focus on blood sugar management');
    }
    
    return actions;
}

function identifyImprovementAreas(metabolicHealth: any): string[] {
    const areas = [];

    if (metabolicHealth.bloodSugarStability.score < 70) {
        areas.push('Blood sugar stability');
    }

    if (metabolicHealth.nutritionalBalance.score < 70) {
        areas.push('Nutritional balance');
    }

    if (metabolicHealth.mealTiming.score < 70) {
        areas.push('Meal timing consistency');
    }

    return areas;
}

function calculateAverageImplementationTime(recommendations: any[]): string {
    // Simplified calculation
    return '2-4 weeks';
}

function generateKeyInsights(analyses: any): string[] {
    const insights = [];
    
    if (analyses.metabolicHealth.metabolicHealth?.overallScore) {
        insights.push(`Overall metabolic health score: ${analyses.metabolicHealth.metabolicHealth.overallScore}/100`);
    }
    
    if (analyses.deficiencies.nutrientDeficiencies?.length > 0) {
        insights.push(`${analyses.deficiencies.nutrientDeficiencies.length} nutrient deficiencies identified`);
    }
    
    return insights;
}

function generateActionPlan(recommendations: any[]): any {
    const highPriority = recommendations.filter(r => r.priority === 'high');

    return {
        immediate: highPriority.slice(0, 3),
        shortTerm: recommendations.filter(r => r.priority === 'medium').slice(0, 3),
        longTerm: recommendations.filter(r => r.priority === 'low').slice(0, 2)
    };
}
