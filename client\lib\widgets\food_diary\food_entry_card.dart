import 'package:flutter/material.dart';
import '../../constants/app_colors.dart';
import '../../models/food_entry.dart';

class FoodEntryCard extends StatelessWidget {
  final FoodEntry entry;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;

  const FoodEntryCard({
    super.key,
    required this.entry,
    this.onEdit,
    this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: entry.category.color.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: entry.category.color.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Food name and actions
          Row(
            children: [
              // Food category icon
              Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  color: entry.category.color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Icon(
                  entry.category.icon,
                  color: entry.category.color,
                  size: 16,
                ),
              ),
              const SizedBox(width: 12),

              // Food name and brand
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      entry.name,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: AppColors.onSurface,
                      ),
                    ),
                    if (entry.brand != null && entry.brand!.isNotEmpty) ...[
                      const SizedBox(height: 2),
                      Text(
                        entry.brand!,
                        style: const TextStyle(
                          fontSize: 12,
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ],
                ),
              ),

              // Action buttons
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (onEdit != null)
                    IconButton(
                      icon: const Icon(Icons.edit_outlined),
                      onPressed: onEdit,
                      iconSize: 20,
                      color: AppColors.textSecondary,
                      tooltip: 'Edit',
                    ),
                  if (onDelete != null)
                    IconButton(
                      icon: const Icon(Icons.delete_outline),
                      onPressed: onDelete,
                      iconSize: 20,
                      color: Colors.red,
                      tooltip: 'Delete',
                    ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 12),

          // Nutrition information
          Row(
            children: [
              // Carbohydrates (prominent for diabetes)
              Expanded(
                child: _buildNutritionInfo(
                  'Carbs',
                  '${entry.carbohydrates.toStringAsFixed(1)}g',
                  Colors.orange,
                  Icons.grain,
                ),
              ),
              const SizedBox(width: 12),

              // Calories
              Expanded(
                child: _buildNutritionInfo(
                  'Calories',
                  entry.calories.toStringAsFixed(0),
                  Colors.red,
                  Icons.local_fire_department,
                ),
              ),
              const SizedBox(width: 12),

              // Protein
              Expanded(
                child: _buildNutritionInfo(
                  'Protein',
                  '${entry.protein.toStringAsFixed(1)}g',
                  Colors.purple,
                  Icons.fitness_center,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),

          // Portion and additional info
          Row(
            children: [
              // Portion size
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: AppColors.textSecondary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  entry.portionDisplay,
                  style: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: AppColors.textSecondary,
                  ),
                ),
              ),
              const SizedBox(width: 8),

              // Glycemic Index indicator
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: entry.glycemicIndex.color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  entry.glycemicIndex.displayName,
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: entry.glycemicIndex.color,
                  ),
                ),
              ),
              const SizedBox(width: 8),

              // Blood sugar impact
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: entry.bloodSugarImpact.color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      entry.bloodSugarImpact.icon,
                      size: 12,
                      color: entry.bloodSugarImpact.color,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      entry.bloodSugarImpact.displayName.split(' ')[0],
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                        color: entry.bloodSugarImpact.color,
                      ),
                    ),
                  ],
                ),
              ),

              // Diabetes-friendly indicator
              if (entry.isDiabetesFriendly) ...[
                const SizedBox(width: 8),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.green.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.check_circle, size: 12, color: Colors.green),
                      SizedBox(width: 4),
                      Text(
                        'Diabetes-friendly',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                          color: Colors.green,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ),

          // Notes (if any)
          if (entry.notes != null && entry.notes!.isNotEmpty) ...[
            const SizedBox(height: 12),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.textSecondary.withValues(alpha: 0.05),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                entry.notes!,
                style: const TextStyle(
                  fontSize: 14,
                  color: AppColors.textSecondary,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildNutritionInfo(
    String label,
    String value,
    Color color,
    IconData icon,
  ) {
    return Column(
      children: [
        Icon(icon, color: color, size: 16),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: const TextStyle(fontSize: 12, color: AppColors.textSecondary),
        ),
      ],
    );
  }
}
