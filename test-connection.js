const http = require('http');

console.log('Testing connection to backend server...');

const options = {
  hostname: 'localhost',
  port: 3001,
  path: '/api/health',
  method: 'GET',
  headers: {
    'Content-Type': 'application/json'
  }
};

const req = http.request(options, (res) => {
  console.log(`Status: ${res.statusCode}`);
  console.log(`Headers: ${JSON.stringify(res.headers)}`);
  
  let data = '';
  res.on('data', (chunk) => {
    data += chunk;
  });
  
  res.on('end', () => {
    console.log('Response body:', data);
    console.log('✅ Connection successful!');
  });
});

req.on('error', (e) => {
  console.error(`❌ Connection failed: ${e.message}`);
});

req.setTimeout(5000, () => {
  console.error('❌ Connection timeout');
  req.destroy();
});

req.end();
