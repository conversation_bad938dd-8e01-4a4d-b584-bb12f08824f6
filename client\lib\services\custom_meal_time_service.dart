import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../models/food_entry.dart';
import '../services/backend_service.dart';

class CustomMealTimeService {
  static String get _baseUrl => '${BackendService.baseUrl}/custom-meal-times';

  // Get authentication token from shared preferences
  static Future<String?> _getAuthToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('backend_token');
  }

  /// Get all custom meal times for the current user
  static Future<List<CustomMealTime>> getCustomMealTimes() async {
    try {
      final token = await _getAuthToken();
      if (token == null) {
        throw Exception('No authentication token found');
      }

      final response = await http.get(
        Uri.parse(_baseUrl),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          final List<dynamic> mealTimesJson = data['data'];
          return mealTimesJson
              .map((json) => CustomMealTime.fromJson(json))
              .toList();
        } else {
          throw Exception(
            data['message'] ?? 'Failed to fetch custom meal times',
          );
        }
      } else {
        throw Exception('HTTP ${response.statusCode}: ${response.body}');
      }
    } catch (e) {
      throw Exception('Failed to fetch custom meal times: $e');
    }
  }

  /// Create a new custom meal time
  static Future<CustomMealTime> createCustomMealTime({
    required String name,
    required String displayName,
    required String icon,
    required String color,
    required String defaultTime,
    required MealTimeRange timeRange,
  }) async {
    try {
      final token = await _getAuthToken();
      if (token == null) {
        throw Exception('No authentication token found');
      }

      final requestBody = {
        'name': name,
        'displayName': displayName,
        'icon': icon,
        'color': color,
        'defaultTime': defaultTime,
        'timeRange': {'start': timeRange.start, 'end': timeRange.end},
      };

      final response = await http.post(
        Uri.parse(_baseUrl),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: json.encode(requestBody),
      );

      if (response.statusCode == 201) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          return CustomMealTime.fromJson(data['data']);
        } else {
          throw Exception(
            data['message'] ?? 'Failed to create custom meal time',
          );
        }
      } else {
        final errorData = json.decode(response.body);
        throw Exception(
          errorData['message'] ??
              'HTTP ${response.statusCode}: ${response.body}',
        );
      }
    } catch (e) {
      throw Exception('Failed to create custom meal time: $e');
    }
  }

  /// Update an existing custom meal time
  static Future<CustomMealTime> updateCustomMealTime({
    required String id,
    String? name,
    String? displayName,
    String? icon,
    String? color,
    String? defaultTime,
    MealTimeRange? timeRange,
    bool? isActive,
  }) async {
    try {
      final token = await _getAuthToken();
      if (token == null) {
        throw Exception('No authentication token found');
      }

      final requestBody = <String, dynamic>{};
      if (name != null) requestBody['name'] = name;
      if (displayName != null) requestBody['displayName'] = displayName;
      if (icon != null) requestBody['icon'] = icon;
      if (color != null) requestBody['color'] = color;
      if (defaultTime != null) requestBody['defaultTime'] = defaultTime;
      if (timeRange != null) {
        requestBody['timeRange'] = {
          'start': timeRange.start,
          'end': timeRange.end,
        };
      }
      if (isActive != null) requestBody['isActive'] = isActive;

      final response = await http.put(
        Uri.parse('$_baseUrl/$id'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: json.encode(requestBody),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          return CustomMealTime.fromJson(data['data']);
        } else {
          throw Exception(
            data['message'] ?? 'Failed to update custom meal time',
          );
        }
      } else {
        final errorData = json.decode(response.body);
        throw Exception(
          errorData['message'] ??
              'HTTP ${response.statusCode}: ${response.body}',
        );
      }
    } catch (e) {
      throw Exception('Failed to update custom meal time: $e');
    }
  }

  /// Delete a custom meal time
  static Future<void> deleteCustomMealTime(String id) async {
    try {
      final token = await _getAuthToken();
      if (token == null) {
        throw Exception('No authentication token found');
      }

      final response = await http.delete(
        Uri.parse('$_baseUrl/$id'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] != true) {
          throw Exception(
            data['message'] ?? 'Failed to delete custom meal time',
          );
        }
      } else {
        final errorData = json.decode(response.body);
        throw Exception(
          errorData['message'] ??
              'HTTP ${response.statusCode}: ${response.body}',
        );
      }
    } catch (e) {
      throw Exception('Failed to delete custom meal time: $e');
    }
  }

  /// Initialize default meal times for the user
  static Future<List<CustomMealTime>> initializeDefaultMealTimes() async {
    try {
      final token = await _getAuthToken();
      if (token == null) {
        throw Exception('No authentication token found');
      }

      final response = await http.post(
        Uri.parse('$_baseUrl/initialize-defaults'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 201) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          final List<dynamic> mealTimesJson = data['data'];
          return mealTimesJson
              .map((json) => CustomMealTime.fromJson(json))
              .toList();
        } else {
          throw Exception(
            data['message'] ?? 'Failed to initialize default meal times',
          );
        }
      } else {
        final errorData = json.decode(response.body);
        throw Exception(
          errorData['message'] ??
              'HTTP ${response.statusCode}: ${response.body}',
        );
      }
    } catch (e) {
      throw Exception('Failed to initialize default meal times: $e');
    }
  }

  /// Get meal time suggestions based on current time
  static MealType suggestMealTypeForCurrentTime() {
    final now = DateTime.now();
    final currentTime =
        '${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}';

    for (final mealType in MealType.values) {
      if (mealType == MealType.custom) continue;
      if (mealType.timeRange.isTimeInRange(currentTime)) {
        return mealType;
      }
    }

    // Default fallback based on hour
    final hour = now.hour;
    if (hour >= 5 && hour < 11) return MealType.breakfast;
    if (hour >= 11 && hour < 15) return MealType.lunch;
    if (hour >= 17 && hour < 22) return MealType.dinner;
    return MealType.eveningSnack;
  }

  /// Validate time format (HH:mm)
  static bool isValidTimeFormat(String time) {
    final regex = RegExp(r'^([01]?[0-9]|2[0-3]):[0-5][0-9]$');
    return regex.hasMatch(time);
  }

  /// Validate color format (#RRGGBB)
  static bool isValidColorFormat(String color) {
    final regex = RegExp(r'^#[0-9A-F]{6}$', caseSensitive: false);
    return regex.hasMatch(color);
  }

  /// Convert time string to minutes since midnight
  static int timeToMinutes(String time) {
    final parts = time.split(':');
    return int.parse(parts[0]) * 60 + int.parse(parts[1]);
  }

  /// Convert minutes since midnight to time string
  static String minutesToTime(int minutes) {
    final hours = (minutes ~/ 60) % 24;
    final mins = minutes % 60;
    return '${hours.toString().padLeft(2, '0')}:${mins.toString().padLeft(2, '0')}';
  }
}
