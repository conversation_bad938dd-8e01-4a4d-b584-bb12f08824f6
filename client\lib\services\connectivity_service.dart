import 'dart:async';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/foundation.dart';

class ConnectivityService {
  static final ConnectivityService _instance = ConnectivityService._internal();
  final _connectivity = Connectivity();
  final ValueNotifier<bool> isOnline = ValueNotifier<bool>(true);
  StreamSubscription<List<ConnectivityResult>>? _subscription;

  factory ConnectivityService() {
    return _instance;
  }

  ConnectivityService._internal();

  Future<void> init() async {
    // Set initial state
    isOnline.value = await _checkConnectivity();

    // Listen for connectivity changes
    _subscription = _connectivity.onConnectivityChanged.listen((results) async {
      // If we have any non-none connectivity result, we're online
      isOnline.value = results.any(
        (result) => result != ConnectivityResult.none,
      );
    });
  }

  Future<bool> _checkConnectivity() async {
    final results = await _connectivity.checkConnectivity();
    return results.any((result) => result != ConnectivityResult.none);
  }

  void dispose() {
    _subscription?.cancel();
    isOnline.dispose();
  }
}
