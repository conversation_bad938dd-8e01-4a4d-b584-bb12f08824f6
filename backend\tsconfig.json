{
  "compilerOptions": {
    // Target and Module Settings
    "target": "ES2022",
    "module": "commonjs",
    "lib": ["ES2022", "DOM"],
    "moduleResolution": "node",

    // Output Settings
    "outDir": "./dist",
    "rootDir": "./src",
    "declaration": true,
    "declarationMap": true,
    "sourceMap": true,
    "removeComments": true,
    "importHelpers": true,
    "noEmitOnError": false,

    // Strict Type Checking
    "strict": false,
    "noImplicitAny": false,
    "strictNullChecks": false,
    "strictFunctionTypes": false,
    "strictBindCallApply": false,
    "strictPropertyInitialization": false,
    "noImplicitThis": false,
    "alwaysStrict": true,

    // Additional Checks
    "noUnusedLocals": false,
    "noUnusedParameters": false,
    "exactOptionalPropertyTypes": false,
    "noImplicitReturns": false,
    "noFallthroughCasesInSwitch": false,
    "noUncheckedIndexedAccess": false,
    "noImplicitOverride": false,
    "noPropertyAccessFromIndexSignature": false,

    // Module Resolution
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "allowUmdGlobalAccess": false,

    // Advanced Options
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": false,
    "preserveConstEnums": true,

    // Experimental Features
    "experimentalDecorators": true,
    "emitDecoratorMetadata": true,

    // Path Mapping
    "baseUrl": "./src",
    "paths": {
      "@/*": ["*"],
      "@config/*": ["config/*"],
      "@controllers/*": ["controllers/*"],
      "@models/*": ["models/*"],
      "@services/*": ["services/*"],
      "@utils/*": ["utils/*"],
      "@middleware/*": ["middleware/*"],
      "@types/*": ["types/*"],
      "@routes/*": ["routes/*"]
    },

    // Type Roots
    "typeRoots": ["./node_modules/@types", "./src/types"],
    "types": ["node", "jest"],

    // Incremental Compilation
    "incremental": true,
    "tsBuildInfoFile": "./dist/.tsbuildinfo"
  },
  "include": [
    "src/**/*",
    "src/**/*.json"
  ],
  "exclude": [
    "node_modules",
    "dist",
    "**/*.test.ts",
    "**/*.spec.ts",
    "src/__tests__/**/*",
    "coverage",
    "*.config.js",
    "*.config.ts"
  ],
  "ts-node": {
    "esm": false,
    "experimentalSpecifierResolution": "node",
    "transpileOnly": true,
    "files": true,
    "compilerOptions": {
      "module": "commonjs",
      "target": "ES2020"
    }
  },
  "compileOnSave": false
}
