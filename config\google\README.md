# Google Service Account Configuration

## 📁 Directory Purpose
This directory contains the Google Service Account <PERSON><PERSON>N key file for Google Drive integration.

## 🔐 Security Notice
**IMPORTANT**: This directory contains sensitive credentials and should NEVER be committed to version control.

## 📋 Setup Instructions

1. **Download your Google Service Account <PERSON><PERSON><PERSON> key** from Google Cloud Console
2. **Rename it to**: `service-account-key.json`
3. **Place it in this directory**: `./config/google/service-account-key.json`
4. **Set proper permissions**:
   ```bash
   chmod 600 ./config/google/service-account-key.json
   ```

## 📂 Expected File Structure
```
config/
└── google/
    ├── README.md (this file)
    └── service-account-key.json (your key file)
```

## 🔒 Security Best Practices
- File permissions should be `600` (read/write for owner only)
- This directory is mounted read-only in Docker containers
- The JSON key file is automatically ignored by Git (see .gitignore)

## 📖 For detailed setup instructions, see:
`backend/docs/GOOGLE_DRIVE_SETUP.md`
