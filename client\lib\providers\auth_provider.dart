import 'package:shared_preferences/shared_preferences.dart';
import '../services/backend_service.dart';
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart';

class AuthProvider with ChangeNotifier {
  bool _isAuthenticated = false;
  bool _isOfflineMode = false;
  String? _phoneNumber;
  String? _name;
  String? _email;
  String? _ageGroup;
  String? _diabetesType;
  String? _language;
  String? _backendToken;
  String? _profilePicturePath;
  String? _guardianName;
  String? _guardianPhone;
  String? _guardianEmail;

  static const String _phoneKey = 'user_phone';
  static const String _nameKey = 'user_name';
  static const String _emailKey = 'user_email';
  static const String _ageGroupKey = 'user_age_group';
  static const String _diabetesTypeKey = 'user_diabetes_type';
  static const String _languageKey = 'user_language';
  static const String _offlineModeKey = 'offline_mode';
  static const String _popiaConsentKey = 'popia_consent';
  static const String _backendTokenKey = 'backend_token';
  static const String _profilePictureKey = 'profile_picture_path';
  static const String _guardianNameKey = 'guardian_name';
  static const String _guardianPhoneKey = 'guardian_phone';
  static const String _guardianEmailKey = 'guardian_email';
  static const String _profileDraftKey = 'profile_draft';
  String? _lastError;
  bool _isVerificationInProgress = false;
  DateTime? _popiaConsentTimestamp;

  bool get isAuthenticated => _isAuthenticated;
  bool get isOfflineMode => _isOfflineMode;
  String? get phoneNumber => _phoneNumber;
  String? get lastError => _lastError;
  String? get name => _name;
  String? get email => _email;
  String? get ageGroup => _ageGroup;
  String? get diabetesType => _diabetesType;
  bool get isVerificationInProgress => _isVerificationInProgress;
  DateTime? get popiaConsentTimestamp => _popiaConsentTimestamp;
  bool get hasPopiaConsent => _popiaConsentTimestamp != null;
  String? get language => _language;
  String? get backendToken => _backendToken;
  String? get profilePicturePath => _profilePicturePath;
  String? get guardianName => _guardianName;
  String? get guardianPhone => _guardianPhone;
  String? get guardianEmail => _guardianEmail;
  ValueNotifier<String?> get retryStatus => BackendService.retryStatus;

  // Phone number validation regex for South African numbers
  static final _phoneRegex = RegExp(r'^\+27[1-9]\d{8}$');

  AuthProvider() {
    _loadAuthState();
  }

  // Initialize Auth Provider
  Future<void> init() async {
    await _loadAuthState();
  }

  @override
  void dispose() {
    BackendService.retryStatus.dispose();
    super.dispose();
  }

  Future<void> _loadAuthState() async {
    final prefs = await SharedPreferences.getInstance();
    _phoneNumber = prefs.getString(_phoneKey);
    _name = prefs.getString(_nameKey);
    _email = prefs.getString(_emailKey);
    _ageGroup = prefs.getString(_ageGroupKey);
    _diabetesType = prefs.getString(_diabetesTypeKey);
    _language = prefs.getString(_languageKey);
    _backendToken = prefs.getString(_backendTokenKey);
    _profilePicturePath = prefs.getString(_profilePictureKey);
    _guardianName = prefs.getString(_guardianNameKey);
    _guardianPhone = prefs.getString(_guardianPhoneKey);
    _guardianEmail = prefs.getString(_guardianEmailKey);
    _isOfflineMode = prefs.getBool(_offlineModeKey) ?? false;

    final consentStr = prefs.getString(_popiaConsentKey);
    if (consentStr != null) {
      _popiaConsentTimestamp = DateTime.parse(consentStr);
    }

    // Check if user is authenticated with backend token
    if (_backendToken != null && _phoneNumber != null) {
      _isAuthenticated = true;
    }

    notifyListeners();
  }

  // Send OTP via backend
  Future<void> sendOTP(String phone) async {
    if (!_phoneRegex.hasMatch(phone)) {
      _lastError =
          'Invalid South African phone number. Please use format: +27XXXXXXXXX';
      throw _lastError!;
    }

    _isVerificationInProgress = true;
    _phoneNumber = phone;
    notifyListeners();

    try {
      await BackendService.sendOTP(phone, _language ?? 'en');
    } catch (e) {
      _isVerificationInProgress = false;
      notifyListeners();
      rethrow;
    }
  }

  // Verify OTP via backend
  Future<bool> verifyOTP(String phone, String otp) async {
    try {
      _lastError = null;
      debugPrint('Verifying OTP: $phone, $otp');

      final response = await http.post(
        Uri.parse('${BackendService.baseUrl}/auth/verify-otp'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({'phoneNumber': phone, 'otp': otp}),
      );

      debugPrint(
        'Verification response: ${response.statusCode} ${response.body}',
      );
      final data = jsonDecode(response.body);
      if (response.statusCode == 200 && data['success'] == true) {
        _backendToken = data['token'];
        _isAuthenticated = true;

        // Save the token and user data
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString(_backendTokenKey, _backendToken!);

        // Update user data if available in response
        if (data['user'] != null) {
          final user = data['user'];
          _name = user['name'];
          _ageGroup = user['ageGroup'];
          _diabetesType = user['diabetesType'];
          _language = user['language'];

          if (_name != null) {
            await prefs.setString(_nameKey, _name!);
          }
          if (_ageGroup != null) {
            await prefs.setString(_ageGroupKey, _ageGroup!);
          }
          if (_diabetesType != null) {
            await prefs.setString(_diabetesTypeKey, _diabetesType!);
          }
          if (_language != null) {
            await prefs.setString(_languageKey, _language!);
          }
        }

        notifyListeners();
        return true;
      }

      _lastError = data['message'] ?? 'Verification failed';
      debugPrint('Verification error: $_lastError');
      return false;
    } catch (e) {
      _lastError = e.toString();
      debugPrint('General verification error: $_lastError');
      return false;
    }
  }

  Future<bool> recordPopiaConsent() async {
    try {
      _popiaConsentTimestamp = DateTime.now();
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(
        _popiaConsentKey,
        _popiaConsentTimestamp!.toIso8601String(),
      );
      notifyListeners();
      return true;
    } catch (e) {
      _lastError = 'Failed to record POPIA consent: ${e.toString()}';
      return false;
    }
  }

  // Start the registration process
  Future<void> startRegistration(
    String phone,
    String name, {
    String? email,
    String? password,
  }) async {
    try {
      _lastError = null;
      _name = name;
      _email = email;
      await sendOTP(phone);

      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_nameKey, name);
      if (email != null) {
        await prefs.setString(_emailKey, email);
      }
      // Note: In a real app, you'd want to hash the password before storing
      // For now, we'll just store it temporarily for the registration process
    } catch (e) {
      _lastError = 'An unexpected error occurred: $e';
      throw _lastError!;
    }
  }

  // Send login OTP
  Future<void> sendLoginOTP(String phone) async {
    try {
      _lastError = null;
      await sendOTP(phone);
    } catch (e) {
      _lastError = 'An unexpected error occurred: $e';
      throw _lastError!;
    }
  }

  // Resend OTP
  Future<void> resendOTP(String phone) async {
    await sendOTP(phone);
  }

  // Register with email/password and send phone OTP
  Future<void> registerWithEmailPassword(
    String email,
    String password,
    String phoneNumber,
    String name,
  ) async {
    try {
      _lastError = null;

      final response = await http.post(
        Uri.parse('${BackendService.baseUrl}/auth/register'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'email': email,
          'password': password,
          'phoneNumber': phoneNumber,
          'name': name,
        }),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 201 && data['success'] == true) {
        // Store user data temporarily for OTP verification
        _name = name;
        _email = email;
        _phoneNumber = phoneNumber;

        final prefs = await SharedPreferences.getInstance();
        await prefs.setString(_nameKey, name);
        await prefs.setString(_emailKey, email);

        debugPrint('Registration successful, OTP sent to $phoneNumber');
      } else {
        // Enhanced error handling for TypeScript backend
        String errorMessage = data['message'] ?? 'Registration failed';

        // Handle validation errors if present
        if (data['errors'] != null && data['errors'] is List) {
          final errors = data['errors'] as List;
          if (errors.isNotEmpty) {
            errorMessage = errors
                .map((e) => e['msg'] ?? e.toString())
                .join(', ');
          }
        }

        throw errorMessage;
      }
    } catch (e) {
      _lastError = 'Registration failed: $e';
      throw _lastError!;
    }
  }

  // Login with email/password
  Future<void> loginWithEmailPassword(String email, String password) async {
    try {
      _lastError = null;

      final response = await http.post(
        Uri.parse('${BackendService.baseUrl}/auth/login'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({'email': email, 'password': password}),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200 && data['success'] == true) {
        _backendToken = data['token'];
        _isAuthenticated = true;

        // Store user data
        final user = data['user'];
        _name = user['name'];
        _email = user['email'];
        _phoneNumber = user['phoneNumber'];

        // Save to SharedPreferences
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString(_backendTokenKey, _backendToken!);
        await prefs.setString(_nameKey, _name!);
        await prefs.setString(_emailKey, _email!);

        debugPrint('Login successful for $email');
        notifyListeners();
      } else {
        // Enhanced error handling for TypeScript backend
        String errorMessage = data['message'] ?? 'Login failed';

        // Handle validation errors if present
        if (data['errors'] != null && data['errors'] is List) {
          final errors = data['errors'] as List;
          if (errors.isNotEmpty) {
            errorMessage = errors
                .map((e) => e['msg'] ?? e.toString())
                .join(', ');
          }
        }

        throw errorMessage;
      }
    } catch (e) {
      _lastError = 'Login failed: $e';
      throw _lastError!;
    }
  }

  // Update user profile with backend sync
  Future<bool> updateProfile({
    required String name,
    required String ageGroup,
    String? diabetesType,
    required bool givePopiaConsent,
    bool isOfflineMode = false,
    String? language,
    String? profilePicturePath,
    String? guardianName,
    String? guardianPhone,
    String? guardianEmail,
  }) async {
    try {
      debugPrint('Updating profile - Name: $name, AgeGroup: $ageGroup');
      _lastError = null;
      final prefs = await SharedPreferences.getInstance();

      // Update local storage
      _name = name;
      debugPrint('Set name in provider: $_name');
      _ageGroup = ageGroup;
      _diabetesType = diabetesType;
      if (language != null) _language = language;
      if (profilePicturePath != null) _profilePicturePath = profilePicturePath;
      if (guardianName != null) _guardianName = guardianName;
      if (guardianPhone != null) {
        // Format phone number to include +27 prefix
        final digitsOnly = guardianPhone.replaceAll(RegExp(r'\D'), '');
        _guardianPhone =
            digitsOnly.isNotEmpty ? '+27$digitsOnly' : guardianPhone;
      }
      if (guardianEmail != null) _guardianEmail = guardianEmail;

      await prefs.setString(_nameKey, name);
      debugPrint('Saved name to SharedPreferences');
      await prefs.setString(_ageGroupKey, ageGroup);
      if (diabetesType != null) {
        await prefs.setString(_diabetesTypeKey, diabetesType);
      }
      if (language != null) {
        await prefs.setString(_languageKey, language);
      }
      if (profilePicturePath != null) {
        await prefs.setString(_profilePictureKey, profilePicturePath);
      }
      if (guardianName != null) {
        await prefs.setString(_guardianNameKey, guardianName);
      }
      if (guardianPhone != null) {
        await prefs.setString(_guardianPhoneKey, _guardianPhone!);
      }
      if (guardianEmail != null) {
        await prefs.setString(_guardianEmailKey, guardianEmail);
      }

      // Update backend if authenticated
      if (_isAuthenticated && _backendToken != null && !isOfflineMode) {
        try {
          await _updateProfileOnBackend(
            name: name,
            ageGroup: ageGroup,
            diabetesType: diabetesType,
            language: language,
            givePopiaConsent: givePopiaConsent,
          );
          debugPrint('Profile updated on backend successfully');
        } catch (e) {
          debugPrint('Failed to update profile on backend: $e');
          // Don't fail the entire operation if backend update fails
          // The local update was successful
        }
      } else {
        debugPrint('Profile updated locally only');
      }

      notifyListeners();
      return true;
    } catch (e) {
      _lastError = e.toString();
      return false;
    }
  }

  // Update profile on backend
  Future<void> _updateProfileOnBackend({
    required String name,
    required String ageGroup,
    String? diabetesType,
    String? language,
    required bool givePopiaConsent,
  }) async {
    final response = await http.put(
      Uri.parse('${BackendService.baseUrl}/auth/profile'),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $_backendToken',
      },
      body: jsonEncode({
        'name': name,
        'ageGroup': ageGroup,
        'diabetesType': diabetesType,
        'language': language,
        'givePopiaConsent': givePopiaConsent,
      }),
    );

    final data = jsonDecode(response.body);

    if (response.statusCode == 200 && data['success'] == true) {
      // Update local data with backend response if available
      if (data['user'] != null) {
        final user = data['user'];
        _name = user['name'];
        _ageGroup = user['ageGroup'];
        _diabetesType = user['diabetesType'];
        _language = user['language'];

        // Update SharedPreferences with backend data
        final prefs = await SharedPreferences.getInstance();
        if (_name != null) await prefs.setString(_nameKey, _name!);
        if (_ageGroup != null) await prefs.setString(_ageGroupKey, _ageGroup!);
        if (_diabetesType != null) {
          await prefs.setString(_diabetesTypeKey, _diabetesType!);
        }
        if (_language != null) await prefs.setString(_languageKey, _language!);
      }
      debugPrint('Backend profile update successful');
    } else {
      throw data['message'] ?? 'Failed to update profile on backend';
    }
  }

  // Forgot password - send reset token via SMS
  Future<void> forgotPassword(String email) async {
    try {
      _lastError = null;

      final response = await http.post(
        Uri.parse('${BackendService.baseUrl}/auth/forgot-password'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({'email': email}),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200 && data['success'] == true) {
        debugPrint('Password reset token sent to phone number');
      } else {
        // Enhanced error handling for TypeScript backend
        String errorMessage = data['message'] ?? 'Failed to send reset token';

        // Handle validation errors if present
        if (data['errors'] != null && data['errors'] is List) {
          final errors = data['errors'] as List;
          if (errors.isNotEmpty) {
            errorMessage = errors
                .map((e) => e['msg'] ?? e.toString())
                .join(', ');
          }
        }

        throw errorMessage;
      }
    } catch (e) {
      _lastError = 'Failed to send reset token: $e';
      throw _lastError!;
    }
  }

  // Reset password using token
  Future<void> resetPassword(
    String email,
    String resetToken,
    String newPassword,
  ) async {
    try {
      _lastError = null;

      final response = await http.post(
        Uri.parse('${BackendService.baseUrl}/auth/reset-password'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'email': email,
          'resetToken': resetToken,
          'newPassword': newPassword,
        }),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200 && data['success'] == true) {
        debugPrint('Password reset successful');
      } else {
        // Enhanced error handling for TypeScript backend
        String errorMessage = data['message'] ?? 'Failed to reset password';

        // Handle validation errors if present
        if (data['errors'] != null && data['errors'] is List) {
          final errors = data['errors'] as List;
          if (errors.isNotEmpty) {
            errorMessage = errors
                .map((e) => e['msg'] ?? e.toString())
                .join(', ');
          }
        }

        throw errorMessage;
      }
    } catch (e) {
      _lastError = 'Failed to reset password: $e';
      throw _lastError!;
    }
  }

  // Get current user profile from backend
  Future<Map<String, dynamic>?> getCurrentUserProfile() async {
    if (!_isAuthenticated || _backendToken == null) {
      throw 'User not authenticated';
    }

    try {
      final response = await http.get(
        Uri.parse('${BackendService.baseUrl}/auth/me'),
        headers: {'Authorization': 'Bearer $_backendToken'},
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200 && data['success'] == true) {
        return data['user'];
      } else {
        throw data['message'] ?? 'Failed to get user profile';
      }
    } catch (e) {
      debugPrint('Failed to get user profile: $e');
      rethrow;
    }
  }

  // Clear authentication state on logout
  Future<void> logout() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_phoneKey);
      await prefs.remove(_nameKey);
      await prefs.remove(_emailKey);
      await prefs.remove(_ageGroupKey);
      await prefs.remove(_diabetesTypeKey);
      await prefs.remove(_offlineModeKey);
      await prefs.remove(_backendTokenKey);
      await prefs.remove(_profilePictureKey);
      await prefs.remove(_guardianNameKey);
      await prefs.remove(_guardianPhoneKey);
      await prefs.remove(_guardianEmailKey);
      await prefs.remove(_profileDraftKey);
      // Don't clear POPIA consent as it's permanent

      _phoneNumber = null;
      _name = null;
      _email = null;
      _ageGroup = null;
      _diabetesType = null;
      _backendToken = null;
      _profilePicturePath = null;
      _guardianName = null;
      _guardianPhone = null;
      _guardianEmail = null;
      _isAuthenticated = false;
      _isVerificationInProgress = false;
      _isOfflineMode = false;

      notifyListeners();
    } catch (e) {
      _lastError = 'Failed to logout: ${e.toString()}';
      throw _lastError!;
    }
  }

  Future<void> setOfflineMode(bool value) async {
    _isOfflineMode = value;
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_offlineModeKey, value);
    notifyListeners();
  }

  // Save profile draft for offline completion
  Future<void> saveProfileDraft({
    String? name,
    String? ageGroup,
    String? diabetesType,
    String? profilePicturePath,
    String? guardianName,
    String? guardianPhone,
    String? guardianEmail,
  }) async {
    final prefs = await SharedPreferences.getInstance();
    final draft = {
      'name': name,
      'ageGroup': ageGroup,
      'diabetesType': diabetesType,
      'profilePicturePath': profilePicturePath,
      'guardianName': guardianName,
      'guardianPhone': guardianPhone,
      'guardianEmail': guardianEmail,
      'timestamp': DateTime.now().toIso8601String(),
    };
    await prefs.setString(_profileDraftKey, jsonEncode(draft));
  }

  // Load profile draft
  Future<Map<String, dynamic>?> loadProfileDraft() async {
    final prefs = await SharedPreferences.getInstance();
    final draftStr = prefs.getString(_profileDraftKey);
    if (draftStr != null) {
      return jsonDecode(draftStr);
    }
    return null;
  }

  // Clear profile draft
  Future<void> clearProfileDraft() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_profileDraftKey);
  }

  // Sync profile draft when going online
  Future<void> syncProfileDraftIfExists() async {
    final draft = await loadProfileDraft();
    if (draft != null) {
      // Check if the draft has enough information to create a profile
      if (draft['name'] != null &&
          draft['ageGroup'] != null &&
          draft['diabetesType'] != null) {
        // Try to sync the profile
        final success = await updateProfile(
          name: draft['name'],
          ageGroup: draft['ageGroup'],
          diabetesType: draft['diabetesType'],
          givePopiaConsent: false, // Don't override existing consent
          profilePicturePath: draft['profilePicturePath'],
          guardianName: draft['guardianName'],
          guardianPhone: draft['guardianPhone'],
          guardianEmail: draft['guardianEmail'],
          isOfflineMode: false,
        );

        if (success) {
          await clearProfileDraft();
        }
      }
    }
  }
}
