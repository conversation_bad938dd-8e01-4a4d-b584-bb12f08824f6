class RetryConfig {
  final int maxAttempts;
  final Duration initialDelay;
  final double backoffMultiplier;
  final Duration maxDelay;

  const RetryConfig({
    this.maxAttempts = 3,
    this.initialDelay = const Duration(seconds: 1),
    this.backoffMultiplier = 2.0,
    this.maxDelay = const Duration(seconds: 10),
  });

  RetryConfig copyWith({
    int? maxAttempts,
    Duration? initialDelay,
    double? backoffMultiplier,
    Duration? maxDelay,
  }) {
    return RetryConfig(
      maxAttempts: maxAttempts ?? this.maxAttempts,
      initialDelay: initialDelay ?? this.initialDelay,
      backoffMultiplier: backoffMultiplier ?? this.backoffMultiplier,
      maxDelay: maxDelay ?? this.maxDelay,
    );
  }
}

class RetryException implements Exception {
  final String message;
  final int attempts;
  final dynamic lastError;

  RetryException(this.message, this.attempts, this.lastError);

  @override
  String toString() =>
      'RetryException: $message after $attempts attempts. Last error: $lastError';
}

Future<T> withRetry<T>({
  required Future<T> Function() operation,
  required String operationName,
  RetryConfig config = const RetryConfig(),
  bool Function(Exception)? shouldRetry,
}) async {
  Duration delay = config.initialDelay;
  Exception? lastError;

  for (int attempt = 1; attempt <= config.maxAttempts; attempt++) {
    try {
      return await operation();
    } catch (e) {
      lastError = e is Exception ? e : Exception(e.toString());

      if (attempt == config.maxAttempts ||
          (shouldRetry != null && !shouldRetry(lastError))) {
        throw RetryException(
          'Failed to execute $operationName',
          attempt,
          lastError,
        );
      }

      await Future.delayed(delay);
      delay = Duration(
        milliseconds: (delay.inMilliseconds * config.backoffMultiplier)
            .round()
            .clamp(0, config.maxDelay.inMilliseconds),
      );
    }
  }

  throw RetryException(
    'Failed to execute $operationName after exhausting all retries',
    config.maxAttempts,
    lastError,
  );
}
