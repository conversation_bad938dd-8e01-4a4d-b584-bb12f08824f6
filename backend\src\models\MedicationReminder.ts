import mongoose, { Document, Schema } from 'mongoose';

// Enums matching frontend
export enum ReminderStatus {
    PENDING = 'pending',
    TAKEN = 'taken',
    MISSED = 'missed',
    SKIPPED = 'skipped'
}

// MedicationReminder interface
export interface IMedicationReminder extends Document {
    userId: mongoose.Types.ObjectId;
    medicationId: mongoose.Types.ObjectId;
    scheduledTime: Date;
    actualTime?: Date;
    status: ReminderStatus;
    notes?: string;
    notificationSent: boolean;
    notificationId?: string;
    createdAt: Date;
    updatedAt: Date;

    // Virtual properties
    isOverdue: boolean;
    statusDisplay: string;
    statusColor: string;
    statusIcon: string;
}

// MedicationAdherence interface for statistics
export interface IMedicationAdherence {
    medicationId: mongoose.Types.ObjectId;
    medicationName: string;
    totalReminders: number;
    takenCount: number;
    missedCount: number;
    skippedCount: number;
    adherencePercentage: number;
    lastTaken?: Date;
    nextDue?: Date;
}

// MedicationStats interface for overall statistics
export interface IMedicationStats {
    totalMedications: number;
    activeMedications: number;
    totalRemindersToday: number;
    takenToday: number;
    missedToday: number;
    overallAdherence: number;
    medicationAdherence: IMedicationAdherence[];
}

const medicationReminderSchema = new Schema<IMedicationReminder>({
    userId: {
        type: Schema.Types.ObjectId,
        ref: 'User',
        required: [true, 'User ID is required'],
        index: true
    },
    medicationId: {
        type: Schema.Types.ObjectId,
        ref: 'Medication',
        required: [true, 'Medication ID is required'],
        index: true
    },
    scheduledTime: {
        type: Date,
        required: [true, 'Scheduled time is required'],
        index: true
    },
    actualTime: {
        type: Date
    },
    status: {
        type: String,
        enum: Object.values(ReminderStatus),
        default: ReminderStatus.PENDING,
        index: true
    },
    notes: {
        type: String,
        trim: true,
        maxlength: [200, 'Notes cannot exceed 200 characters']
    },
    notificationSent: {
        type: Boolean,
        default: false,
        index: true
    },
    notificationId: {
        type: String,
        trim: true
    }
}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
});

// Compound indexes for performance
medicationReminderSchema.index({ userId: 1, scheduledTime: 1 });
medicationReminderSchema.index({ userId: 1, status: 1 });
medicationReminderSchema.index({ medicationId: 1, scheduledTime: 1 });
medicationReminderSchema.index({ userId: 1, scheduledTime: 1, status: 1 });

// Virtual properties
medicationReminderSchema.virtual('isOverdue').get(function(this: IMedicationReminder) {
    if (this.status !== ReminderStatus.PENDING) {
        return false;
    }
    return new Date() > this.scheduledTime;
});

medicationReminderSchema.virtual('statusDisplay').get(function(this: IMedicationReminder) {
    const statusMap: Record<ReminderStatus, string> = {
        [ReminderStatus.PENDING]: this.isOverdue ? 'Overdue' : 'Pending',
        [ReminderStatus.TAKEN]: 'Taken',
        [ReminderStatus.MISSED]: 'Missed',
        [ReminderStatus.SKIPPED]: 'Skipped'
    };
    return statusMap[this.status];
});

medicationReminderSchema.virtual('statusColor').get(function(this: IMedicationReminder) {
    const colorMap: Record<string, string> = {
        'Pending': '#2196F3',
        'Overdue': '#FF5722',
        'Taken': '#4CAF50',
        'Missed': '#F44336',
        'Skipped': '#FF9800'
    };
    return colorMap[this.statusDisplay] || '#757575';
});

medicationReminderSchema.virtual('statusIcon').get(function(this: IMedicationReminder) {
    const iconMap: Record<string, string> = {
        'Pending': 'schedule',
        'Overdue': 'warning',
        'Taken': 'check_circle',
        'Missed': 'cancel',
        'Skipped': 'remove_circle'
    };
    return iconMap[this.statusDisplay] || 'help';
});

// Static methods for statistics
medicationReminderSchema.statics.getAdherenceStats = async function(
    userId: mongoose.Types.ObjectId,
    startDate?: Date,
    endDate?: Date
): Promise<IMedicationStats> {
    const start = startDate || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000); // 30 days ago
    const end = endDate || new Date();

    // Get total and active medications count
    const Medication = mongoose.model('Medication');
    const totalMedications = await Medication.countDocuments({ userId });
    const activeMedications = await Medication.countDocuments({ userId, isActive: true });

    // Get today's reminders
    const todayStart = new Date();
    todayStart.setHours(0, 0, 0, 0);
    const todayEnd = new Date();
    todayEnd.setHours(23, 59, 59, 999);

    const todayReminders = await this.find({
        userId,
        scheduledTime: { $gte: todayStart, $lte: todayEnd }
    });

    const totalRemindersToday = todayReminders.length;
    const takenToday = todayReminders.filter((r: any) => r.status === ReminderStatus.TAKEN).length;
    const missedToday = todayReminders.filter((r: any) => r.status === ReminderStatus.MISSED).length;

    // Get medication adherence data
    const adherenceData = await this.aggregate([
        {
            $match: {
                userId,
                scheduledTime: { $gte: start, $lte: end }
            }
        },
        {
            $lookup: {
                from: 'medications',
                localField: 'medicationId',
                foreignField: '_id',
                as: 'medication'
            }
        },
        {
            $unwind: '$medication'
        },
        {
            $group: {
                _id: '$medicationId',
                medicationName: { $first: '$medication.name' },
                totalReminders: { $sum: 1 },
                takenCount: {
                    $sum: { $cond: [{ $eq: ['$status', ReminderStatus.TAKEN] }, 1, 0] }
                },
                missedCount: {
                    $sum: { $cond: [{ $eq: ['$status', ReminderStatus.MISSED] }, 1, 0] }
                },
                skippedCount: {
                    $sum: { $cond: [{ $eq: ['$status', ReminderStatus.SKIPPED] }, 1, 0] }
                },
                lastTaken: {
                    $max: {
                        $cond: [{ $eq: ['$status', ReminderStatus.TAKEN] }, '$actualTime', null]
                    }
                }
            }
        },
        {
            $addFields: {
                adherencePercentage: {
                    $multiply: [
                        { $divide: ['$takenCount', '$totalReminders'] },
                        100
                    ]
                }
            }
        }
    ]);

    // Calculate overall adherence
    const totalReminders = adherenceData.reduce((sum, med) => sum + med.totalReminders, 0);
    const totalTaken = adherenceData.reduce((sum, med) => sum + med.takenCount, 0);
    const overallAdherence = totalReminders > 0 ? (totalTaken / totalReminders) * 100 : 0;

    // Get next due reminders for each medication
    const nextDueReminders = await this.aggregate([
        {
            $match: {
                userId,
                status: ReminderStatus.PENDING,
                scheduledTime: { $gte: new Date() }
            }
        },
        {
            $group: {
                _id: '$medicationId',
                nextDue: { $min: '$scheduledTime' }
            }
        }
    ]);

    const nextDueMap = new Map(
        nextDueReminders.map(item => [item._id.toString(), item.nextDue])
    );

    const medicationAdherence: IMedicationAdherence[] = adherenceData.map(med => ({
        medicationId: med._id,
        medicationName: med.medicationName,
        totalReminders: med.totalReminders,
        takenCount: med.takenCount,
        missedCount: med.missedCount,
        skippedCount: med.skippedCount,
        adherencePercentage: Math.round(med.adherencePercentage * 100) / 100,
        lastTaken: med.lastTaken,
        nextDue: nextDueMap.get(med._id.toString())
    }));

    return {
        totalMedications,
        activeMedications,
        totalRemindersToday,
        takenToday,
        missedToday,
        overallAdherence: Math.round(overallAdherence * 100) / 100,
        medicationAdherence
    };
};

// Model interface with static methods
export interface IMedicationReminderModel extends mongoose.Model<IMedicationReminder> {
    getAdherenceStats(
        userId: mongoose.Types.ObjectId,
        startDate?: Date,
        endDate?: Date
    ): Promise<IMedicationStats>;
}

const MedicationReminder = mongoose.model<IMedicationReminder, IMedicationReminderModel>('MedicationReminder', medicationReminderSchema);

export default MedicationReminder;
