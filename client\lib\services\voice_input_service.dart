import 'package:flutter/material.dart';
import 'package:speech_to_text/speech_to_text.dart';
import 'package:permission_handler/permission_handler.dart';
import '../models/food_entry.dart';
import '../services/food_diary_service.dart';

class VoiceInputService {
  static final SpeechToText _speechToText = SpeechToText();
  static bool _speechEnabled = false;

  /// Initialize speech recognition
  static Future<bool> initialize() async {
    try {
      _speechEnabled = await _speechToText.initialize(
        onError: (error) => debugPrint('Speech recognition error: $error'),
        onStatus: (status) => debugPrint('Speech recognition status: $status'),
      );
      return _speechEnabled;
    } catch (e) {
      debugPrint('Failed to initialize speech recognition: $e');
      return false;
    }
  }

  /// Check if microphone permission is granted
  static Future<bool> hasMicrophonePermission() async {
    final status = await Permission.microphone.status;
    return status == PermissionStatus.granted;
  }

  /// Request microphone permission
  static Future<bool> requestMicrophonePermission() async {
    final status = await Permission.microphone.request();
    return status == PermissionStatus.granted;
  }

  /// Start listening for voice input
  static Future<String?> startListening() async {
    if (!_speechEnabled) {
      final initialized = await initialize();
      if (!initialized) return null;
    }

    // Check microphone permission
    final hasPermission = await hasMicrophonePermission();
    if (!hasPermission) {
      final granted = await requestMicrophonePermission();
      if (!granted) return null;
    }

    String recognizedText = '';

    await _speechToText.listen(
      onResult: (result) {
        recognizedText = result.recognizedWords;
      },
      listenOptions: SpeechListenOptions(
        listenMode: ListenMode.confirmation,
        partialResults: false,
        autoPunctuation: true,
        enableHapticFeedback: true,
      ),
      listenFor: const Duration(seconds: 10),
      pauseFor: const Duration(seconds: 3),
      localeId: 'en_US',
    );

    // Wait for listening to complete
    while (_speechToText.isListening) {
      await Future.delayed(const Duration(milliseconds: 100));
    }

    return recognizedText.isNotEmpty ? recognizedText : null;
  }

  /// Stop listening
  static Future<void> stopListening() async {
    await _speechToText.stop();
  }

  /// Parse voice input to extract food items
  static List<String> parseFoodItems(String voiceInput) {
    final input = voiceInput.toLowerCase();

    // Common food separators
    final separators = [
      'and',
      ',',
      'with',
      'plus',
      'also',
      'then',
      'followed by',
      'along with',
      'including',
    ];

    // Split by separators
    List<String> items = [input];
    for (final separator in separators) {
      final newItems = <String>[];
      for (final item in items) {
        newItems.addAll(item.split(separator));
      }
      items = newItems;
    }

    // Clean up items
    return items
        .map((item) => item.trim())
        .where((item) => item.isNotEmpty && item.length > 2)
        .toList();
  }

  /// Search for foods based on voice input
  static Future<List<FoodEntry>> searchFoodsFromVoice(String voiceInput) async {
    final foodItems = parseFoodItems(voiceInput);
    final results = <FoodEntry>[];

    for (final item in foodItems) {
      try {
        final searchResults = await FoodDiaryService.searchFoodDatabase(item);
        if (searchResults.isNotEmpty) {
          // Take the best match (first result)
          results.add(searchResults.first);
        }
      } catch (e) {
        debugPrint('Error searching for "$item": $e');
      }
    }

    return results;
  }

  /// Get voice input suggestions
  static List<String> getVoiceInputSuggestions() {
    return [
      "I ate an apple",
      "I had chicken breast and rice",
      "I drank a glass of milk",
      "I ate oatmeal with berries",
      "I had a banana and yogurt",
      "I ate grilled fish with vegetables",
      "I had a salad with olive oil",
      "I drank green tea",
      "I ate whole grain bread",
      "I had scrambled eggs",
    ];
  }

  /// Check if speech recognition is available
  static bool get isAvailable => _speechEnabled;

  /// Check if currently listening
  static bool get isListening => _speechToText.isListening;
}
