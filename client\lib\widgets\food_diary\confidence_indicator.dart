import 'package:flutter/material.dart';
import '../../constants/app_colors.dart';

/// Widget that displays confidence score with visual indicators
class ConfidenceIndicator extends StatelessWidget {
  final double confidence;
  final String? label;
  final bool showPercentage;
  final bool showIcon;
  final double size;
  final bool isCompact;

  const ConfidenceIndicator({
    super.key,
    required this.confidence,
    this.label,
    this.showPercentage = true,
    this.showIcon = true,
    this.size = 24.0,
    this.isCompact = false,
  });

  @override
  Widget build(BuildContext context) {
    final confidenceLevel = _getConfidenceLevel(confidence);
    final color = _getConfidenceColor(confidence);
    final icon = _getConfidenceIcon(confidence);
    final percentage = (confidence * 100).toInt();

    if (isCompact) {
      return _buildCompactIndicator(color, icon, percentage);
    }

    return _buildFullIndicator(color, icon, percentage, confidenceLevel);
  }

  Widget _buildCompactIndicator(Color color, IconData icon, int percentage) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(10),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (showIcon) ...[
            Icon(icon, color: Colors.white, size: 12),
            const SizedBox(width: 4),
          ],
          if (showPercentage)
            Text(
              '$percentage%',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 11,
                fontWeight: FontWeight.bold,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildFullIndicator(
    Color color,
    IconData icon,
    int percentage,
    String confidenceLevel,
  ) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (showIcon) ...[
            Icon(icon, color: color, size: size),
            const SizedBox(width: 8),
          ],
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              if (label != null) ...[
                Text(
                  label!,
                  style: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 2),
              ],
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (showPercentage) ...[
                    Text(
                      '$percentage%',
                      style: TextStyle(
                        color: color,
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(width: 4),
                  ],
                  Text(
                    confidenceLevel,
                    style: TextStyle(
                      color: color,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  String _getConfidenceLevel(double confidence) {
    if (confidence >= 0.9) return 'Excellent';
    if (confidence >= 0.8) return 'Very Good';
    if (confidence >= 0.7) return 'Good';
    if (confidence >= 0.6) return 'Fair';
    if (confidence >= 0.5) return 'Poor';
    return 'Very Poor';
  }

  Color _getConfidenceColor(double confidence) {
    if (confidence >= 0.8) return Colors.green;
    if (confidence >= 0.6) return Colors.orange;
    return Colors.red;
  }

  IconData _getConfidenceIcon(double confidence) {
    if (confidence >= 0.8) return Icons.check_circle;
    if (confidence >= 0.6) return Icons.warning;
    return Icons.error;
  }
}

/// Animated confidence bar that fills based on confidence level
class ConfidenceBar extends StatefulWidget {
  final double confidence;
  final double height;
  final double width;
  final bool showLabel;
  final Duration animationDuration;

  const ConfidenceBar({
    super.key,
    required this.confidence,
    this.height = 8.0,
    this.width = 100.0,
    this.showLabel = true,
    this.animationDuration = const Duration(milliseconds: 800),
  });

  @override
  State<ConfidenceBar> createState() => _ConfidenceBarState();
}

class _ConfidenceBarState extends State<ConfidenceBar>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );
    _animation = Tween<double>(begin: 0.0, end: widget.confidence).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        if (widget.showLabel) ...[
          Text(
            'Confidence: ${(widget.confidence * 100).toInt()}%',
            style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
          ),
          const SizedBox(height: 4),
        ],
        Container(
          width: widget.width,
          height: widget.height,
          decoration: BoxDecoration(
            color: Colors.grey[300],
            borderRadius: BorderRadius.circular(widget.height / 2),
          ),
          child: AnimatedBuilder(
            animation: _animation,
            builder: (context, child) {
              final color = _getConfidenceColor(_animation.value);
              return Container(
                width: widget.width * _animation.value,
                height: widget.height,
                decoration: BoxDecoration(
                  color: color,
                  borderRadius: BorderRadius.circular(widget.height / 2),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Color _getConfidenceColor(double confidence) {
    if (confidence >= 0.8) return Colors.green;
    if (confidence >= 0.6) return Colors.orange;
    return Colors.red;
  }
}

/// Multi-level confidence indicator for multiple detected items
class MultiConfidenceIndicator extends StatelessWidget {
  final List<ConfidenceItem> items;
  final bool isExpanded;
  final VoidCallback? onToggle;

  const MultiConfidenceIndicator({
    super.key,
    required this.items,
    this.isExpanded = false,
    this.onToggle,
  });

  @override
  Widget build(BuildContext context) {
    if (items.isEmpty) return const SizedBox.shrink();

    final averageConfidence =
        items.fold<double>(0.0, (sum, item) => sum + item.confidence) /
        items.length;

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Overall confidence
          Row(
            children: [
              const Icon(Icons.analytics, color: AppColors.primary, size: 20),
              const SizedBox(width: 8),
              Text(
                'Detection Confidence',
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              ConfidenceIndicator(
                confidence: averageConfidence,
                isCompact: true,
              ),
              if (onToggle != null) ...[
                const SizedBox(width: 8),
                GestureDetector(
                  onTap: onToggle,
                  child: Icon(
                    isExpanded ? Icons.expand_less : Icons.expand_more,
                    color: AppColors.primary,
                  ),
                ),
              ],
            ],
          ),

          // Individual item confidences
          if (isExpanded) ...[
            const SizedBox(height: 12),
            const Divider(),
            const SizedBox(height: 8),
            ...items.map((item) => _buildConfidenceItem(item)),
          ],
        ],
      ),
    );
  }

  Widget _buildConfidenceItem(ConfidenceItem item) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Expanded(
            child: Text(item.name, style: const TextStyle(fontSize: 13)),
          ),
          const SizedBox(width: 8),
          ConfidenceBar(
            confidence: item.confidence,
            width: 80,
            height: 6,
            showLabel: false,
          ),
          const SizedBox(width: 8),
          Text(
            '${(item.confidence * 100).toInt()}%',
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: _getConfidenceColor(item.confidence),
            ),
          ),
        ],
      ),
    );
  }

  Color _getConfidenceColor(double confidence) {
    if (confidence >= 0.8) return Colors.green;
    if (confidence >= 0.6) return Colors.orange;
    return Colors.red;
  }
}

/// Data class for confidence items
class ConfidenceItem {
  final String name;
  final double confidence;

  const ConfidenceItem({required this.name, required this.confidence});
}

/// Confidence feedback widget for user corrections
class ConfidenceFeedback extends StatefulWidget {
  final String foodName;
  final double originalConfidence;
  final Function(bool isCorrect, String? correction) onFeedback;

  const ConfidenceFeedback({
    super.key,
    required this.foodName,
    required this.originalConfidence,
    required this.onFeedback,
  });

  @override
  State<ConfidenceFeedback> createState() => _ConfidenceFeedbackState();
}

class _ConfidenceFeedbackState extends State<ConfidenceFeedback> {
  bool? _isCorrect;
  final _correctionController = TextEditingController();

  @override
  void dispose() {
    _correctionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue.withValues(alpha: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.feedback, color: Colors.blue, size: 20),
              const SizedBox(width: 8),
              const Text(
                'Help improve recognition',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: Colors.blue,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            'Is "${widget.foodName}" correct?',
            style: const TextStyle(fontSize: 14),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: () => _setCorrect(true),
                  icon: Icon(
                    Icons.check,
                    color: _isCorrect == true ? Colors.white : Colors.green,
                  ),
                  label: Text(
                    'Yes',
                    style: TextStyle(
                      color: _isCorrect == true ? Colors.white : Colors.green,
                    ),
                  ),
                  style: OutlinedButton.styleFrom(
                    backgroundColor: _isCorrect == true ? Colors.green : null,
                    side: const BorderSide(color: Colors.green),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: () => _setCorrect(false),
                  icon: Icon(
                    Icons.close,
                    color: _isCorrect == false ? Colors.white : Colors.red,
                  ),
                  label: Text(
                    'No',
                    style: TextStyle(
                      color: _isCorrect == false ? Colors.white : Colors.red,
                    ),
                  ),
                  style: OutlinedButton.styleFrom(
                    backgroundColor: _isCorrect == false ? Colors.red : null,
                    side: const BorderSide(color: Colors.red),
                  ),
                ),
              ),
            ],
          ),
          if (_isCorrect == false) ...[
            const SizedBox(height: 12),
            TextField(
              controller: _correctionController,
              decoration: const InputDecoration(
                labelText: 'What food is this?',
                hintText: 'Enter the correct food name',
                border: OutlineInputBorder(),
                isDense: true,
              ),
              onSubmitted: (_) => _submitFeedback(),
            ),
            const SizedBox(height: 8),
            ElevatedButton(
              onPressed: _submitFeedback,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
              ),
              child: const Text(
                'Submit Correction',
                style: TextStyle(color: Colors.white),
              ),
            ),
          ],
        ],
      ),
    );
  }

  void _setCorrect(bool isCorrect) {
    setState(() {
      _isCorrect = isCorrect;
    });

    if (isCorrect) {
      widget.onFeedback(true, null);
    }
  }

  void _submitFeedback() {
    final correction = _correctionController.text.trim();
    if (correction.isNotEmpty) {
      widget.onFeedback(false, correction);
    }
  }
}
