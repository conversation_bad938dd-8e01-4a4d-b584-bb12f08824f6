@echo off
REM GlucoMonitor Backend Startup Script for Windows
REM This script helps start the backend server for development

setlocal enabledelayedexpansion

REM Function to print colored output (simplified for Windows)
set "INFO=[INFO]"
set "SUCCESS=[SUCCESS]"
set "WARNING=[WARNING]"
set "ERROR=[ERROR]"

REM Check if we're in the right directory
if not exist "backend" (
    echo %ERROR% Backend directory not found. Please run this script from the project root.
    exit /b 1
)

REM Check if Node.js is installed
node --version >nul 2>&1
if errorlevel 1 (
    echo %ERROR% Node.js is not installed. Please install Node.js and try again.
    exit /b 1
)

REM Check if npm is installed
npm --version >nul 2>&1
if errorlevel 1 (
    echo %ERROR% npm is not installed. Please install npm and try again.
    exit /b 1
)

echo %INFO% Starting GlucoMonitor Backend Server...

REM Navigate to backend directory
cd backend

REM Check if node_modules exists
if not exist "node_modules" (
    echo %WARNING% node_modules not found. Installing dependencies...
    npm install
)

REM Check if .env file exists
if not exist ".env" (
    echo %WARNING% .env file not found.
    if exist ".env.example" (
        echo %INFO% Creating .env from .env.example...
        copy ".env.example" ".env" >nul
        echo %WARNING% Please edit .env file with your configuration before running the server.
        pause
        exit /b 1
    ) else (
        echo %ERROR% .env.example not found. Please create environment configuration.
        pause
        exit /b 1
    )
)

REM Check if TypeScript is compiled
if not exist "dist" (
    echo %INFO% Compiling TypeScript...
    npm run build
)

REM Start the server
echo %INFO% Starting backend server in development mode...
echo %INFO% Server will be available at: http://localhost:5000
echo %INFO% Health check endpoint: http://localhost:5000/api/health
echo %INFO% Press Ctrl+C to stop the server
echo %SUCCESS% Backend server starting...

REM Start the development server
npm run dev

endlocal
