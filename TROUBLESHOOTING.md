# 🔧 GlucoMonitor Troubleshooting Guide

This guide helps resolve common issues with the GlucoMonitor application.

## 🚨 Health Check Timeout Error

**Error Message:**
```
I/flutter (22708): Health check failed: TimeoutException after 0:00:05.000000: Future not completed
```

### 🔍 Root Cause
This error occurs when the Flutter app cannot connect to the backend server within the timeout period (5-10 seconds).

### 🛠️ Solutions

#### 1. **Start the Backend Server**

The most common cause is that the backend server is not running.

**Option A: Using Scripts (Recommended)**
```bash
# Linux/macOS
./start-backend.sh

# Windows
start-backend.bat
```

**Option B: Manual Start**
```bash
cd backend
npm install  # First time only
npm run dev  # Start development server
```

**Option C: Using Docker**
```bash
# Development environment
./docker-manager.sh dev

# Or using docker-compose directly
docker-compose --profile dev up --build
```

#### 2. **Check Backend URL Configuration**

Verify the backend URL in `client/.env`:

```env
# For local development
BACKEND_URL=http://localhost:5000/api

# For network access (replace with your IP)
BACKEND_URL=http://*************:5000/api
```

#### 3. **Verify Backend Health**

Test the backend health endpoint directly:

```bash
# Check if backend is responding
curl http://localhost:5000/api/health

# Expected response:
{
  "success": true,
  "data": {
    "status": "healthy",
    "timestamp": "2024-01-01T12:00:00.000Z",
    "uptime": 123.45,
    "version": "1.0.0",
    "environment": "development"
  }
}
```

#### 4. **Network Connectivity Issues**

**For Physical Devices:**
If testing on a physical device, use your computer's IP address:

```bash
# Find your IP address
# Windows
ipconfig

# macOS/Linux
ifconfig
# or
ip addr show
```

Update `client/.env`:
```env
BACKEND_URL=http://YOUR_IP_ADDRESS:5000/api
```

**For Emulators:**
- **Android Emulator**: Use `http://********:5000/api`
- **iOS Simulator**: Use `http://localhost:5000/api`

#### 5. **Firewall and Port Issues**

Ensure port 5000 is not blocked:

```bash
# Check if port 5000 is in use
# Windows
netstat -ano | findstr :5000

# macOS/Linux
lsof -i :5000
```

If port 5000 is occupied, either:
- Stop the process using port 5000
- Change the backend port in `backend/.env`:
  ```env
  PORT=5001
  ```
- Update the client configuration accordingly

### 🔧 Advanced Troubleshooting

#### Check Backend Logs

```bash
cd backend
npm run logs:tail  # View real-time logs
```

Look for error messages related to:
- Database connection issues
- Redis connection problems
- Environment variable errors

#### Increase Timeout Values

If the backend is slow to start, increase timeout values in `client/lib/main.dart`:

```dart
await BackendService.init().timeout(
  const Duration(seconds: 30), // Increased from 10
  onTimeout: () {
    debugPrint('Backend service initialization timed out');
  },
);
```

#### Debug Network Requests

Enable detailed logging in `client/lib/services/backend_service.dart`:

```dart
static Future<bool> checkConnection() async {
  try {
    final healthUrl = '${baseUrl.replaceAll('/api', '')}/api/health';
    debugPrint('Checking backend health at: $healthUrl');
    
    final response = await http.get(
      Uri.parse(healthUrl),
      headers: {'Content-Type': 'application/json'},
    ).timeout(const Duration(seconds: 10));

    debugPrint('Health check response: ${response.statusCode}');
    debugPrint('Response body: ${response.body}');
    
    // ... rest of the method
  } catch (e) {
    debugPrint('Health check failed: $e');
    debugPrint('Backend URL: $baseUrl');
    return false;
  }
}
```

## 🐛 Other Common Issues

### Database Connection Issues

**Error:** MongoDB connection failed

**Solution:**
1. Ensure MongoDB is running
2. Check `MONGODB_URI` in `backend/.env`
3. Verify network connectivity to MongoDB

### Redis Connection Issues

**Error:** Redis connection timeout

**Solution:**
1. Start Redis server: `redis-server`
2. Check `REDIS_URL` in `backend/.env`
3. Use Docker: `docker run -d -p 6379:6379 redis:7-alpine`

### Environment Variable Issues

**Error:** Missing environment variables

**Solution:**
1. Copy environment templates:
   ```bash
   cp backend/.env.example backend/.env
   cp client/.env.example client/.env  # If exists
   ```
2. Fill in required values
3. Restart the application

### Build Issues

**Error:** TypeScript compilation errors

**Solution:**
```bash
cd backend
npm run clean      # Clean previous build
npm run build      # Rebuild TypeScript
```

### Permission Issues (Linux/macOS)

**Error:** Permission denied

**Solution:**
```bash
# Make scripts executable
chmod +x start-backend.sh
chmod +x docker-manager.sh

# Fix file permissions
sudo chown -R $USER:$USER ./backend/logs
sudo chown -R $USER:$USER ./uploads
```

## 📱 Flutter-Specific Issues

### Hot Reload Not Working

**Solution:**
1. Restart the Flutter app: `flutter run`
2. Clear Flutter cache: `flutter clean && flutter pub get`

### Package Issues

**Solution:**
```bash
cd client
flutter clean
flutter pub get
flutter pub upgrade
```

### Build Issues

**Solution:**
```bash
cd client
flutter clean
flutter pub get
flutter build apk --debug  # For Android
flutter build ios --debug  # For iOS
```

## 🔍 Debugging Steps

### 1. Check Service Status
```bash
# Backend health
curl http://localhost:5000/api/health

# Database health
curl http://localhost:5000/api/health/database

# Redis health
curl http://localhost:5000/api/health/redis
```

### 2. View Logs
```bash
# Backend logs
cd backend && npm run logs:tail

# Docker logs
docker-compose logs -f

# Flutter logs
flutter logs
```

### 3. Test Network Connectivity
```bash
# Test basic connectivity
ping localhost

# Test port accessibility
telnet localhost 5000
```

## 🆘 Getting Help

If you're still experiencing issues:

1. **Check the logs** for specific error messages
2. **Verify all services are running** (backend, database, Redis)
3. **Test network connectivity** between client and server
4. **Review environment configuration** files
5. **Try the Docker setup** for a consistent environment

### Useful Commands

```bash
# Quick health check
curl -f http://localhost:5000/api/health && echo "Backend is healthy" || echo "Backend is down"

# Start everything with Docker
./docker-manager.sh dev

# View all service status
./docker-manager.sh status

# Clean restart
./docker-manager.sh clean && ./docker-manager.sh dev
```

### Environment Information

When reporting issues, include:
- Operating system and version
- Node.js version (`node --version`)
- Flutter version (`flutter --version`)
- Docker version (`docker --version`)
- Error messages and logs
- Steps to reproduce the issue
