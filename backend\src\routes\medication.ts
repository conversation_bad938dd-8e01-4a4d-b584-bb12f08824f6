import express from 'express';
import {
    getMedications,
    getMedication,
    createMedication,
    updateMedication,
    deleteMedication,
    getMedicationReminders,
    getAllReminders,
    updateReminderStatus,
    getMedicationStats,
    bulkUpdateReminders,
    generateMedicationReminders
} from '../controllers/medication';
import { protect } from '../middleware/auth';

const router = express.Router();

// Apply authentication middleware to all routes
router.use(protect);

// Medication CRUD routes
router.route('/')
    .get(getMedications)      // GET /api/medications - Get all medications for user
    .post(createMedication);  // POST /api/medications - Create new medication

// General reminder routes (must come before /:id routes)
router.route('/reminders')
    .get(getAllReminders); // GET /api/medications/reminders - Get all reminders for user

router.route('/reminders/bulk')
    .put(bulkUpdateReminders); // PUT /api/medications/reminders/bulk - Bulk update reminder statuses

router.route('/reminders/:id')
    .put(updateReminderStatus); // PUT /api/medications/reminders/:id - Update single reminder status

// Statistics route (must come before /:id routes)
router.route('/stats')
    .get(getMedicationStats); // GET /api/medications/stats - Get medication adherence statistics

// Parameterized routes (must come after specific routes)
router.route('/:id')
    .get(getMedication)       // GET /api/medications/:id - Get single medication
    .put(updateMedication)    // PUT /api/medications/:id - Update medication
    .delete(deleteMedication); // DELETE /api/medications/:id - Delete medication

// Medication-specific reminder routes
router.route('/:id/reminders')
    .get(getMedicationReminders); // GET /api/medications/:id/reminders - Get reminders for specific medication

router.route('/:id/generate-reminders')
    .post(generateMedicationReminders); // POST /api/medications/:id/generate-reminders - Generate reminders for medication

export default router;
