import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/auth_provider.dart';
import '../../constants/app_colors.dart';
import '../onboarding/welcome_screen.dart';
import '../../widgets/export_options_dialog.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  void _showExportDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => const ExportOptionsDialog(),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, _) {
        debugPrint('HomeScreen build - User name: ${authProvider.name}');
        return Scaffold(
          backgroundColor: AppColors.background,
          appBar: AppBar(
            title: const Text('GlucoMonitor'),
            backgroundColor: AppColors.background,
            foregroundColor: AppColors.onBackground,
            iconTheme: const IconThemeData(color: AppColors.onBackground),
            actions: [
              IconButton(
                icon: const Icon(Icons.logout),
                onPressed: () async {
                  await authProvider.logout();
                  if (context.mounted) {
                    Navigator.of(context).pushAndRemoveUntil(
                      MaterialPageRoute(builder: (_) => const WelcomeScreen()),
                      (route) => false,
                    );
                  }
                },
              ),
            ],
          ),
          body: SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Welcome ${authProvider.name ?? 'User'}!',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    color: AppColors.onBackground,
                  ),
                ),
                const SizedBox(height: 24),

                // Latest Reading Card
                Card(
                  color: AppColors.surface,
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Latest Reading',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: AppColors.onSurface,
                          ),
                        ),
                        const SizedBox(height: 12),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  '120 mg/dL',
                                  style: Theme.of(context)
                                      .textTheme
                                      .headlineMedium
                                      ?.copyWith(color: AppColors.onSurface),
                                ),
                                const Text(
                                  'Within Range',
                                  style: TextStyle(color: AppColors.onSurface),
                                ),
                              ],
                            ),
                            ElevatedButton(
                              onPressed: () {
                                // TODO: Add new reading
                              },
                              child: const Text('Add Reading'),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 24),

                // Quick Actions Grid
                GridView.count(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  crossAxisCount: 2,
                  mainAxisSpacing: 16,
                  crossAxisSpacing: 16,
                  childAspectRatio: 1.1,
                  children: [
                    _QuickActionCard(
                      title: 'Food Diary',
                      icon: Icons.restaurant_menu,
                      onTap: () {
                        // TODO: Navigate to Food Diary
                      },
                    ),
                    _QuickActionCard(
                      title: 'AI Recognition',
                      icon: Icons.auto_awesome,
                      color: AppColors.primary,
                      onTap: () {
                        Navigator.of(context).pushNamed('/enhanced-ai-demo');
                      },
                    ),
                    _QuickActionCard(
                      title: 'Medications',
                      icon: Icons.medication,
                      onTap: () {
                        // TODO: Navigate to Medications
                      },
                    ),
                    _QuickActionCard(
                      title: 'Export Data',
                      icon: Icons.download,
                      onTap: () {
                        _showExportDialog(context);
                      },
                    ),
                    _QuickActionCard(
                      title: 'Emergency',
                      icon: Icons.emergency,
                      color: Colors.red,
                      onTap: () {
                        // TODO: Show emergency contacts
                      },
                    ),
                    _QuickActionCard(
                      title: 'Quick Scan',
                      icon: Icons.qr_code_scanner,
                      color: AppColors.secondary,
                      onTap: () {
                        Navigator.of(context).pushNamed('/barcode-scanner');
                      },
                    ),
                  ],
                ),

                const SizedBox(height: 24),

                // Today's Schedule
                Card(
                  color: AppColors.surface,
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Today\'s Schedule',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: AppColors.onSurface,
                          ),
                        ),
                        const SizedBox(height: 12),
                        _ScheduleItem(
                          time: '08:00 AM',
                          title: 'Morning Medicine',
                          isDone: true,
                        ),
                        _ScheduleItem(
                          time: '02:00 PM',
                          title: 'Check Blood Sugar',
                          isDone: false,
                        ),
                        _ScheduleItem(
                          time: '08:00 PM',
                          title: 'Evening Medicine',
                          isDone: false,
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          floatingActionButton: FloatingActionButton(
            heroTag: "home_fab",
            onPressed: () {
              // TODO: Add new reading
            },
            child: const Icon(Icons.add),
          ),
        );
      },
    );
  }
}

class _QuickActionCard extends StatelessWidget {
  final String title;
  final IconData icon;
  final VoidCallback onTap;
  final Color? color;

  const _QuickActionCard({
    required this.title,
    required this.icon,
    required this.onTap,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      color: AppColors.surface,
      child: InkWell(
        onTap: onTap,
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(icon, size: 32, color: color ?? AppColors.onSurface),
              const SizedBox(height: 8),
              Text(
                title,
                textAlign: TextAlign.center,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: AppColors.onSurface,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _ScheduleItem extends StatelessWidget {
  final String time;
  final String title;
  final bool isDone;

  const _ScheduleItem({
    required this.time,
    required this.title,
    required this.isDone,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        children: [
          Icon(
            isDone ? Icons.check_circle : Icons.schedule,
            color: isDone ? AppColors.secondary : AppColors.textSecondary,
          ),
          const SizedBox(width: 12),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                time,
                style: const TextStyle(
                  color: AppColors.textSecondary,
                  fontSize: 12,
                ),
              ),
              Text(
                title,
                style: TextStyle(
                  decoration: isDone ? TextDecoration.lineThrough : null,
                  color: AppColors.onSurface,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
