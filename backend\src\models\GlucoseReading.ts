import mongoose, { Document, Schema } from 'mongoose';

// Glucose Reading interface
export interface IGlucoseReading extends Document {
    userId: mongoose.Types.ObjectId;
    value: number; // mg/dL
    timestamp: Date;
    mealTiming?: 'before_breakfast' | 'after_breakfast' | 'before_lunch' | 'after_lunch' | 'before_dinner' | 'after_dinner' | 'bedtime' | 'other';
    notes?: string;
    tags?: string[]; // e.g., ['exercise', 'stress', 'medication']
    context?: 'fasting' | 'post_meal' | 'exercise' | 'stress' | 'illness' | 'medication' | 'other'; // Additional context for better analytics
    symptoms?: string[]; // e.g., ['fatigue', 'thirst', 'blurred_vision']
    location?: string; // Where the reading was taken
    deviceId?: string; // Glucose meter device identifier
    isManual: boolean; // Whether reading was entered manually or from device
    confidence?: 'low' | 'medium' | 'high'; // Confidence level in the reading
    createdAt: Date;
    updatedAt: Date;
}

const glucoseReadingSchema = new Schema<IGlucoseReading>({
    userId: {
        type: Schema.Types.ObjectId,
        ref: 'User',
        required: [true, 'User ID is required'],
        index: true
    },
    value: {
        type: Number,
        required: [true, 'Glucose value is required'],
        min: [20, 'Glucose value must be at least 20 mg/dL'],
        max: [600, 'Glucose value must be at most 600 mg/dL']
    },
    timestamp: {
        type: Date,
        required: [true, 'Timestamp is required'],
        default: Date.now,
        index: true
    },
    mealTiming: {
        type: String,
        enum: ['before_breakfast', 'after_breakfast', 'before_lunch', 'after_lunch', 'before_dinner', 'after_dinner', 'bedtime', 'other'],
        default: 'other'
    },
    notes: {
        type: String,
        maxlength: [500, 'Notes cannot exceed 500 characters'],
        trim: true
    },
    tags: [{
        type: String,
        trim: true,
        maxlength: [50, 'Tag cannot exceed 50 characters']
    }],
    context: {
        type: String,
        enum: ['fasting', 'post_meal', 'exercise', 'stress', 'illness', 'medication', 'other'],
        default: 'other'
    },
    symptoms: [{
        type: String,
        trim: true,
        maxlength: [100, 'Symptom cannot exceed 100 characters']
    }],
    location: {
        type: String,
        trim: true,
        maxlength: [100, 'Location cannot exceed 100 characters']
    },
    deviceId: {
        type: String,
        trim: true,
        maxlength: [50, 'Device ID cannot exceed 50 characters']
    },
    isManual: {
        type: Boolean,
        default: true
    },
    confidence: {
        type: String,
        enum: ['low', 'medium', 'high'],
        default: 'medium'
    }
}, {
    timestamps: true
});

// Compound index for efficient queries by user and timestamp
glucoseReadingSchema.index({ userId: 1, timestamp: -1 });

// Virtual for glucose level category (updated ranges for better accuracy)
glucoseReadingSchema.virtual('category').get(function(this: IGlucoseReading) {
    if (this.value < 80) return 'low';
    if (this.value <= 180) return 'normal'; // Target range 80-180 mg/dL
    if (this.value <= 250) return 'high';
    return 'very_high';
});

// Virtual for time-based category
glucoseReadingSchema.virtual('timeCategory').get(function(this: IGlucoseReading) {
    const hour = this.timestamp.getHours();
    if (hour >= 6 && hour < 12) return 'morning';
    if (hour >= 12 && hour < 18) return 'afternoon';
    if (hour >= 18 && hour < 22) return 'evening';
    return 'night';
});

// Virtual for trend analysis (requires comparison with previous reading)
glucoseReadingSchema.virtual('trend').get(function(this: IGlucoseReading) {
    // This would be calculated in the controller with previous reading data
    return 'stable'; // Default value
});

// Ensure virtual fields are serialized
glucoseReadingSchema.set('toJSON', { virtuals: true });

const GlucoseReading = mongoose.model<IGlucoseReading>('GlucoseReading', glucoseReadingSchema);

export default GlucoseReading;
