const axios = require('axios');

// Configuration
const BASE_URL = 'http://localhost:5000/api';
const authToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NjdlZTJhZTIyOTFjZDliYjQwNzAyYiIsImlhdCI6MTc1MTk2ODQ0NX0.oIgLoFufiOBKzpMo3PB0fvz1K6MjP65Fk9hJvvIYXP4";

// Headers with authentication
const headers = {
    'Authorization': `Bearer ${authToken}`,
    'Content-Type': 'application/json'
};

async function testFoodDiaryAPI() {
    console.log('🧪 Testing Food Diary API Endpoints');
    console.log('=====================================\n');

    try {
        // Test 1: Test route availability
        console.log('1️⃣ Testing route availability...');
        const testResponse = await axios.get(`${BASE_URL}/food-diary/test`, { headers });
        console.log('✅ Test route:', testResponse.data);
        console.log('');

        // Test 2: Search food database
        console.log('2️⃣ Testing food database search...');
        const searchResponse = await axios.get(`${BASE_URL}/food-diary/search?query=pap`, { headers });
        console.log('✅ Search results:', searchResponse.data);
        console.log('');

        // Test 3: Get foods by category
        console.log('3️⃣ Testing get foods by category...');
        const categoryResponse = await axios.get(`${BASE_URL}/food-diary/foods/category/grains_starches`, { headers });
        console.log('✅ Category foods:', categoryResponse.data);
        console.log('');

        // Test 4: Get diabetes-friendly foods
        console.log('4️⃣ Testing diabetes-friendly foods...');
        const diabeticResponse = await axios.get(`${BASE_URL}/food-diary/foods/diabetes-friendly`, { headers });
        console.log('✅ Diabetes-friendly foods:', diabeticResponse.data);
        console.log('');

        // Test 5: Get traditional SA foods
        console.log('5️⃣ Testing traditional SA foods...');
        const traditionalResponse = await axios.get(`${BASE_URL}/food-diary/foods/traditional-sa`, { headers });
        console.log('✅ Traditional SA foods:', traditionalResponse.data);
        console.log('');

        // Test 6: Create a food entry
        console.log('6️⃣ Testing create food entry...');
        const foodEntryData = {
            name: 'Pap (Maize Porridge)',
            carbohydrates: 23,
            calories: 112,
            protein: 2.5,
            fat: 0.5,
            fiber: 1.2,
            glycemicIndex: 'high',
            category: 'grains_starches',
            portion: '1 cup cooked',
            portionSize: 240,
            unit: 'g',
            mealType: 'breakfast',
            notes: 'Traditional South African breakfast',
            brand: '',
            isCustom: false
        };

        const createResponse = await axios.post(`${BASE_URL}/food-diary/entries`, foodEntryData, { headers });
        console.log('✅ Created food entry:', createResponse.data);
        const entryId = createResponse.data.data._id;
        console.log('');

        // Test 7: Get food entries
        console.log('7️⃣ Testing get food entries...');
        const entriesResponse = await axios.get(`${BASE_URL}/food-diary/entries`, { headers });
        console.log('✅ Food entries:', entriesResponse.data);
        console.log('');

        // Test 8: Get daily nutrition
        console.log('8️⃣ Testing get daily nutrition...');
        const nutritionResponse = await axios.get(`${BASE_URL}/food-diary/daily-nutrition`, { headers });
        console.log('✅ Daily nutrition:', nutritionResponse.data);
        console.log('');

        // Test 9: Update food entry
        console.log('9️⃣ Testing update food entry...');
        const updateData = {
            notes: 'Updated: Traditional South African breakfast with butter'
        };
        const updateResponse = await axios.put(`${BASE_URL}/food-diary/entries/${entryId}`, updateData, { headers });
        console.log('✅ Updated food entry:', updateResponse.data);
        console.log('');

        // Test 10: Get nutrition analytics
        console.log('🔟 Testing nutrition analytics...');
        const analyticsResponse = await axios.get(`${BASE_URL}/food-diary/analytics?period=7`, { headers });
        console.log('✅ Nutrition analytics:', analyticsResponse.data);
        console.log('');

        // Test 11: Delete food entry
        console.log('1️⃣1️⃣ Testing delete food entry...');
        const deleteResponse = await axios.delete(`${BASE_URL}/food-diary/entries/${entryId}`, { headers });
        console.log('✅ Deleted food entry:', deleteResponse.data);
        console.log('');

        console.log('🎉 All Food Diary API tests completed successfully!');

    } catch (error) {
        console.error('❌ Test failed:', error.response?.data || error.message);
        if (error.response?.status === 401) {
            console.log('💡 Hint: Make sure you have a valid JWT token');
        }
    }
}

// Run the tests
testFoodDiaryAPI();
