import { Request, Response, NextFunction } from 'express';
import { MongoError } from 'mongodb';
import { Error as MongooseError } from 'mongoose';
import { env } from '../config/env';
import logger from './logger';

/**
 * Custom application error class for operational errors
 */
export class AppError extends Error {
  public readonly statusCode: number;
  public readonly isOperational: boolean;
  public readonly timestamp: string;
  public readonly code?: string;
  public readonly details?: any;

  constructor(
    message: string,
    statusCode: number = 500,
    isOperational: boolean = true,
    code?: string,
    details?: any
  ) {
    super(message);
    this.name = this.constructor.name;
    this.statusCode = statusCode;
    this.isOperational = isOperational;
    this.timestamp = new Date().toISOString();
    this.code = code;
    this.details = details;

    // Capture stack trace, excluding constructor call from it
    Error.captureStackTrace(this, this.constructor);
  }
}

/**
 * Validation error for input validation failures
 */
export class ValidationError extends AppError {
  public readonly errors: any[];

  constructor(message: string, errors: any[] = []) {
    super(message, 400, true, 'VALIDATION_ERROR', errors);
    this.errors = errors;
  }
}

/**
 * Authentication error for auth failures
 */
export class AuthError extends AppError {
  constructor(message: string = 'Authentication failed') {
    super(message, 401, true, 'AUTHENTICATION_ERROR');
  }
}

/**
 * Authorization error for permission failures
 */
export class AuthorizationError extends AppError {
  constructor(message: string = 'Access denied') {
    super(message, 403, true, 'AUTHORIZATION_ERROR');
  }
}

/**
 * Not found error for missing resources
 */
export class NotFoundError extends AppError {
  constructor(resource: string = 'Resource') {
    super(`${resource} not found`, 404, true, 'NOT_FOUND_ERROR');
  }
}

/**
 * Conflict error for duplicate resources
 */
export class ConflictError extends AppError {
  constructor(message: string) {
    super(message, 409, true, 'CONFLICT_ERROR');
  }
}

/**
 * Rate limit error for too many requests
 */
export class RateLimitError extends AppError {
  constructor(message: string = 'Too many requests') {
    super(message, 429, true, 'RATE_LIMIT_ERROR');
  }
}

/**
 * Database error for database-related issues
 */
export class DatabaseError extends AppError {
  constructor(message: string, details?: any) {
    super(message, 500, true, 'DATABASE_ERROR', details);
  }
}

/**
 * External service error for third-party service failures
 */
export class ExternalServiceError extends AppError {
  constructor(service: string, message: string) {
    super(`External service error (${service}): ${message}`, 502, true, 'EXTERNAL_SERVICE_ERROR');
  }
}



/**
 * Enhanced error handler middleware
 */
export const errorHandler = (
  err: Error, 
  req: Request, 
  res: Response, 
  next: NextFunction
): void => {
  let error = err;

  // Log error with enhanced logger
  logger.exception(err, {
    requestId: req.headers['x-request-id'] as string,
    userId: (req as any).user?.id,
    url: req.url,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    body: req.body,
    params: req.params,
    query: req.query
  });

  // Handle specific error types
  if (err.name === 'ValidationError') {
    // Mongoose validation error
    const message = Object.values(err as any).map((val: any) => val.message).join(', ');
    error = new ValidationError(message);
  } else if (err.name === 'CastError') {
    // Mongoose bad ObjectId
    error = new ValidationError('Invalid ID format');
  } else if ((err as any).code === 11000) {
    // Mongoose duplicate key error
    const field = Object.keys((err as any).keyValue)[0];
    error = new ValidationError(`${field} already exists`);
  } else if (err.name === 'JsonWebTokenError') {
    // JWT error
    error = new AuthError('Invalid token');
  } else if (err.name === 'TokenExpiredError') {
    // JWT expired
    error = new AuthError('Token expired');
  }

  // Handle AppError instances
  if (error instanceof AppError) {
    const response: any = {
      success: false,
      error: {
        message: error.message,
        code: error.code,
        statusCode: error.statusCode,
        timestamp: error.timestamp,
        requestId: req.headers['x-request-id'] as string
      }
    };

    // Add validation errors if present
    if (error instanceof ValidationError && error.errors.length > 0) {
      response.error.details = error.errors;
    }

    // Add additional details if present
    if (error.details) {
      response.error.details = error.details;
    }

    // Add stack trace in development
    if (env.NODE_ENV === 'development') {
      response.error.stack = error.stack;
    }

    res.status(error.statusCode).json(response);
    return;
  }

  // Handle unexpected errors
  logger.exception(err, {
    requestId: req.headers['x-request-id'] as string,
    category: 'unexpected-error'
  });

  const response: any = {
    success: false,
    error: {
      message: env.NODE_ENV === 'production'
        ? 'Internal server error'
        : err.message,
      code: 'INTERNAL_SERVER_ERROR',
      statusCode: 500,
      timestamp: new Date().toISOString(),
      requestId: req.headers['x-request-id'] as string
    }
  };

  // Add stack trace in development
  if (env.NODE_ENV === 'development') {
    response.error.stack = err.stack;
  }

  res.status(500).json(response);
};

/**
 * Async error wrapper to catch async errors in route handlers
 */
export const asyncHandler = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

// Alternative name for backward compatibility
export const catchAsync = asyncHandler;

/**
 * 404 handler for unmatched routes
 */
export const notFoundHandler = (req: Request, res: Response, next: NextFunction): void => {
  const error = new NotFoundError(`Route ${req.originalUrl} not found`);
  next(error);
};

/**
 * Graceful shutdown handler
 */
export const gracefulShutdown = (server: any) => {
  return (signal: string) => {
    logger.info(`Received ${signal}. Graceful shutdown initiated...`);

    server.close(() => {
      logger.info('HTTP server closed.');
      process.exit(0);
    });

    // Force close after 10 seconds
    setTimeout(() => {
      logger.error('Could not close connections in time, forcefully shutting down');
      process.exit(1);
    }, 10000);
  };
};

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason: any, promise: Promise<any>) => {
  logger.error('Unhandled Promise Rejection', {
    reason: reason?.message || reason,
    stack: reason?.stack,
    promise: promise.toString(),
    category: 'unhandled-rejection'
  });

  // Close server gracefully in production
  if (env.NODE_ENV === 'production') {
    process.exit(1);
  }
});

// Handle uncaught exceptions
process.on('uncaughtException', (err: Error) => {
  logger.exception(err, { category: 'uncaught-exception' });

  // Close server gracefully
  process.exit(1);
});
