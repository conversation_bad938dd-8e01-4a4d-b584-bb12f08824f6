// Load environment variables
import dotenv from 'dotenv';
dotenv.config();

import { seedFoodDatabase } from './src/seeders/foodDatabaseSeeder';

async function runSeeder() {
    try {
        console.log('🚀 Starting food database seeding process...');
        await seedFoodDatabase();
        console.log('✅ Food database seeding completed successfully!');
        process.exit(0);
    } catch (error) {
        console.error('❌ Food database seeding failed:', error);
        process.exit(1);
    }
}

runSeeder();
