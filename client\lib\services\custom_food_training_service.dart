import 'dart:convert';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/food_entry.dart';
import '../services/enhanced_offline_recognition_service.dart';

/// Service for training AI on user's custom foods and recipes
class CustomFoodTrainingService {
  static const String _customFoodsKey = 'custom_trained_foods';

  /// Train the AI on a custom food with photo examples
  static Future<bool> trainCustomFood({
    required String foodName,
    required List<Uint8List> photoExamples,
    required FoodEntry nutritionData,
    String? description,
    List<String>? tags,
  }) async {
    try {
      debugPrint('🎓 Training custom food: $foodName');

      // Extract features from all photo examples
      final trainingFeatures = <ComprehensiveImageFeatures>[];
      for (final photo in photoExamples) {
        final features = await _extractTrainingFeatures(photo);
        trainingFeatures.add(features);
      }

      // Create custom food pattern
      final customPattern = await _createCustomPattern(
        foodName,
        trainingFeatures,
        nutritionData,
        description,
        tags,
      );

      // Save custom food
      await _saveCustomFood(customPattern);

      debugPrint('✅ Custom food training completed: $foodName');
      return true;
    } catch (e) {
      debugPrint('💥 Error training custom food: $e');
      return false;
    }
  }

  /// Recognize custom food from image features
  static Future<FoodEntry?> recognizeCustomFood(
    ComprehensiveImageFeatures features,
    MealType mealType,
  ) async {
    try {
      final customFoods = await _getCustomFoods();

      double bestScore = 0.0;
      CustomFoodPattern? bestMatch;

      for (final customFood in customFoods) {
        final score = _calculateCustomPatternScore(features, customFood);
        if (score > bestScore && score > 0.75) {
          // High threshold for custom foods
          bestScore = score;
          bestMatch = customFood;
        }
      }

      if (bestMatch != null) {
        debugPrint('✅ Custom food recognized: ${bestMatch.name}');
        return _createFoodEntryFromCustomPattern(
          bestMatch,
          mealType,
          bestScore,
        );
      }

      return null;
    } catch (e) {
      debugPrint('💥 Error recognizing custom food: $e');
      return null;
    }
  }

  /// Get all trained custom foods
  static Future<List<CustomFoodPattern>> getTrainedFoods() async {
    return await _getCustomFoods();
  }

  /// Update custom food training with new examples
  static Future<bool> updateCustomFood({
    required String foodId,
    List<Uint8List>? newPhotoExamples,
    FoodEntry? updatedNutrition,
    String? updatedDescription,
  }) async {
    try {
      final customFoods = await _getCustomFoods();
      final foodIndex = customFoods.indexWhere((food) => food.id == foodId);

      if (foodIndex == -1) {
        debugPrint('❌ Custom food not found: $foodId');
        return false;
      }

      var customFood = customFoods[foodIndex];

      // Add new training examples
      if (newPhotoExamples != null && newPhotoExamples.isNotEmpty) {
        final newFeatures = <ComprehensiveImageFeatures>[];
        for (final photo in newPhotoExamples) {
          final features = await _extractTrainingFeatures(photo);
          newFeatures.add(features);
        }

        customFood = customFood.copyWith(
          trainingFeatures: [...customFood.trainingFeatures, ...newFeatures],
        );
      }

      // Update nutrition data
      if (updatedNutrition != null) {
        customFood = customFood.copyWith(nutritionData: updatedNutrition);
      }

      // Update description
      if (updatedDescription != null) {
        customFood = customFood.copyWith(description: updatedDescription);
      }

      // Update last trained timestamp
      customFood = customFood.copyWith(lastTrained: DateTime.now());

      // Save updated custom food
      customFoods[foodIndex] = customFood;
      await _saveCustomFoods(customFoods);

      debugPrint('✅ Custom food updated: ${customFood.name}');
      return true;
    } catch (e) {
      debugPrint('💥 Error updating custom food: $e');
      return false;
    }
  }

  /// Delete custom food training
  static Future<bool> deleteCustomFood(String foodId) async {
    try {
      final customFoods = await _getCustomFoods();
      customFoods.removeWhere((food) => food.id == foodId);
      await _saveCustomFoods(customFoods);

      debugPrint('✅ Custom food deleted: $foodId');
      return true;
    } catch (e) {
      debugPrint('💥 Error deleting custom food: $e');
      return false;
    }
  }

  /// Get training statistics
  static Future<TrainingStatistics> getTrainingStatistics() async {
    try {
      final customFoods = await _getCustomFoods();

      final totalFoods = customFoods.length;
      final totalExamples = customFoods.fold<int>(
        0,
        (sum, food) => sum + food.trainingFeatures.length,
      );

      final recentlyTrained =
          customFoods.where((food) {
            final daysSinceTraining =
                DateTime.now().difference(food.lastTrained).inDays;
            return daysSinceTraining <= 7;
          }).length;

      return TrainingStatistics(
        totalCustomFoods: totalFoods,
        totalTrainingExamples: totalExamples,
        recentlyTrainedFoods: recentlyTrained,
        averageExamplesPerFood:
            totalFoods > 0 ? totalExamples / totalFoods : 0.0,
      );
    } catch (e) {
      debugPrint('💥 Error getting training statistics: $e');
      return TrainingStatistics.empty();
    }
  }

  /// Extract features for training
  static Future<ComprehensiveImageFeatures> _extractTrainingFeatures(
    Uint8List imageBytes,
  ) async {
    // Simplified feature extraction for training
    // In a real implementation, this would use proper image processing

    int redSum = 0, greenSum = 0, blueSum = 0;
    int pixelCount = imageBytes.length ~/ 3;

    for (int i = 0; i < imageBytes.length - 2; i += 3) {
      redSum += imageBytes[i];
      greenSum += imageBytes[i + 1];
      blueSum += imageBytes[i + 2];
    }

    final avgRed = redSum / pixelCount;
    final avgGreen = greenSum / pixelCount;
    final avgBlue = blueSum / pixelCount;
    final brightness = (avgRed + avgGreen + avgBlue) / 3;

    // Simplified color features
    final colorFeatures = ColorFeatures(
      avgRed: avgRed,
      avgGreen: avgGreen,
      avgBlue: avgBlue,
      redStdDev: 0, // Simplified
      greenStdDev: 0,
      blueStdDev: 0,
      dominantColor: _getDominantColor(avgRed, avgGreen, avgBlue),
      brightness: brightness,
      saturation: 0.5, // Simplified
    );

    // Simplified texture features
    final textureFeatures = TextureFeatures(
      smoothness: 0.5,
      uniformity: 0.5,
      graininess: 0.5,
    );

    // Simplified shape features
    final shapeFeatures = ShapeFeatures(
      roundness: 0.5,
      elongation: 0.5,
      compactness: 0.5,
    );

    // Simplified size features
    final sizeFeatures = SizeFeatures(
      totalPixels: pixelCount,
      estimatedArea: pixelCount * 0.1,
    );

    return ComprehensiveImageFeatures(
      colorFeatures: colorFeatures,
      textureFeatures: textureFeatures,
      shapeFeatures: shapeFeatures,
      sizeFeatures: sizeFeatures,
    );
  }

  /// Get dominant color from RGB values
  static String _getDominantColor(double r, double g, double b) {
    if (r > g && r > b) return 'red';
    if (g > r && g > b) return 'green';
    if (b > r && b > g) return 'blue';
    if (r > 200 && g > 200 && b > 200) return 'white';
    if (r < 50 && g < 50 && b < 50) return 'black';
    if (r > 150 && g > 100 && b < 100) return 'brown';
    if (r > 200 && g > 200 && b < 150) return 'yellow';
    return 'mixed';
  }

  /// Create custom pattern from training data
  static Future<CustomFoodPattern> _createCustomPattern(
    String foodName,
    List<ComprehensiveImageFeatures> trainingFeatures,
    FoodEntry nutritionData,
    String? description,
    List<String>? tags,
  ) async {
    // Calculate average features from training examples
    final avgColorFeatures = _calculateAverageColorFeatures(trainingFeatures);
    final avgTextureFeatures = _calculateAverageTextureFeatures(
      trainingFeatures,
    );
    final avgShapeFeatures = _calculateAverageShapeFeatures(trainingFeatures);

    // Calculate feature ranges for matching
    final colorRanges = _calculateColorRanges(trainingFeatures);
    final textureRanges = _calculateTextureRanges(trainingFeatures);

    return CustomFoodPattern(
      id: 'custom_${DateTime.now().millisecondsSinceEpoch}',
      name: foodName,
      description: description ?? '',
      tags: tags ?? [],
      trainingFeatures: trainingFeatures,
      avgColorFeatures: avgColorFeatures,
      avgTextureFeatures: avgTextureFeatures,
      avgShapeFeatures: avgShapeFeatures,
      colorRanges: colorRanges,
      textureRanges: textureRanges,
      nutritionData: nutritionData,
      confidence: 0.9, // High confidence for custom trained foods
      createdAt: DateTime.now(),
      lastTrained: DateTime.now(),
    );
  }

  /// Calculate pattern matching score for custom foods
  static double _calculateCustomPatternScore(
    ComprehensiveImageFeatures features,
    CustomFoodPattern pattern,
  ) {
    // Color matching with ranges
    double colorScore = 0.0;
    for (final range in pattern.colorRanges) {
      if (_isColorInCustomRange(features.colorFeatures, range)) {
        colorScore = 0.9;
        break;
      }
    }

    // Texture matching
    final textureScore = _calculateCustomTextureScore(
      features.textureFeatures,
      pattern.avgTextureFeatures,
      pattern.textureRanges,
    );

    // Shape matching
    final shapeScore = _calculateCustomShapeScore(
      features.shapeFeatures,
      pattern.avgShapeFeatures,
    );

    // Weighted average with higher weight on color for custom foods
    final totalScore =
        (colorScore * 0.6) + (textureScore * 0.25) + (shapeScore * 0.15);

    return totalScore * pattern.confidence;
  }

  /// Calculate average color features
  static ColorFeatures _calculateAverageColorFeatures(
    List<ComprehensiveImageFeatures> features,
  ) {
    if (features.isEmpty) {
      return const ColorFeatures(
        avgRed: 128,
        avgGreen: 128,
        avgBlue: 128,
        redStdDev: 0,
        greenStdDev: 0,
        blueStdDev: 0,
        dominantColor: 'mixed',
        brightness: 128,
        saturation: 0.5,
      );
    }

    final avgRed =
        features.fold<double>(0, (sum, f) => sum + f.colorFeatures.avgRed) /
        features.length;
    final avgGreen =
        features.fold<double>(0, (sum, f) => sum + f.colorFeatures.avgGreen) /
        features.length;
    final avgBlue =
        features.fold<double>(0, (sum, f) => sum + f.colorFeatures.avgBlue) /
        features.length;
    final brightness =
        features.fold<double>(0, (sum, f) => sum + f.colorFeatures.brightness) /
        features.length;
    final saturation =
        features.fold<double>(0, (sum, f) => sum + f.colorFeatures.saturation) /
        features.length;

    // Find most common dominant color
    final colorCounts = <String, int>{};
    for (final feature in features) {
      colorCounts[feature.colorFeatures.dominantColor] =
          (colorCounts[feature.colorFeatures.dominantColor] ?? 0) + 1;
    }
    final dominantColor =
        colorCounts.entries.reduce((a, b) => a.value > b.value ? a : b).key;

    return ColorFeatures(
      avgRed: avgRed,
      avgGreen: avgGreen,
      avgBlue: avgBlue,
      redStdDev: 0, // Simplified for now
      greenStdDev: 0,
      blueStdDev: 0,
      dominantColor: dominantColor,
      brightness: brightness,
      saturation: saturation,
    );
  }

  /// Calculate average texture features
  static TextureFeatures _calculateAverageTextureFeatures(
    List<ComprehensiveImageFeatures> features,
  ) {
    if (features.isEmpty) {
      return const TextureFeatures(
        smoothness: 0.5,
        uniformity: 0.5,
        graininess: 0.5,
      );
    }

    final smoothness =
        features.fold<double>(
          0,
          (sum, f) => sum + f.textureFeatures.smoothness,
        ) /
        features.length;
    final uniformity =
        features.fold<double>(
          0,
          (sum, f) => sum + f.textureFeatures.uniformity,
        ) /
        features.length;
    final graininess =
        features.fold<double>(
          0,
          (sum, f) => sum + f.textureFeatures.graininess,
        ) /
        features.length;

    return TextureFeatures(
      smoothness: smoothness,
      uniformity: uniformity,
      graininess: graininess,
    );
  }

  /// Calculate average shape features
  static ShapeFeatures _calculateAverageShapeFeatures(
    List<ComprehensiveImageFeatures> features,
  ) {
    if (features.isEmpty) {
      return const ShapeFeatures(
        roundness: 0.5,
        elongation: 0.5,
        compactness: 0.5,
      );
    }

    final roundness =
        features.fold<double>(0, (sum, f) => sum + f.shapeFeatures.roundness) /
        features.length;
    final elongation =
        features.fold<double>(0, (sum, f) => sum + f.shapeFeatures.elongation) /
        features.length;
    final compactness =
        features.fold<double>(
          0,
          (sum, f) => sum + f.shapeFeatures.compactness,
        ) /
        features.length;

    return ShapeFeatures(
      roundness: roundness,
      elongation: elongation,
      compactness: compactness,
    );
  }

  /// Calculate color ranges from training examples
  static List<CustomColorRange> _calculateColorRanges(
    List<ComprehensiveImageFeatures> features,
  ) {
    if (features.isEmpty) return [];

    final redValues = features.map((f) => f.colorFeatures.avgRed).toList();
    final greenValues = features.map((f) => f.colorFeatures.avgGreen).toList();
    final blueValues = features.map((f) => f.colorFeatures.avgBlue).toList();

    redValues.sort();
    greenValues.sort();
    blueValues.sort();

    // Use range from min to max with some tolerance
    final tolerance = 30.0;

    return [
      CustomColorRange(
        minR: (redValues.first - tolerance).clamp(0, 255),
        maxR: (redValues.last + tolerance).clamp(0, 255),
        minG: (greenValues.first - tolerance).clamp(0, 255),
        maxG: (greenValues.last + tolerance).clamp(0, 255),
        minB: (blueValues.first - tolerance).clamp(0, 255),
        maxB: (blueValues.last + tolerance).clamp(0, 255),
      ),
    ];
  }

  /// Calculate texture ranges
  static CustomTextureRange _calculateTextureRanges(
    List<ComprehensiveImageFeatures> features,
  ) {
    if (features.isEmpty) {
      return const CustomTextureRange(
        smoothnessRange: 0.2,
        uniformityRange: 0.2,
        graininesRange: 0.2,
      );
    }

    // Calculate standard deviations for ranges
    // Note: In a real implementation, this would calculate actual ranges
    // based on the variance of texture features across training examples

    return const CustomTextureRange(
      smoothnessRange: 0.2, // Simplified tolerance
      uniformityRange: 0.2,
      graininesRange: 0.2,
    );
  }

  /// Check if color is in custom range
  static bool _isColorInCustomRange(
    ColorFeatures color,
    CustomColorRange range,
  ) {
    return color.avgRed >= range.minR &&
        color.avgRed <= range.maxR &&
        color.avgGreen >= range.minG &&
        color.avgGreen <= range.maxG &&
        color.avgBlue >= range.minB &&
        color.avgBlue <= range.maxB;
  }

  /// Calculate custom texture score
  static double _calculateCustomTextureScore(
    TextureFeatures actual,
    TextureFeatures expected,
    CustomTextureRange ranges,
  ) {
    final smoothnessDiff = (actual.smoothness - expected.smoothness).abs();
    final uniformityDiff = (actual.uniformity - expected.uniformity).abs();
    final graininessDiff = (actual.graininess - expected.graininess).abs();

    final smoothnessScore =
        smoothnessDiff <= ranges.smoothnessRange
            ? 1.0
            : (1.0 - (smoothnessDiff - ranges.smoothnessRange)).clamp(0.0, 1.0);
    final uniformityScore =
        uniformityDiff <= ranges.uniformityRange
            ? 1.0
            : (1.0 - (uniformityDiff - ranges.uniformityRange)).clamp(0.0, 1.0);
    final graininessScore =
        graininessDiff <= ranges.graininesRange
            ? 1.0
            : (1.0 - (graininessDiff - ranges.graininesRange)).clamp(0.0, 1.0);

    return (smoothnessScore + uniformityScore + graininessScore) / 3.0;
  }

  /// Calculate custom shape score
  static double _calculateCustomShapeScore(
    ShapeFeatures actual,
    ShapeFeatures expected,
  ) {
    final roundnessDiff = (actual.roundness - expected.roundness).abs();
    final elongationDiff = (actual.elongation - expected.elongation).abs();
    final compactnessDiff = (actual.compactness - expected.compactness).abs();

    return 1.0 - ((roundnessDiff + elongationDiff + compactnessDiff) / 3.0);
  }

  /// Create food entry from custom pattern
  static FoodEntry _createFoodEntryFromCustomPattern(
    CustomFoodPattern pattern,
    MealType mealType,
    double confidence,
  ) {
    return pattern.nutritionData.copyWith(
      id: 'custom_recognized_${DateTime.now().millisecondsSinceEpoch}',
      mealType: mealType,
      timestamp: DateTime.now(),
      notes: 'Custom trained food - confidence: ${(confidence * 100).toInt()}%',
      updatedAt: DateTime.now(),
    );
  }

  // Data persistence methods
  static Future<List<CustomFoodPattern>> _getCustomFoods() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final customFoodsJson = prefs.getString(_customFoodsKey);

      if (customFoodsJson != null) {
        final List<dynamic> foodsList = jsonDecode(customFoodsJson);
        return foodsList
            .map((json) => CustomFoodPattern.fromJson(json))
            .toList();
      }

      return [];
    } catch (e) {
      debugPrint('💥 Error getting custom foods: $e');
      return [];
    }
  }

  static Future<void> _saveCustomFood(CustomFoodPattern customFood) async {
    final customFoods = await _getCustomFoods();
    customFoods.add(customFood);
    await _saveCustomFoods(customFoods);
  }

  static Future<void> _saveCustomFoods(
    List<CustomFoodPattern> customFoods,
  ) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final customFoodsJson = jsonEncode(
        customFoods.map((food) => food.toJson()).toList(),
      );
      await prefs.setString(_customFoodsKey, customFoodsJson);
    } catch (e) {
      debugPrint('💥 Error saving custom foods: $e');
    }
  }
}

// Data classes for custom food training
class CustomFoodPattern {
  final String id;
  final String name;
  final String description;
  final List<String> tags;
  final List<ComprehensiveImageFeatures> trainingFeatures;
  final ColorFeatures avgColorFeatures;
  final TextureFeatures avgTextureFeatures;
  final ShapeFeatures avgShapeFeatures;
  final List<CustomColorRange> colorRanges;
  final CustomTextureRange textureRanges;
  final FoodEntry nutritionData;
  final double confidence;
  final DateTime createdAt;
  final DateTime lastTrained;

  const CustomFoodPattern({
    required this.id,
    required this.name,
    required this.description,
    required this.tags,
    required this.trainingFeatures,
    required this.avgColorFeatures,
    required this.avgTextureFeatures,
    required this.avgShapeFeatures,
    required this.colorRanges,
    required this.textureRanges,
    required this.nutritionData,
    required this.confidence,
    required this.createdAt,
    required this.lastTrained,
  });

  CustomFoodPattern copyWith({
    String? id,
    String? name,
    String? description,
    List<String>? tags,
    List<ComprehensiveImageFeatures>? trainingFeatures,
    ColorFeatures? avgColorFeatures,
    TextureFeatures? avgTextureFeatures,
    ShapeFeatures? avgShapeFeatures,
    List<CustomColorRange>? colorRanges,
    CustomTextureRange? textureRanges,
    FoodEntry? nutritionData,
    double? confidence,
    DateTime? createdAt,
    DateTime? lastTrained,
  }) {
    return CustomFoodPattern(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      tags: tags ?? this.tags,
      trainingFeatures: trainingFeatures ?? this.trainingFeatures,
      avgColorFeatures: avgColorFeatures ?? this.avgColorFeatures,
      avgTextureFeatures: avgTextureFeatures ?? this.avgTextureFeatures,
      avgShapeFeatures: avgShapeFeatures ?? this.avgShapeFeatures,
      colorRanges: colorRanges ?? this.colorRanges,
      textureRanges: textureRanges ?? this.textureRanges,
      nutritionData: nutritionData ?? this.nutritionData,
      confidence: confidence ?? this.confidence,
      createdAt: createdAt ?? this.createdAt,
      lastTrained: lastTrained ?? this.lastTrained,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'tags': tags,
      'nutritionData': nutritionData.toJson(),
      'confidence': confidence,
      'createdAt': createdAt.toIso8601String(),
      'lastTrained': lastTrained.toIso8601String(),
      // Note: Simplified JSON - in production, would need to serialize all features
    };
  }

  factory CustomFoodPattern.fromJson(Map<String, dynamic> json) {
    return CustomFoodPattern(
      id: json['id'],
      name: json['name'],
      description: json['description'] ?? '',
      tags: List<String>.from(json['tags'] ?? []),
      trainingFeatures: [], // Simplified - would need proper deserialization
      avgColorFeatures: const ColorFeatures(
        avgRed: 128,
        avgGreen: 128,
        avgBlue: 128,
        redStdDev: 0,
        greenStdDev: 0,
        blueStdDev: 0,
        dominantColor: 'mixed',
        brightness: 128,
        saturation: 0.5,
      ),
      avgTextureFeatures: const TextureFeatures(
        smoothness: 0.5,
        uniformity: 0.5,
        graininess: 0.5,
      ),
      avgShapeFeatures: const ShapeFeatures(
        roundness: 0.5,
        elongation: 0.5,
        compactness: 0.5,
      ),
      colorRanges: [],
      textureRanges: const CustomTextureRange(
        smoothnessRange: 0.2,
        uniformityRange: 0.2,
        graininesRange: 0.2,
      ),
      nutritionData: FoodEntry.fromJson(json['nutritionData']),
      confidence: json['confidence']?.toDouble() ?? 0.9,
      createdAt: DateTime.parse(json['createdAt']),
      lastTrained: DateTime.parse(json['lastTrained']),
    );
  }
}

class CustomColorRange {
  final double minR, maxR, minG, maxG, minB, maxB;

  const CustomColorRange({
    required this.minR,
    required this.maxR,
    required this.minG,
    required this.maxG,
    required this.minB,
    required this.maxB,
  });
}

class CustomTextureRange {
  final double smoothnessRange;
  final double uniformityRange;
  final double graininesRange;

  const CustomTextureRange({
    required this.smoothnessRange,
    required this.uniformityRange,
    required this.graininesRange,
  });
}

class TrainingStatistics {
  final int totalCustomFoods;
  final int totalTrainingExamples;
  final int recentlyTrainedFoods;
  final double averageExamplesPerFood;

  const TrainingStatistics({
    required this.totalCustomFoods,
    required this.totalTrainingExamples,
    required this.recentlyTrainedFoods,
    required this.averageExamplesPerFood,
  });

  factory TrainingStatistics.empty() {
    return const TrainingStatistics(
      totalCustomFoods: 0,
      totalTrainingExamples: 0,
      recentlyTrainedFoods: 0,
      averageExamplesPerFood: 0.0,
    );
  }
}
