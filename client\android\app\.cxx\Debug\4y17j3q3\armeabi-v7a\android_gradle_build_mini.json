{"buildFiles": ["C:\\flutter\\src\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Documents\\Sentebale\\GlucoMonitor\\client\\android\\app\\.cxx\\Debug\\4y17j3q3\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Documents\\Sentebale\\GlucoMonitor\\client\\android\\app\\.cxx\\Debug\\4y17j3q3\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}