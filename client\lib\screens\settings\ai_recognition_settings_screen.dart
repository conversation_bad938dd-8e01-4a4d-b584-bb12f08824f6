import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../constants/app_colors.dart';
import '../../providers/ai_recognition_settings_provider.dart';

import '../../services/custom_food_training_service.dart';
import '../../services/learning_algorithm_service.dart';
import '../../widgets/food_diary/confidence_indicator.dart';

/// Settings screen for AI recognition preferences and custom food management
class AIRecognitionSettingsScreen extends StatefulWidget {
  const AIRecognitionSettingsScreen({super.key});

  @override
  State<AIRecognitionSettingsScreen> createState() =>
      _AIRecognitionSettingsScreenState();
}

class _AIRecognitionSettingsScreenState
    extends State<AIRecognitionSettingsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  List<CustomFoodPattern> _customFoods = [];
  AccuracyMetrics? _accuracyMetrics;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadSettings();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadSettings() async {
    try {
      final customFoods = await CustomFoodTrainingService.getTrainedFoods();
      final metrics = await LearningAlgorithmService.getAccuracyMetrics();

      setState(() {
        _customFoods = customFoods;
        _accuracyMetrics = metrics;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text('AI Recognition Settings'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: const [
            Tab(icon: Icon(Icons.tune), text: 'Confidence'),
            Tab(icon: Icon(Icons.settings), text: 'Features'),
            Tab(icon: Icon(Icons.speed), text: 'Performance'),
            Tab(icon: Icon(Icons.palette), text: 'Presets'),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.restore),
            onPressed: _showResetDialog,
            tooltip: 'Reset to Defaults',
          ),
        ],
      ),
      body: Consumer<AIRecognitionSettingsProvider>(
        builder: (context, settingsProvider, child) {
          if (settingsProvider.isLoading || _isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          return TabBarView(
            controller: _tabController,
            children: [
              _buildConfidenceTab(settingsProvider),
              _buildFeaturesTab(settingsProvider),
              _buildPerformanceTab(settingsProvider),
              _buildPresetsTab(settingsProvider),
            ],
          );
        },
      ),
    );
  }

  Widget _buildConfidenceTab(AIRecognitionSettingsProvider provider) {
    final settings = provider.settings;

    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        _buildSectionHeader('Confidence Thresholds'),
        const SizedBox(height: 16),

        _buildSliderSetting(
          title: 'Minimum Confidence',
          subtitle: 'Reject results below this threshold',
          value: settings.minimumConfidence,
          min: 0.1,
          max: 0.9,
          divisions: 80,
          onChanged:
              (value) => provider.updateConfidenceThresholds(minimum: value),
        ),

        _buildSliderSetting(
          title: 'Medium Confidence',
          subtitle: 'Show warning for results below this',
          value: settings.mediumConfidenceThreshold,
          min: 0.5,
          max: 0.95,
          divisions: 45,
          onChanged:
              (value) => provider.updateConfidenceThresholds(medium: value),
        ),

        _buildSliderSetting(
          title: 'High Confidence',
          subtitle: 'Auto-accept results above this',
          value: settings.highConfidenceThreshold,
          min: 0.7,
          max: 1.0,
          divisions: 30,
          onChanged:
              (value) => provider.updateConfidenceThresholds(high: value),
        ),

        const SizedBox(height: 24),
        _buildSectionHeader('Custom Food Thresholds'),
        const SizedBox(height: 16),

        _buildCustomThresholdsList(provider),

        const SizedBox(height: 16),
        ElevatedButton.icon(
          onPressed: () => _showAddCustomThresholdDialog(provider),
          icon: const Icon(Icons.add),
          label: const Text('Add Custom Threshold'),
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.secondary,
            foregroundColor: Colors.white,
          ),
        ),
      ],
    );
  }

  Widget _buildFeaturesTab(AIRecognitionSettingsProvider provider) {
    final settings = provider.settings;

    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        _buildSectionHeader('Recognition Features'),
        const SizedBox(height: 16),

        _buildSwitchSetting(
          title: 'Offline First',
          subtitle: 'Try offline recognition before online',
          value: settings.enableOfflineFirst,
          onChanged:
              (value) => provider.updateRecognitionPreferences(
                enableOfflineFirst: value,
              ),
        ),

        _buildSwitchSetting(
          title: 'Multi-Food Detection',
          subtitle: 'Detect multiple foods in one image',
          value: settings.enableMultiDetection,
          onChanged:
              (value) => provider.updateRecognitionPreferences(
                enableMultiDetection: value,
              ),
        ),

        _buildSwitchSetting(
          title: 'Portion Estimation',
          subtitle: 'Estimate portion sizes automatically',
          value: settings.enablePortionEstimation,
          onChanged:
              (value) => provider.updateRecognitionPreferences(
                enablePortionEstimation: value,
              ),
        ),

        _buildSwitchSetting(
          title: 'Real-time Analysis',
          subtitle: 'Live camera analysis',
          value: settings.enableRealtimeAnalysis,
          onChanged:
              (value) => provider.updateRecognitionPreferences(
                enableRealtimeAnalysis: value,
              ),
        ),

        const SizedBox(height: 24),
        _buildSectionHeader('User Experience'),
        const SizedBox(height: 16),

        _buildSwitchSetting(
          title: 'Show Confidence Indicators',
          subtitle: 'Display confidence scores and colors',
          value: settings.showConfidenceIndicators,
          onChanged:
              (value) =>
                  provider.updateUXSettings(showConfidenceIndicators: value),
        ),

        _buildSwitchSetting(
          title: 'Haptic Feedback',
          subtitle: 'Vibrate on successful recognition',
          value: settings.enableHapticFeedback,
          onChanged:
              (value) => provider.updateUXSettings(enableHapticFeedback: value),
        ),

        _buildSwitchSetting(
          title: 'Auto-Save Recognized Foods',
          subtitle: 'Automatically save to food diary',
          value: settings.autoSaveRecognizedFoods,
          onChanged:
              (value) =>
                  provider.updateUXSettings(autoSaveRecognizedFoods: value),
        ),

        _buildSwitchSetting(
          title: 'Learning Mode',
          subtitle: 'Improve accuracy with user feedback',
          value: settings.enableLearningMode,
          onChanged:
              (value) => provider.updateUXSettings(enableLearningMode: value),
        ),
      ],
    );
  }

  Widget _buildPerformanceTab(AIRecognitionSettingsProvider provider) {
    final settings = provider.settings;

    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        _buildSectionHeader('Performance Settings'),
        const SizedBox(height: 16),

        _buildSliderSetting(
          title: 'Max Detection Results',
          subtitle: 'Maximum number of foods to detect',
          value: settings.maxDetectionResults.toDouble(),
          min: 1,
          max: 15,
          divisions: 14,
          isInteger: true,
          onChanged:
              (value) => provider.updatePerformanceSettings(
                maxDetectionResults: value.round(),
              ),
        ),

        _buildSliderSetting(
          title: 'Recognition Timeout',
          subtitle: 'Maximum time to wait for results (seconds)',
          value: settings.recognitionTimeout.inSeconds.toDouble(),
          min: 5,
          max: 30,
          divisions: 25,
          isInteger: true,
          onChanged:
              (value) => provider.updatePerformanceSettings(
                recognitionTimeout: Duration(seconds: value.round()),
              ),
        ),

        _buildSliderSetting(
          title: 'Image Quality Threshold',
          subtitle: 'Minimum image quality for processing',
          value: settings.imageQualityThreshold,
          min: 0.1,
          max: 1.0,
          divisions: 90,
          onChanged:
              (value) => provider.updatePerformanceSettings(
                imageQualityThreshold: value,
              ),
        ),

        const SizedBox(height: 24),
        _buildSectionHeader('Optimization'),
        const SizedBox(height: 16),

        _buildSwitchSetting(
          title: 'Image Optimization',
          subtitle: 'Compress images for faster processing',
          value: settings.enableImageOptimization,
          onChanged:
              (value) => provider.updatePerformanceSettings(
                enableImageOptimization: value,
              ),
        ),
      ],
    );
  }

  Widget _buildPresetsTab(AIRecognitionSettingsProvider provider) {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        _buildSectionHeader('Quick Presets'),
        const SizedBox(height: 16),

        _buildPresetCard(
          title: 'Default',
          description: 'Balanced settings for general use',
          icon: Icons.balance,
          color: AppColors.primary,
          onTap: () => provider.applyPreset('default'),
        ),

        const SizedBox(height: 16),

        _buildPresetCard(
          title: 'Conservative',
          description: 'Higher thresholds, more accurate results',
          icon: Icons.security,
          color: Colors.green,
          onTap: () => provider.applyPreset('conservative'),
        ),

        const SizedBox(height: 16),

        _buildPresetCard(
          title: 'Aggressive',
          description: 'Lower thresholds, more detections',
          icon: Icons.speed,
          color: Colors.orange,
          onTap: () => provider.applyPreset('aggressive'),
        ),

        const SizedBox(height: 32),
        _buildSectionHeader('Settings Summary'),
        const SizedBox(height: 16),

        _buildSettingsSummary(provider),
      ],
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: AppColors.primary,
        ),
      ),
    );
  }

  Widget _buildMetricCard(
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            label,
            style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildCustomFoodsSection() {
    if (_customFoods.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: Colors.grey[50],
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey[300]!),
        ),
        child: Column(
          children: [
            Icon(Icons.restaurant, size: 48, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'No Custom Foods Trained',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Train the AI on your specific dishes to improve recognition accuracy',
              style: TextStyle(fontSize: 14, color: Colors.grey[500]),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: _showTrainFoodDialog,
              icon: const Icon(Icons.add),
              label: const Text('Train First Food'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      );
    }

    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              '${_customFoods.length} Custom Foods',
              style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
            ),
            TextButton.icon(
              onPressed: _showTrainFoodDialog,
              icon: const Icon(Icons.add),
              label: const Text('Train New'),
              style: TextButton.styleFrom(foregroundColor: AppColors.primary),
            ),
          ],
        ),
        const SizedBox(height: 8),
        ..._customFoods.take(5).map((food) => _buildCustomFoodItem(food)),
        if (_customFoods.length > 5)
          TextButton(
            onPressed: () {
              // Navigate to full custom foods list
            },
            child: Text('View All ${_customFoods.length} Foods'),
          ),
      ],
    );
  }

  Widget _buildCustomFoodItem(CustomFoodPattern food) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(
              Icons.restaurant,
              color: AppColors.primary,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  food.name,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                if (food.description.isNotEmpty)
                  Text(
                    food.description,
                    style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                  ),
              ],
            ),
          ),
          ConfidenceIndicator(confidence: food.confidence, isCompact: true),
        ],
      ),
    );
  }

  void _showTrainFoodDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Train Custom Food'),
            content: const Text(
              'This would open the custom food training interface.\n\n'
              'You would be able to:\n'
              '• Take multiple photos of your dish\n'
              '• Add nutrition information\n'
              '• Train the AI to recognize it\n\n'
              'Feature implementation in progress!',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('OK'),
              ),
            ],
          ),
    );
  }

  Widget _buildSliderSetting({
    required String title,
    required String subtitle,
    required double value,
    required double min,
    required double max,
    required int divisions,
    required ValueChanged<double> onChanged,
    bool isInteger = false,
  }) {
    return Card(
      color: Colors.white,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: AppColors.onBackground,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: const TextStyle(
                fontSize: 14,
                color: AppColors.textSecondary,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: Slider(
                    value: value,
                    min: min,
                    max: max,
                    divisions: divisions,
                    activeColor: AppColors.primary,
                    onChanged: onChanged,
                  ),
                ),
                const SizedBox(width: 16),
                Container(
                  width: 60,
                  alignment: Alignment.centerRight,
                  child: Text(
                    isInteger
                        ? value.round().toString()
                        : '${(value * 100).round()}%',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: AppColors.primary,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSwitchSetting({
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return Card(
      color: Colors.white,
      child: ListTile(
        title: Text(
          title,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppColors.onBackground,
          ),
        ),
        subtitle: Text(
          subtitle,
          style: const TextStyle(fontSize: 14, color: AppColors.textSecondary),
        ),
        trailing: Switch(
          value: value,
          activeColor: AppColors.primary,
          onChanged: onChanged,
        ),
      ),
    );
  }

  Widget _buildPresetCard({
    required String title,
    required String description,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Card(
      color: Colors.white,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: color, size: 24),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: AppColors.onBackground,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      description,
                      style: const TextStyle(
                        fontSize: 14,
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
              const Icon(Icons.chevron_right, color: AppColors.textSecondary),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCustomThresholdsList(AIRecognitionSettingsProvider provider) {
    final customThresholds = provider.settings.customFoodThresholds;

    if (customThresholds.isEmpty) {
      return Card(
        color: Colors.white,
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Text(
            'No custom thresholds set',
            style: TextStyle(
              fontSize: 14,
              color: AppColors.textSecondary,
              fontStyle: FontStyle.italic,
            ),
          ),
        ),
      );
    }

    return Column(
      children:
          customThresholds.entries.map((entry) {
            return Card(
              color: Colors.white,
              child: ListTile(
                title: Text(
                  entry.key.toUpperCase(),
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: AppColors.onBackground,
                  ),
                ),
                subtitle: Text(
                  'Threshold: ${(entry.value * 100).round()}%',
                  style: const TextStyle(
                    fontSize: 14,
                    color: AppColors.textSecondary,
                  ),
                ),
                trailing: IconButton(
                  icon: const Icon(Icons.delete, color: Colors.red),
                  onPressed:
                      () => provider.removeCustomFoodThreshold(entry.key),
                ),
              ),
            );
          }).toList(),
    );
  }

  Widget _buildSettingsSummary(AIRecognitionSettingsProvider provider) {
    final summary = provider.getSettingsSummary();

    return Card(
      color: Colors.white,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children:
              summary.entries.map((entry) {
                return Padding(
                  padding: const EdgeInsets.symmetric(vertical: 4),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        entry.key,
                        style: const TextStyle(
                          fontSize: 14,
                          color: AppColors.textSecondary,
                        ),
                      ),
                      Text(
                        entry.value,
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: AppColors.onBackground,
                        ),
                      ),
                    ],
                  ),
                );
              }).toList(),
        ),
      ),
    );
  }

  void _showResetDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Reset Settings'),
            content: const Text(
              'Are you sure you want to reset all AI recognition settings to their default values?',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  context
                      .read<AIRecognitionSettingsProvider>()
                      .resetToDefaults();
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Settings reset to defaults'),
                      backgroundColor: AppColors.secondary,
                    ),
                  );
                },
                style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
                child: const Text(
                  'Reset',
                  style: TextStyle(color: Colors.white),
                ),
              ),
            ],
          ),
    );
  }

  void _showAddCustomThresholdDialog(AIRecognitionSettingsProvider provider) {
    final foodNameController = TextEditingController();
    double threshold = 0.7;

    showDialog(
      context: context,
      builder:
          (context) => StatefulBuilder(
            builder:
                (context, setState) => AlertDialog(
                  title: const Text('Add Custom Threshold'),
                  content: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      TextField(
                        controller: foodNameController,
                        decoration: const InputDecoration(
                          labelText: 'Food Name',
                          hintText: 'e.g., Pap, Morogo',
                        ),
                      ),
                      const SizedBox(height: 16),
                      Text('Threshold: ${(threshold * 100).round()}%'),
                      Slider(
                        value: threshold,
                        min: 0.1,
                        max: 1.0,
                        divisions: 90,
                        onChanged: (value) => setState(() => threshold = value),
                      ),
                    ],
                  ),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.of(context).pop(),
                      child: const Text('Cancel'),
                    ),
                    ElevatedButton(
                      onPressed: () {
                        if (foodNameController.text.isNotEmpty) {
                          provider.addCustomFoodThreshold(
                            foodNameController.text,
                            threshold,
                          );
                          Navigator.of(context).pop();
                        }
                      },
                      child: const Text('Add'),
                    ),
                  ],
                ),
          ),
    );
  }
}
