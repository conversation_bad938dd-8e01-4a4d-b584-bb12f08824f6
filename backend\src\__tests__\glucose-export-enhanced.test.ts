import request from 'supertest';
import app from '../index';
import User from '../models/User';
import GlucoseReading from '../models/GlucoseReading';
import jwt from 'jsonwebtoken';

describe('Enhanced Glucose Export Endpoints', () => {
    let authToken: string;
    let userId: string;

    beforeAll(async () => {
        // Create a test user
        const testUser = await User.create({
            name: 'Test User',
            email: '<EMAIL>',
            password: 'password123',
            phoneNumber: '+27123456789',
            isPhoneVerified: true
        });
        userId = (testUser._id as any).toString();

        // Generate auth token
        authToken = jwt.sign(
            { id: userId },
            process.env.JWT_SECRET || 'test-secret',
            { expiresIn: '1h' }
        );

        // Create test glucose readings
        const testReadings = [
            {
                userId: testUser._id as any,
                value: 95,
                timestamp: new Date('2024-01-01T08:00:00Z'),
                mealTiming: 'before_breakfast',
                context: 'fasting',
                isManual: true
            },
            {
                userId: testUser._id as any,
                value: 140,
                timestamp: new Date('2024-01-01T10:00:00Z'),
                mealTiming: 'after_breakfast',
                context: 'post_meal',
                isManual: true
            },
            {
                userId: testUser._id as any,
                value: 110,
                timestamp: new Date('2024-01-01T14:00:00Z'),
                mealTiming: 'before_lunch',
                context: 'fasting',
                isManual: true
            }
        ];

        await GlucoseReading.insertMany(testReadings);
    });

    afterAll(async () => {
        // Clean up test data
        await User.deleteMany({});
        await GlucoseReading.deleteMany({});
    });

    describe('GET /api/glucose/export/chart - Enhanced Export', () => {
        it('should export chart data as PDF', async () => {
            const response = await request(app)
                .get('/api/glucose/export/chart')
                .set('Authorization', `Bearer ${authToken}`)
                .query({
                    format: 'pdf',
                    chartType: 'line',
                    destination: 'download'
                });

            expect(response.status).toBe(200);
            expect(response.headers['content-type']).toContain('application/pdf');
            expect(response.headers['content-disposition']).toContain('.pdf');
        });

        it('should export chart data as Excel', async () => {
            const response = await request(app)
                .get('/api/glucose/export/chart')
                .set('Authorization', `Bearer ${authToken}`)
                .query({
                    format: 'excel',
                    chartType: 'bar',
                    destination: 'download'
                });

            expect(response.status).toBe(200);
            expect(response.headers['content-type']).toContain('spreadsheetml');
            expect(response.headers['content-disposition']).toContain('.xlsx');
        });

        it('should validate format parameter', async () => {
            const response = await request(app)
                .get('/api/glucose/export/chart')
                .set('Authorization', `Bearer ${authToken}`)
                .query({
                    format: 'invalid_format'
                });

            expect(response.status).toBe(400);
            expect(response.body.success).toBe(false);
            expect(response.body.message).toContain('Invalid format');
        });

        it('should validate destination parameter', async () => {
            const response = await request(app)
                .get('/api/glucose/export/chart')
                .set('Authorization', `Bearer ${authToken}`)
                .query({
                    destination: 'invalid_destination'
                });

            expect(response.status).toBe(400);
            expect(response.body.success).toBe(false);
            expect(response.body.message).toContain('Invalid destination');
        });

        it('should require email when destination is email', async () => {
            const response = await request(app)
                .get('/api/glucose/export/chart')
                .set('Authorization', `Bearer ${authToken}`)
                .query({
                    destination: 'email'
                });

            expect(response.status).toBe(400);
            expect(response.body.success).toBe(false);
            expect(response.body.message).toContain('Email address is required');
        });
    });

    describe('POST /api/glucose/export/bulk - Bulk Export', () => {
        it('should perform bulk export with multiple formats', async () => {
            const response = await request(app)
                .post('/api/glucose/export/bulk')
                .set('Authorization', `Bearer ${authToken}`)
                .send({
                    chartType: 'line',
                    formats: ['json', 'csv'],
                    destinations: ['download'],
                    includeAllFormats: false
                });

            expect(response.status).toBe(200);
            expect(response.body.success).toBe(true);
            expect(response.body.data.totalExports).toBe(2);
            expect(response.body.data.exports).toHaveLength(2);
        });

        it('should validate chart type in bulk export', async () => {
            const response = await request(app)
                .post('/api/glucose/export/bulk')
                .set('Authorization', `Bearer ${authToken}`)
                .send({
                    chartType: 'invalid_chart_type',
                    formats: ['json'],
                    destinations: ['download']
                });

            expect(response.status).toBe(400);
            expect(response.body.success).toBe(false);
            expect(response.body.message).toContain('Invalid chart type');
        });

        it('should handle includeAllFormats option', async () => {
            const response = await request(app)
                .post('/api/glucose/export/bulk')
                .set('Authorization', `Bearer ${authToken}`)
                .send({
                    chartType: 'line',
                    destinations: ['download'],
                    includeAllFormats: true
                });

            expect(response.status).toBe(200);
            expect(response.body.success).toBe(true);
            expect(response.body.data.totalExports).toBe(4); // json, csv, pdf, excel
        });
    });
});
