import { Request, Response, NextFunction } from 'express';
import { v4 as uuidv4 } from 'uuid';

/**
 * Middleware to add unique request ID to each request
 * This helps with request tracing and logging correlation
 */
export const requestIdMiddleware = (req: Request, res: Response, next: NextFunction): void => {
    // Check if request ID already exists in headers
    let requestId = req.headers['x-request-id'] as string;
    
    // Generate new request ID if not provided
    if (!requestId) {
        requestId = `req_${Date.now()}_${uuidv4().substring(0, 8)}`;
    }
    
    // Set request ID in headers for both request and response
    req.headers['x-request-id'] = requestId;
    res.setHeader('X-Request-ID', requestId);
    
    // Add request ID to request object for easy access
    (req as any).requestId = requestId;
    
    next();
};

export default requestIdMiddleware;
