# Environment variables
.env
.env.*
!.env.example
.env.local
.env.production
.env.staging
.env.docker

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# nyc test coverage
.nyc_output

# Dependency directories
node_modules/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Build directories
dist/
build/

# Google Service Account Keys and Sensitive Config
config/google/service-account-key.json
config/google/*.json
backend/config/google-service-account.json

# Export files and uploads
uploads/exports/*.pdf
uploads/exports/*.xlsx
uploads/exports/*.csv
uploads/exports/*.json
uploads/exports/*
!uploads/exports/README.md

# Temporary export files
temp/exports/
backend/temp/exports/

# Docker environment file (contains sensitive data)
.env.docker
