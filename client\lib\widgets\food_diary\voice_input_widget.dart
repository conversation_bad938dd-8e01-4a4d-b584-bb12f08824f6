import 'package:flutter/material.dart';
import '../../constants/app_colors.dart';
import '../../models/food_entry.dart';
import '../../services/voice_input_service.dart';

class VoiceInputWidget extends StatefulWidget {
  final MealType mealType;
  final Function(FoodEntry) onFoodAdded;

  const VoiceInputWidget({
    super.key,
    required this.mealType,
    required this.onFoodAdded,
  });

  @override
  State<VoiceInputWidget> createState() => _VoiceInputWidgetState();
}

class _VoiceInputWidgetState extends State<VoiceInputWidget>
    with TickerProviderStateMixin {
  bool _isListening = false;
  bool _isProcessing = false;
  String _recognizedText = '';
  List<FoodEntry> _recognizedFoods = [];
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(begin: 1.0, end: 1.2).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          if (!_isListening && !_isProcessing && _recognizedFoods.isEmpty)
            _buildInitialState(),
          if (_isListening) _buildListeningState(),
          if (_isProcessing) _buildProcessingState(),
          if (_recognizedFoods.isNotEmpty) _buildResultsState(),
        ],
      ),
    );
  }

  Widget _buildInitialState() {
    return Expanded(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.mic, size: 80, color: AppColors.primary),
          const SizedBox(height: 24),
          const Text(
            'Voice Input',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.w600,
              color: AppColors.onSurface,
            ),
          ),
          const SizedBox(height: 12),
          const Text(
            'Speak your food items for hands-free logging',
            textAlign: TextAlign.center,
            style: TextStyle(fontSize: 16, color: AppColors.textSecondary),
          ),
          const SizedBox(height: 32),

          // Example phrases
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.background,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Try saying:',
                  style: TextStyle(
                    fontWeight: FontWeight.w600,
                    color: AppColors.onSurface,
                  ),
                ),
                const SizedBox(height: 8),
                ...VoiceInputService.getVoiceInputSuggestions()
                    .take(3)
                    .map(
                      (suggestion) => Padding(
                        padding: const EdgeInsets.symmetric(vertical: 2),
                        child: Text(
                          '• "$suggestion"',
                          style: const TextStyle(
                            color: AppColors.textSecondary,
                            fontStyle: FontStyle.italic,
                          ),
                        ),
                      ),
                    ),
              ],
            ),
          ),
          const SizedBox(height: 32),

          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: _startListening,
              icon: const Icon(Icons.mic),
              label: const Text('Start Speaking'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildListeningState() {
    return Expanded(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          AnimatedBuilder(
            animation: _pulseAnimation,
            builder: (context, child) {
              return Transform.scale(
                scale: _pulseAnimation.value,
                child: Container(
                  width: 120,
                  height: 120,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: AppColors.primary.withValues(alpha: 0.2),
                  ),
                  child: const Icon(
                    Icons.mic,
                    size: 60,
                    color: AppColors.primary,
                  ),
                ),
              );
            },
          ),
          const SizedBox(height: 24),
          const Text(
            'Listening...',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.w600,
              color: AppColors.primary,
            ),
          ),
          const SizedBox(height: 12),
          const Text(
            'Speak clearly and mention your food items',
            textAlign: TextAlign.center,
            style: TextStyle(fontSize: 16, color: AppColors.textSecondary),
          ),
          if (_recognizedText.isNotEmpty) ...[
            const SizedBox(height: 24),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppColors.background,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                _recognizedText,
                style: const TextStyle(
                  fontSize: 16,
                  color: AppColors.onSurface,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ],
          const SizedBox(height: 32),
          OutlinedButton.icon(
            onPressed: _stopListening,
            icon: const Icon(Icons.stop),
            label: const Text('Stop Listening'),
            style: OutlinedButton.styleFrom(
              foregroundColor: AppColors.primary,
              side: const BorderSide(color: AppColors.primary),
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProcessingState() {
    return const Expanded(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(color: AppColors.primary),
          SizedBox(height: 24),
          Text(
            'Processing...',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.w600,
              color: AppColors.onSurface,
            ),
          ),
          SizedBox(height: 12),
          Text(
            'Searching for your foods',
            style: TextStyle(fontSize: 16, color: AppColors.textSecondary),
          ),
        ],
      ),
    );
  }

  Widget _buildResultsState() {
    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.check_circle, color: Colors.green),
              const SizedBox(width: 8),
              const Text(
                'Found Foods:',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: AppColors.onSurface,
                ),
              ),
              const Spacer(),
              TextButton.icon(
                onPressed: _reset,
                icon: const Icon(Icons.refresh, size: 18),
                label: const Text('Try Again'),
              ),
            ],
          ),
          const SizedBox(height: 16),

          Expanded(
            child: ListView.builder(
              itemCount: _recognizedFoods.length,
              itemBuilder: (context, index) {
                final food = _recognizedFoods[index];
                return Card(
                  margin: const EdgeInsets.only(bottom: 8),
                  child: ListTile(
                    leading: CircleAvatar(
                      backgroundColor: AppColors.primary.withValues(alpha: 0.1),
                      child: Icon(
                        _getFoodIcon(food.category),
                        color: AppColors.primary,
                        size: 20,
                      ),
                    ),
                    title: Text(
                      food.name,
                      style: const TextStyle(
                        fontWeight: FontWeight.w500,
                        color: AppColors.onSurface,
                      ),
                    ),
                    subtitle: Text(
                      '${food.calories.toInt()} cal • ${food.carbohydrates.toInt()}g carbs',
                      style: const TextStyle(color: AppColors.textSecondary),
                    ),
                    trailing: ElevatedButton(
                      onPressed: () => widget.onFoodAdded(food),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primary,
                        foregroundColor: Colors.white,
                        minimumSize: const Size(60, 36),
                      ),
                      child: const Text('Add'),
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  IconData _getFoodIcon(FoodCategory category) {
    switch (category) {
      case FoodCategory.fruits:
        return Icons.apple;
      case FoodCategory.vegetables:
        return Icons.eco;
      case FoodCategory.grains:
        return Icons.grain;
      case FoodCategory.proteins:
        return Icons.egg;
      case FoodCategory.dairy:
        return Icons.local_drink;
      case FoodCategory.beverages:
        return Icons.local_cafe;
      case FoodCategory.fats:
        return Icons.opacity;
      case FoodCategory.sweets:
        return Icons.cookie;
      case FoodCategory.other:
        return Icons.fastfood;
    }
  }

  Future<void> _startListening() async {
    setState(() {
      _isListening = true;
      _recognizedText = '';
    });

    _pulseController.repeat(reverse: true);

    try {
      final result = await VoiceInputService.startListening();

      if (result != null && result.isNotEmpty) {
        setState(() {
          _recognizedText = result;
          _isListening = false;
          _isProcessing = true;
        });

        _pulseController.stop();

        // Search for foods based on voice input
        final foods = await VoiceInputService.searchFoodsFromVoice(result);

        setState(() {
          _recognizedFoods = foods;
          _isProcessing = false;
        });
      } else {
        _reset();
        _showErrorMessage('No speech detected. Please try again.');
      }
    } catch (e) {
      _reset();
      _showErrorMessage('Voice recognition failed: $e');
    }
  }

  Future<void> _stopListening() async {
    await VoiceInputService.stopListening();
    _pulseController.stop();
    setState(() => _isListening = false);
  }

  void _reset() {
    setState(() {
      _isListening = false;
      _isProcessing = false;
      _recognizedText = '';
      _recognizedFoods.clear();
    });
    _pulseController.stop();
  }

  void _showErrorMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.red),
    );
  }
}
