import * as cron from 'node-cron';
import mongoose from 'mongoose';
import MedicationR<PERSON>inder, { ReminderStatus } from '../models/MedicationReminder';
import Medication from '../models/Medication';
import User from '../models/User';
import { sendSMS } from './messaging';

// Interface for notification data
interface NotificationData {
    userId: string;
    medicationName: string;
    dosage: string;
    scheduledTime: Date;
    reminderMinutesBefore: number;
    phoneNumber: string;
    userName?: string;
}

// Interface for notification result
interface NotificationResult {
    success: boolean;
    notificationId?: string;
    error?: string;
}

// Notification service class
export class MedicationNotificationService {
    private static instance: MedicationNotificationService;
    private isRunning: boolean = false;
    private cronJob: cron.ScheduledTask | null = null;

    private constructor() {}

    public static getInstance(): MedicationNotificationService {
        if (!MedicationNotificationService.instance) {
            MedicationNotificationService.instance = new MedicationNotificationService();
        }
        return MedicationNotificationService.instance;
    }

    // Start the notification service
    public start(): void {
        if (this.isRunning) {
            console.log('Medication notification service is already running');
            return;
        }

        // Don't start in test environment
        if (process.env.NODE_ENV === 'test') {
            console.log('⚠️ Medication notification service disabled in test environment');
            return;
        }

        // Run every minute to check for pending notifications
        this.cronJob = cron.schedule('* * * * *', async () => {
            await this.checkAndSendNotifications();
        }, {
            timezone: 'Africa/Johannesburg'
        });

        this.isRunning = true;
        console.log('✅ Medication notification service started');
    }

    // Stop the notification service
    public stop(): void {
        if (this.cronJob) {
            this.cronJob.stop();
            this.cronJob = null;
        }
        this.isRunning = false;
        console.log('🛑 Medication notification service stopped');
    }

    // Check for pending notifications and send them
    private async checkAndSendNotifications(): Promise<void> {
        try {
            const now = new Date();
            const fiveMinutesFromNow = new Date(now.getTime() + 5 * 60 * 1000);

            // Find reminders that need notifications
            const pendingReminders = await MedicationReminder.find({
                status: ReminderStatus.PENDING,
                notificationSent: false,
                scheduledTime: {
                    $gte: now,
                    $lte: fiveMinutesFromNow
                }
            }).populate([
                {
                    path: 'medicationId',
                    select: 'name brandName dosage dosageUnit reminderMinutesBefore reminderEnabled'
                },
                {
                    path: 'userId',
                    select: 'name phoneNumber isPhoneVerified'
                }
            ]);

            for (const reminder of pendingReminders) {
                const medication = reminder.medicationId as any;
                const user = reminder.userId as any;

                // Skip if medication doesn't have reminders enabled
                if (!medication?.reminderEnabled) {
                    continue;
                }

                // Skip if user phone is not verified
                if (!user?.isPhoneVerified || !user?.phoneNumber) {
                    continue;
                }

                // Calculate notification time
                const notificationTime = new Date(
                    reminder.scheduledTime.getTime() - (medication.reminderMinutesBefore || 15) * 60 * 1000
                );

                // Check if it's time to send the notification
                if (now >= notificationTime) {
                    const notificationData: NotificationData = {
                        userId: user._id.toString(),
                        medicationName: medication.brandName || medication.name,
                        dosage: `${medication.dosage} ${medication.dosageUnit}`,
                        scheduledTime: reminder.scheduledTime,
                        reminderMinutesBefore: medication.reminderMinutesBefore || 15,
                        phoneNumber: user.phoneNumber,
                        userName: user.name
                    };

                    const result = await this.sendMedicationReminder(notificationData);

                    // Update reminder with notification status
                    await MedicationReminder.findByIdAndUpdate(reminder._id, {
                        notificationSent: true,
                        notificationId: result.notificationId || undefined
                    });

                    if (result.success) {
                        console.log(`✅ Medication reminder sent to ${user.phoneNumber} for ${medication.name}`);
                    } else {
                        console.error(`❌ Failed to send medication reminder: ${result.error}`);
                    }
                }
            }
        } catch (error) {
            console.error('Error checking medication notifications:', error);
        }
    }

    // Send medication reminder notification
    private async sendMedicationReminder(data: NotificationData): Promise<NotificationResult> {
        try {
            const timeString = data.scheduledTime.toLocaleTimeString('en-ZA', {
                hour: '2-digit',
                minute: '2-digit',
                timeZone: 'Africa/Johannesburg'
            });

            const message = this.buildReminderMessage(data, timeString);

            // Send SMS notification
            const smsResult = await sendSMS(data.phoneNumber, message);

            if (smsResult.success) {
                return {
                    success: true,
                    notificationId: smsResult.messageId
                };
            } else {
                return {
                    success: false,
                    error: smsResult.error || 'Failed to send SMS'
                };
            }
        } catch (error) {
            console.error('Error sending medication reminder:', error);
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error'
            };
        }
    }

    // Build reminder message
    private buildReminderMessage(data: NotificationData, timeString: string): string {
        const greeting = data.userName ? `Hi ${data.userName}` : 'Hi';
        
        return `${greeting}, it's time for your medication reminder!\n\n` +
               `💊 Medication: ${data.medicationName}\n` +
               `📏 Dosage: ${data.dosage}\n` +
               `⏰ Scheduled: ${timeString}\n\n` +
               `Please take your medication as prescribed. Stay healthy! 🌟\n\n` +
               `- GlucoMonitor`;
    }

    // Send immediate notification for a specific reminder
    public async sendImmediateNotification(reminderId: string): Promise<NotificationResult> {
        try {
            const reminder = await MedicationReminder.findById(reminderId).populate([
                {
                    path: 'medicationId',
                    select: 'name brandName dosage dosageUnit reminderMinutesBefore'
                },
                {
                    path: 'userId',
                    select: 'name phoneNumber isPhoneVerified'
                }
            ]);

            if (!reminder) {
                return { success: false, error: 'Reminder not found' };
            }

            const medication = reminder.medicationId as any;
            const user = reminder.userId as any;

            if (!user?.isPhoneVerified || !user?.phoneNumber) {
                return { success: false, error: 'User phone not verified' };
            }

            const notificationData: NotificationData = {
                userId: user._id.toString(),
                medicationName: medication.brandName || medication.name,
                dosage: `${medication.dosage} ${medication.dosageUnit}`,
                scheduledTime: reminder.scheduledTime,
                reminderMinutesBefore: medication.reminderMinutesBefore || 15,
                phoneNumber: user.phoneNumber,
                userName: user.name
            };

            const result = await this.sendMedicationReminder(notificationData);

            // Update reminder with notification status
            if (result.success) {
                await MedicationReminder.findByIdAndUpdate(reminderId, {
                    notificationSent: true,
                    notificationId: result.notificationId
                });
            }

            return result;
        } catch (error) {
            console.error('Error sending immediate notification:', error);
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error'
            };
        }
    }

    // Get notification service status
    public getStatus(): { isRunning: boolean; nextCheck?: Date } {
        return {
            isRunning: this.isRunning,
            nextCheck: this.cronJob ? new Date(Date.now() + 60000) : undefined
        };
    }

    // Schedule notification for a specific reminder
    public async scheduleNotification(reminderId: string): Promise<boolean> {
        try {
            const reminder = await MedicationReminder.findById(reminderId);
            if (!reminder) {
                return false;
            }

            // Reset notification status to allow re-sending
            await MedicationReminder.findByIdAndUpdate(reminderId, {
                notificationSent: false,
                notificationId: undefined
            });

            return true;
        } catch (error) {
            console.error('Error scheduling notification:', error);
            return false;
        }
    }

    // Cancel notification for a specific reminder
    public async cancelNotification(reminderId: string): Promise<boolean> {
        try {
            await MedicationReminder.findByIdAndUpdate(reminderId, {
                notificationSent: true // Mark as sent to prevent sending
            });

            return true;
        } catch (error) {
            console.error('Error canceling notification:', error);
            return false;
        }
    }
}

// Export singleton instance
export const medicationNotificationService = MedicationNotificationService.getInstance();

// Helper function to start the service
export const startMedicationNotificationService = (): void => {
    medicationNotificationService.start();
};

// Helper function to stop the service
export const stopMedicationNotificationService = (): void => {
    medicationNotificationService.stop();
};
