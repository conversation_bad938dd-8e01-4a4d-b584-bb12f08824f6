const express = require('express');

const app = express();
const PORT = 5001; // Use different port

// Simple test route
app.get('/test', (req, res) => {
    console.log('Test route hit!');
    res.json({ success: true, message: 'Test server working!' });
});

app.get('/api/food-diary/test', (req, res) => {
    console.log('Food diary test route hit!');
    res.json({ success: true, message: 'Food diary test working!' });
});

// 404 handler
app.use((req, res) => {
    res.status(404).json({ success: false, message: `Route ${req.originalUrl} not found` });
});

app.listen(PORT, () => {
    console.log(`Test server running on port ${PORT}`);
});
