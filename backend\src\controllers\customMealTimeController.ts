import { Request, Response } from 'express';
import { CustomMealTime, ICustomMealTimeDocument } from '../models/CustomMealTime';
import { DEFAULT_MEAL_TIMES, MealType } from '../models/FoodEntry';

interface AuthRequest extends Request {
    user?: {
        id: string;
        email: string;
    };
}

// @desc    Get all custom meal times for user
// @route   GET /api/custom-meal-times
// @access  Private
export const getCustomMealTimes = async (req: AuthRequest, res: Response): Promise<void> => {
    try {
        const userId = req.user?.id;
        if (!userId) {
            res.status(401).json({
                success: false,
                message: 'User not authenticated'
            });
            return;
        }

        const customMealTimes = await CustomMealTime.find({ 
            userId, 
            isActive: true 
        }).sort({ createdAt: 1 });

        res.json({
            success: true,
            count: customMealTimes.length,
            data: customMealTimes
        });
    } catch (error) {
        console.error('Error fetching custom meal times:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch custom meal times'
        });
    }
};

// @desc    Create a new custom meal time
// @route   POST /api/custom-meal-times
// @access  Private
export const createCustomMealTime = async (req: AuthRequest, res: Response): Promise<void> => {
    try {
        const userId = req.user?.id;
        if (!userId) {
            res.status(401).json({
                success: false,
                message: 'User not authenticated'
            });
            return;
        }

        const {
            name,
            displayName,
            icon,
            color,
            defaultTime,
            timeRange
        } = req.body;

        // Validate required fields
        if (!name || !displayName || !icon || !color || !defaultTime || !timeRange) {
            res.status(400).json({
                success: false,
                message: 'All fields are required'
            });
            return;
        }

        // Validate time format
        const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;
        if (!timeRegex.test(defaultTime) || !timeRegex.test(timeRange.start) || !timeRegex.test(timeRange.end)) {
            res.status(400).json({
                success: false,
                message: 'Invalid time format. Use HH:mm format'
            });
            return;
        }

        // Validate color format
        const colorRegex = /^#[0-9A-F]{6}$/i;
        if (!colorRegex.test(color)) {
            res.status(400).json({
                success: false,
                message: 'Invalid color format. Use hex format (#RRGGBB)'
            });
            return;
        }

        // Check if name already exists for this user
        const existingMealTime = await CustomMealTime.findOne({ userId, name });
        if (existingMealTime) {
            res.status(400).json({
                success: false,
                message: 'A meal time with this name already exists'
            });
            return;
        }

        const customMealTime = new CustomMealTime({
            name,
            displayName,
            icon,
            color,
            defaultTime,
            timeRange,
            userId
        });

        await customMealTime.save();

        res.status(201).json({
            success: true,
            data: customMealTime
        });
    } catch (error) {
        console.error('Error creating custom meal time:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to create custom meal time'
        });
    }
};

// @desc    Update a custom meal time
// @route   PUT /api/custom-meal-times/:id
// @access  Private
export const updateCustomMealTime = async (req: AuthRequest, res: Response): Promise<void> => {
    try {
        const userId = req.user?.id;
        if (!userId) {
            res.status(401).json({
                success: false,
                message: 'User not authenticated'
            });
            return;
        }

        const { id } = req.params;
        const {
            name,
            displayName,
            icon,
            color,
            defaultTime,
            timeRange,
            isActive
        } = req.body;

        const customMealTime = await CustomMealTime.findOne({ _id: id, userId });
        if (!customMealTime) {
            res.status(404).json({
                success: false,
                message: 'Custom meal time not found'
            });
            return;
        }

        // Validate time format if provided
        const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;
        if (defaultTime && !timeRegex.test(defaultTime)) {
            res.status(400).json({
                success: false,
                message: 'Invalid default time format. Use HH:mm format'
            });
            return;
        }

        if (timeRange && (!timeRegex.test(timeRange.start) || !timeRegex.test(timeRange.end))) {
            res.status(400).json({
                success: false,
                message: 'Invalid time range format. Use HH:mm format'
            });
            return;
        }

        // Validate color format if provided
        const colorRegex = /^#[0-9A-F]{6}$/i;
        if (color && !colorRegex.test(color)) {
            res.status(400).json({
                success: false,
                message: 'Invalid color format. Use hex format (#RRGGBB)'
            });
            return;
        }

        // Check if new name conflicts with existing meal times
        if (name && name !== customMealTime.name) {
            const existingMealTime = await CustomMealTime.findOne({ userId, name });
            if (existingMealTime) {
                res.status(400).json({
                    success: false,
                    message: 'A meal time with this name already exists'
                });
                return;
            }
        }

        // Update fields
        if (name) customMealTime.name = name;
        if (displayName) customMealTime.displayName = displayName;
        if (icon) customMealTime.icon = icon;
        if (color) customMealTime.color = color;
        if (defaultTime) customMealTime.defaultTime = defaultTime;
        if (timeRange) customMealTime.timeRange = timeRange;
        if (typeof isActive === 'boolean') customMealTime.isActive = isActive;

        await customMealTime.save();

        res.json({
            success: true,
            data: customMealTime
        });
    } catch (error) {
        console.error('Error updating custom meal time:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to update custom meal time'
        });
    }
};

// @desc    Delete a custom meal time
// @route   DELETE /api/custom-meal-times/:id
// @access  Private
export const deleteCustomMealTime = async (req: AuthRequest, res: Response): Promise<void> => {
    try {
        const userId = req.user?.id;
        if (!userId) {
            res.status(401).json({
                success: false,
                message: 'User not authenticated'
            });
            return;
        }

        const { id } = req.params;

        const customMealTime = await CustomMealTime.findOne({ _id: id, userId });
        if (!customMealTime) {
            res.status(404).json({
                success: false,
                message: 'Custom meal time not found'
            });
            return;
        }

        // Soft delete by setting isActive to false
        customMealTime.isActive = false;
        await customMealTime.save();

        res.json({
            success: true,
            message: 'Custom meal time deleted successfully'
        });
    } catch (error) {
        console.error('Error deleting custom meal time:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to delete custom meal time'
        });
    }
};

// @desc    Initialize default meal times for user
// @route   POST /api/custom-meal-times/initialize-defaults
// @access  Private
export const initializeDefaultMealTimes = async (req: AuthRequest, res: Response): Promise<void> => {
    try {
        const userId = req.user?.id;
        if (!userId) {
            res.status(401).json({
                success: false,
                message: 'User not authenticated'
            });
            return;
        }

        // Check if user already has custom meal times
        const existingMealTimes = await CustomMealTime.find({ userId });
        if (existingMealTimes.length > 0) {
            res.status(400).json({
                success: false,
                message: 'User already has custom meal times configured'
            });
            return;
        }

        // Create default meal times
        const defaultMealTimes = (CustomMealTime as any).getDefaultMealTimes(userId);
        const createdMealTimes = await CustomMealTime.insertMany(defaultMealTimes);

        res.status(201).json({
            success: true,
            count: createdMealTimes.length,
            data: createdMealTimes
        });
    } catch (error) {
        console.error('Error initializing default meal times:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to initialize default meal times'
        });
    }
};
