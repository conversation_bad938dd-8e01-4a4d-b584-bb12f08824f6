import { Router } from 'express';
import { getDebugInfo, getDebugRequest, clearDebugData } from '../utils/debugger';
import { protect, authorize } from '../middleware/auth';
import { env } from '../config/env';

const router = Router();

// Only enable debug routes in development
if (env.NODE_ENV === 'development') {
    // @route   GET /api/debug
    // @desc    Get debug information and recent requests
    // @access  Private (Admin only)
    router.get('/', protect, authorize('admin'), getDebugInfo);

    // @route   GET /api/debug/request/:requestId
    // @desc    Get specific debug request details
    // @access  Private (Admin only)
    router.get('/request/:requestId', protect, authorize('admin'), getDebugRequest);

    // @route   DELETE /api/debug
    // @desc    Clear stored debug data
    // @access  Private (Admin only)
    router.delete('/', protect, authorize('admin'), clearDebugData);
} else {
    // Return 404 for all debug routes in production
    router.use('*', (req, res) => {
        res.status(404).json({
            success: false,
            message: 'Debug endpoints are not available in production'
        });
    });
}

export default router;
