# GlucoMonitor Export Features

## Overview
The GlucoMonitor backend now supports comprehensive export functionality with multiple formats and destinations for glucose reading data and charts.

## Supported Export Formats

### 1. JSON Format
- **Use Case**: API consumption, programmatic access
- **Content**: Structured data with metadata
- **File Extension**: `.json`

### 2. CSV Format
- **Use Case**: Excel, Google Sheets, data analysis
- **Content**: Comma-separated values with headers
- **File Extension**: `.csv`

### 3. PDF Format
- **Use Case**: Reports, sharing with healthcare providers
- **Content**: Formatted report with statistics and recent readings table
- **File Extension**: `.pdf`

### 4. Excel Format
- **Use Case**: Advanced data analysis, charts
- **Content**: Multiple worksheets (Summary, Raw Data, Chart Data)
- **File Extension**: `.xlsx`

## Supported Destinations

### 1. Download (Default)
- Direct file download through browser
- Immediate access to exported data

### 2. Email
- Send export as email attachment
- Requires email address parameter
- Professional email template included

### 3. Google Drive
- Upload to Google Drive cloud storage
- Returns shareable link
- Requires Google service account configuration

### 4. Dropbox
- Upload to Dropbox cloud storage
- Returns shareable link
- Requires Dropbox access token

### 5. Local Storage
- Save file to server's local filesystem
- Returns file path
- Useful for backup purposes

## API Endpoints

### Single Export
```
GET /api/glucose/export/chart
```

**Parameters:**
- `startDate` (optional): Start date for data range
- `endDate` (optional): End date for data range
- `chartType` (optional): line, area, bar, scatter (default: line)
- `format` (optional): json, csv, pdf, excel (default: json)
- `destination` (optional): download, email, googledrive, dropbox, local (default: download)
- `email` (required if destination=email): Email address for sending

**Example:**
```
GET /api/glucose/export/chart?format=pdf&destination=email&email=<EMAIL>&chartType=line
```

### Bulk Export
```
POST /api/glucose/export/bulk
```

**Request Body:**
```json
{
  "startDate": "2024-01-01",
  "endDate": "2024-01-31",
  "chartType": "line",
  "formats": ["pdf", "excel", "csv"],
  "destinations": ["email", "googledrive"],
  "email": "<EMAIL>",
  "includeAllFormats": false
}
```

## Environment Configuration

Add these variables to your `.env` file:

```env
# Email Configuration
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your_gmail_app_password

# Google Drive Integration
GOOGLE_SERVICE_ACCOUNT_KEY=path/to/service-account-key.json
GOOGLE_DRIVE_FOLDER_ID=your_folder_id

# Dropbox Integration
DROPBOX_ACCESS_TOKEN=your_dropbox_token

# Export Configuration
EXPORT_MAX_RECORDS=10000
EXPORT_TEMP_DIR=./temp/exports
```

## Setup Instructions

### 1. Email Setup (Gmail)
1. Enable 2-factor authentication on your Gmail account
2. Generate an App Password
3. Use the App Password as `EMAIL_PASSWORD`

### 2. Google Drive Setup
1. Create a Google Cloud Project
2. Enable Google Drive API
3. Create a Service Account
4. Download the service account key JSON file
5. Share your target folder with the service account email

### 3. Dropbox Setup
1. Create a Dropbox App at https://www.dropbox.com/developers/apps
2. Generate an access token
3. Use the token as `DROPBOX_ACCESS_TOKEN`

## File Structure

### PDF Report Contents
- Header with report title
- Summary statistics (total readings, average, min/max)
- Chart type information
- Recent readings table (last 20 entries)

### Excel File Structure
- **Summary Sheet**: Statistics and metadata
- **Raw Data Sheet**: All glucose readings with full details
- **Chart Data Sheet**: Formatted data for chart visualization

### CSV Format
- Headers: Index, Value, Timestamp, Category, Color (bar charts), Size (scatter plots)
- One row per data point
- ISO timestamp format

## Error Handling

The export system includes comprehensive error handling:
- Invalid parameters return 400 status with descriptive messages
- Missing authentication returns 401 status
- Service failures (email, cloud storage) return 500 status with error details
- Bulk exports continue processing even if individual exports fail

## Security Features

- All exports require user authentication
- Users can only export their own data
- File uploads to cloud services use secure APIs
- Email attachments are sent only to verified addresses
- Local file storage is restricted to designated directories

## Performance Considerations

- Large datasets are processed in chunks
- File generation is done asynchronously
- Temporary files are cleaned up automatically
- Export operations are logged for monitoring

## Usage Examples

### Export as PDF via Email
```bash
curl -X GET "http://localhost:5000/api/glucose/export/chart?format=pdf&destination=email&email=<EMAIL>" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### Bulk Export to Multiple Destinations
```bash
curl -X POST "http://localhost:5000/api/glucose/export/bulk" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "formats": ["pdf", "excel"],
    "destinations": ["email", "googledrive"],
    "email": "<EMAIL>",
    "chartType": "line"
  }'
```
