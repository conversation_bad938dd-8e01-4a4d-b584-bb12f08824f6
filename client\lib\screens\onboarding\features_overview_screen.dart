import 'package:flutter/material.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';
import '../../constants/app_colors.dart';
import 'auth_screen.dart';
import 'create_profile_screen.dart';

class FeaturesOverviewScreen extends StatefulWidget {
  final bool isOfflineMode;

  const FeaturesOverviewScreen({super.key, this.isOfflineMode = false});

  @override
  State<FeaturesOverviewScreen> createState() => _FeaturesOverviewScreenState();
}

class _FeaturesOverviewScreenState extends State<FeaturesOverviewScreen> {
  final PageController _pageController = PageController();
  bool isLastPage = false;

  final features = [
    {
      'title': 'Glucose Tracking',
      'description': 'Log and monitor your glucose levels with ease',
      'image': 'assets/images/glucose_tracking.png',
    },
    {
      'title': 'Medication Reminders',
      'description': 'Never miss your medication with timely reminders',
      'image': 'assets/images/medication_reminders.png',
    },
    {
      'title': 'Food Diary',
      'description': 'Track your meals and understand their impact',
      'image': 'assets/images/food_diary.png',
    },
    {
      'title': 'Emergency Alerts',
      'description': 'Quick access to emergency contacts and services',
      'image': 'assets/images/emergency_alerts.png',
    },
    {
      'title': 'Offline Support',
      'description': 'Full functionality even without internet connection',
      'image': 'assets/images/offline_support.png',
    },
  ];

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: SafeArea(
        child: Stack(
          children: [
            PageView.builder(
              controller: _pageController,
              itemCount: features.length,
              onPageChanged: (index) {
                setState(() {
                  isLastPage = index == features.length - 1;
                });
              },
              itemBuilder: (context, index) {
                final feature = features[index];
                return Padding(
                  padding: const EdgeInsets.all(24.0),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      if (index == 0) ...[
                        const Text(
                          'Welcome to GlucoMonitor',
                          style: TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: AppColors.onBackground,
                          ),
                        ),
                        const SizedBox(height: 8),
                        const Text(
                          'Here\'s what you can do with the app:',
                          style: TextStyle(
                            fontSize: 16,
                            color: AppColors.onBackground,
                          ),
                        ),
                        const SizedBox(height: 32),
                      ],
                      Expanded(
                        flex: 3,
                        child: Image.asset(
                          feature['image'] as String,
                          fit: BoxFit.contain,
                        ),
                      ),
                      const SizedBox(height: 32),
                      Text(
                        feature['title'] as String,
                        style: const TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: AppColors.onBackground,
                        ),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        feature['description'] as String,
                        textAlign: TextAlign.center,
                        style: const TextStyle(
                          fontSize: 16,
                          color: AppColors.textSecondary,
                        ),
                      ),
                      const SizedBox(height: 100),
                    ],
                  ),
                );
              },
            ),
            Positioned(
              bottom: 100,
              left: 0,
              right: 0,
              child: Center(
                child: SmoothPageIndicator(
                  controller: _pageController,
                  count: features.length,
                  effect: WormEffect(
                    spacing: 16,
                    dotColor: Colors.grey[300]!,
                    activeDotColor: Theme.of(context).primaryColor,
                  ),
                  onDotClicked:
                      (index) => _pageController.animateToPage(
                        index,
                        duration: const Duration(milliseconds: 500),
                        curve: Curves.easeOut,
                      ),
                ),
              ),
            ),
            Positioned(
              bottom: 16,
              left: 24,
              right: 24,
              child: ElevatedButton(
                onPressed: () {
                  if (isLastPage) {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder:
                            (context) =>
                                widget.isOfflineMode
                                    ? const CreateProfileScreen(
                                      isOfflineMode: true,
                                    )
                                    : const AuthScreen(),
                      ),
                    );
                  } else {
                    _pageController.nextPage(
                      duration: const Duration(milliseconds: 500),
                      curve: Curves.easeOut,
                    );
                  }
                },
                style: ElevatedButton.styleFrom(
                  minimumSize: const Size(double.infinity, 50),
                ),
                child: Text(isLastPage ? 'Get Started' : 'Next'),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
