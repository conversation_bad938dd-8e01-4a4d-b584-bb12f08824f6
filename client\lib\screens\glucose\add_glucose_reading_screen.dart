import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../../constants/app_colors.dart';
import '../../models/glucose_reading.dart';
import '../../services/glucose_service.dart';
import '../../providers/auth_provider.dart';

class AddGlucoseReadingScreen extends StatefulWidget {
  final GlucoseReading? editReading;

  const AddGlucoseReadingScreen({super.key, this.editReading});

  @override
  State<AddGlucoseReadingScreen> createState() =>
      _AddGlucoseReadingScreenState();
}

class _AddGlucoseReadingScreenState extends State<AddGlucoseReadingScreen> {
  final _formKey = GlobalKey<FormState>();
  final _valueController = TextEditingController();
  final _notesController = TextEditingController();

  DateTime _selectedDateTime = DateTime.now();
  MealTiming _selectedMealTiming = MealTiming.other;
  List<String> _selectedTags = [];
  bool _isLoading = false;

  final List<String> _availableTags = [
    'Exercise',
    'Stress',
    'Medication',
    'Illness',
    'Travel',
    'Special Event',
    'Fasting',
    'Post-meal',
  ];

  @override
  void initState() {
    super.initState();
    if (widget.editReading != null) {
      _initializeWithExistingReading();
    }
  }

  void _initializeWithExistingReading() {
    final reading = widget.editReading!;
    _valueController.text = reading.value.toString();
    _selectedDateTime = reading.timestamp;
    _selectedMealTiming = reading.mealTiming ?? MealTiming.other;
    _notesController.text = reading.notes ?? '';
    _selectedTags = reading.tags ?? [];
  }

  @override
  void dispose() {
    _valueController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  Future<void> _selectDateTime() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _selectedDateTime,
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now(),
    );

    if (date != null) {
      if (!mounted) return;

      final time = await showTimePicker(
        context: context,
        initialTime: TimeOfDay.fromDateTime(_selectedDateTime),
      );

      if (time != null) {
        setState(() {
          _selectedDateTime = DateTime(
            date.year,
            date.month,
            date.day,
            time.hour,
            time.minute,
          );
        });
      }
    }
  }

  void _toggleTag(String tag) {
    setState(() {
      if (_selectedTags.contains(tag)) {
        _selectedTags.remove(tag);
      } else {
        _selectedTags.add(tag);
      }
    });
  }

  Future<void> _saveReading() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final token = authProvider.backendToken;

      if (token == null) {
        throw Exception('Authentication required');
      }

      final reading = GlucoseReading(
        id: widget.editReading?.id,
        userId: '', // Will be set by backend based on token
        value: double.parse(_valueController.text),
        timestamp: _selectedDateTime,
        mealTiming: _selectedMealTiming,
        notes:
            _notesController.text.trim().isEmpty
                ? null
                : _notesController.text.trim(),
        tags: _selectedTags.isEmpty ? null : _selectedTags,
      );

      if (widget.editReading != null) {
        await GlucoseService.updateGlucoseReading(
          token: token,
          readingId: widget.editReading!.id!,
          reading: reading,
        );
      } else {
        await GlucoseService.createGlucoseReading(
          token: token,
          reading: reading,
        );
      }

      if (mounted) {
        Navigator.of(context).pop(true); // Return true to indicate success
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _deleteReading() async {
    if (widget.editReading == null) return;

    // Get auth provider before async operations
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final token = authProvider.backendToken;

    if (token == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Authentication required'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    // Show confirmation dialog
    final confirmed = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Delete Reading'),
            content: const Text(
              'Are you sure you want to delete this glucose reading? This action cannot be undone.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                style: TextButton.styleFrom(foregroundColor: Colors.red),
                child: const Text('Delete'),
              ),
            ],
          ),
    );

    if (confirmed != true) return;

    setState(() {
      _isLoading = true;
    });

    try {
      await GlucoseService.deleteGlucoseReading(
        token: token,
        readingId: widget.editReading!.id!,
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Reading deleted successfully'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.of(context).pop(true); // Return true to indicate success
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error deleting reading: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      resizeToAvoidBottomInset: true,
      appBar: AppBar(
        title: Text(
          widget.editReading != null ? 'Edit Reading' : 'Add Glucose Reading',
        ),
        backgroundColor: AppColors.background,
        foregroundColor: AppColors.onBackground,
        actions: [
          // Delete button (only show when editing)
          if (widget.editReading != null && !_isLoading)
            IconButton(
              onPressed: _deleteReading,
              icon: const Icon(Icons.delete),
              color: Colors.red,
              tooltip: 'Delete Reading',
            ),
          // Save/Update button
          if (_isLoading)
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
            )
          else
            TextButton(
              onPressed: _saveReading,
              child: Text(
                widget.editReading != null ? 'Update' : 'Save',
                style: const TextStyle(
                  color: AppColors.secondary,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
        ],
      ),
      body: SafeArea(
        child: Form(
          key: _formKey,
          child: LayoutBuilder(
            builder: (context, constraints) {
              // Get screen dimensions for dynamic sizing
              final availableHeight = constraints.maxHeight;
              final keyboardHeight = MediaQuery.of(context).viewInsets.bottom;
              final isKeyboardVisible = keyboardHeight > 0;

              // Calculate dynamic spacing based on available height and keyboard visibility
              final isCompactScreen =
                  availableHeight < 500 || isKeyboardVisible;
              final cardSpacing = isCompactScreen ? 4.0 : 12.0;
              final sectionPadding = isCompactScreen ? 8.0 : 16.0;
              final fieldSpacing = isCompactScreen ? 4.0 : 12.0;
              final mainPadding = isCompactScreen ? 8.0 : 16.0;

              return SingleChildScrollView(
                padding: EdgeInsets.all(mainPadding),
                child: Column(
                  children: [
                    // Glucose Value Input
                    Card(
                      color: AppColors.surface,
                      child: Padding(
                        padding: EdgeInsets.all(sectionPadding),
                        child: IntrinsicHeight(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Text(
                                'Glucose Value',
                                style: TextStyle(
                                  fontSize: isCompactScreen ? 14 : 16,
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.onSurface,
                                ),
                              ),
                              SizedBox(height: fieldSpacing),
                              TextFormField(
                                controller: _valueController,
                                keyboardType:
                                    const TextInputType.numberWithOptions(
                                      decimal: true,
                                    ),
                                inputFormatters: [
                                  FilteringTextInputFormatter.allow(
                                    RegExp(r'^\d+\.?\d{0,1}'),
                                  ),
                                ],
                                decoration: const InputDecoration(
                                  hintText: 'Enter glucose value',
                                  suffixText: 'mg/dL',
                                  border: OutlineInputBorder(),
                                ),
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return 'Please enter a glucose value';
                                  }
                                  final numValue = double.tryParse(value);
                                  if (numValue == null) {
                                    return 'Please enter a valid number';
                                  }
                                  if (numValue < 20 || numValue > 600) {
                                    return 'Value must be between 20 and 600 mg/dL';
                                  }
                                  return null;
                                },
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),

                    SizedBox(height: cardSpacing),

                    // Date and Time
                    Card(
                      color: AppColors.surface,
                      child: Padding(
                        padding: EdgeInsets.all(sectionPadding),
                        child: IntrinsicHeight(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Text(
                                'Date & Time',
                                style: TextStyle(
                                  fontSize: isCompactScreen ? 14 : 16,
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.onSurface,
                                ),
                              ),
                              SizedBox(height: fieldSpacing),
                              InkWell(
                                onTap: _selectDateTime,
                                child: Container(
                                  padding: EdgeInsets.all(
                                    isCompactScreen ? 10 : 12,
                                  ),
                                  decoration: BoxDecoration(
                                    border: Border.all(color: Colors.grey),
                                    borderRadius: BorderRadius.circular(4),
                                  ),
                                  child: Row(
                                    children: [
                                      const Icon(Icons.calendar_today),
                                      const SizedBox(width: 8),
                                      Text(
                                        '${_selectedDateTime.day}/${_selectedDateTime.month}/${_selectedDateTime.year} '
                                        '${_selectedDateTime.hour.toString().padLeft(2, '0')}:'
                                        '${_selectedDateTime.minute.toString().padLeft(2, '0')}',
                                        style: TextStyle(
                                          fontSize: isCompactScreen ? 14 : 16,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),

                    SizedBox(height: cardSpacing),

                    // Meal Timing
                    Card(
                      color: AppColors.surface,
                      child: Padding(
                        padding: EdgeInsets.all(sectionPadding),
                        child: IntrinsicHeight(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Text(
                                'Meal Timing',
                                style: TextStyle(
                                  fontSize: isCompactScreen ? 14 : 16,
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.onSurface,
                                ),
                              ),
                              SizedBox(height: fieldSpacing),
                              DropdownButtonFormField<MealTiming>(
                                value: _selectedMealTiming,
                                decoration: const InputDecoration(
                                  border: OutlineInputBorder(),
                                ),
                                items:
                                    MealTiming.allTimings.map((timing) {
                                      return DropdownMenuItem(
                                        value: timing,
                                        child: Text(timing.displayName),
                                      );
                                    }).toList(),
                                onChanged: (value) {
                                  if (value != null) {
                                    setState(() {
                                      _selectedMealTiming = value;
                                    });
                                  }
                                },
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),

                    SizedBox(height: cardSpacing),

                    // Tags
                    Card(
                      color: AppColors.surface,
                      child: Padding(
                        padding: EdgeInsets.all(sectionPadding),
                        child: IntrinsicHeight(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Text(
                                'Tags (Optional)',
                                style: TextStyle(
                                  fontSize: isCompactScreen ? 14 : 16,
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.onSurface,
                                ),
                              ),
                              const SizedBox(height: 8),
                              Wrap(
                                spacing: 8,
                                runSpacing: 8,
                                children:
                                    _availableTags.map((tag) {
                                      final isSelected = _selectedTags.contains(
                                        tag,
                                      );
                                      return FilterChip(
                                        label: Text(tag),
                                        selected: isSelected,
                                        onSelected: (_) => _toggleTag(tag),
                                        selectedColor: AppColors.primary
                                            .withValues(alpha: 0.3),
                                        checkmarkColor: AppColors.primary,
                                      );
                                    }).toList(),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),

                    SizedBox(height: cardSpacing),

                    // Notes
                    Card(
                      color: AppColors.surface,
                      child: Padding(
                        padding: EdgeInsets.all(sectionPadding),
                        child: IntrinsicHeight(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Text(
                                'Notes (Optional)',
                                style: TextStyle(
                                  fontSize: isCompactScreen ? 14 : 16,
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.onSurface,
                                ),
                              ),
                              SizedBox(height: fieldSpacing),
                              TextFormField(
                                controller: _notesController,
                                maxLines: isCompactScreen ? 2 : 3,
                                maxLength: 500,
                                decoration: const InputDecoration(
                                  hintText:
                                      'Add any notes about this reading...',
                                  border: OutlineInputBorder(),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),

                    // Delete button (only show when editing)
                    if (widget.editReading != null) ...[
                      SizedBox(height: isCompactScreen ? 16.0 : 32.0),
                      SizedBox(
                        width: double.infinity,
                        child: OutlinedButton.icon(
                          onPressed: _isLoading ? null : _deleteReading,
                          icon: const Icon(Icons.delete, color: Colors.red),
                          label: const Text(
                            'Delete Reading',
                            style: TextStyle(color: Colors.red),
                          ),
                          style: OutlinedButton.styleFrom(
                            side: const BorderSide(color: Colors.red),
                            padding: const EdgeInsets.symmetric(vertical: 16),
                          ),
                        ),
                      ),
                      SizedBox(height: cardSpacing),
                    ],
                    // Add reasonable bottom padding for safe area
                    SizedBox(height: isCompactScreen ? 8.0 : 24.0),
                  ],
                ),
              );
            },
          ),
        ),
      ),
    );
  }
}
