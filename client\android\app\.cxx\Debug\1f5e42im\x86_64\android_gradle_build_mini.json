{"buildFiles": ["C:\\flutter\\src\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Documents\\Sentebale\\GlucoMonitor\\client\\android\\app\\.cxx\\Debug\\1f5e42im\\x86_64", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Documents\\Sentebale\\GlucoMonitor\\client\\android\\app\\.cxx\\Debug\\1f5e42im\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}