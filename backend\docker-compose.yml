# GlucoMonitor Backend - Standalone Docker Compose
# This file is for backend-only development and testing
version: '3.8'

services:
  # Backend API service (Production)
  api:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: glucomonitor-api
    restart: unless-stopped
    ports:
      - "5000:5000"
    environment:
      - NODE_ENV=production
      - PORT=5000
      - MONGODB_URI=${MONGODB_URI}
      - JWT_SECRET=${JWT_SECRET}
      - JWT_EXPIRE=${JWT_EXPIRE}
      - SMSPORTAL_CLIENT_ID=${SMSPORTAL_CLIENT_ID}
      - SMSPORTAL_API_SECRET=${SMSPORTAL_API_SECRET}
      - SMSPORTAL_SENDER=${SMSPORTAL_SENDER}
      - REDIS_URL=redis://redis:6379
      - NOTIFICATION_ENABLED=${NOTIFICATION_ENABLED:-true}
      - NOTIFICATION_ADVANCE_MINUTES=${NOTIFICATION_ADVANCE_MINUTES:-5}
      - NOTIFICATION_RETRY_ATTEMPTS=${NOTIFICATION_RETRY_ATTEMPTS:-3}
      - NOTIFICATION_RETRY_INTERVAL=${NOTIFICATION_RETRY_INTERVAL:-300000}
      - DEVELOPMENT_MODE=${DEVELOPMENT_MODE:-false}
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - glucomonitor-network
    volumes:
      - ./logs:/app/logs
      - ./uploads:/app/uploads
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/api/health/live"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Redis service for caching and rate limiting
  redis:
    image: redis:7-alpine
    container_name: glucomonitor-redis-backend
    restart: unless-stopped
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    networks:
      - glucomonitor-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # Development service
  api-dev:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: glucomonitor-api-dev
    ports:
      - "5000:5000"
      - "9229:9229"  # Debug port
    environment:
      - NODE_ENV=development
      - PORT=5000
      - MONGODB_URI=${MONGODB_URI}
      - JWT_SECRET=${JWT_SECRET}
      - JWT_EXPIRE=${JWT_EXPIRE}
      - SMSPORTAL_CLIENT_ID=${SMSPORTAL_CLIENT_ID}
      - SMSPORTAL_API_SECRET=${SMSPORTAL_API_SECRET}
      - SMSPORTAL_SENDER=${SMSPORTAL_SENDER}
      - REDIS_URL=redis://redis:6379
      - NOTIFICATION_ENABLED=${NOTIFICATION_ENABLED:-true}
      - NOTIFICATION_ADVANCE_MINUTES=${NOTIFICATION_ADVANCE_MINUTES:-5}
      - NOTIFICATION_RETRY_ATTEMPTS=${NOTIFICATION_RETRY_ATTEMPTS:-3}
      - NOTIFICATION_RETRY_INTERVAL=${NOTIFICATION_RETRY_INTERVAL:-300000}
      - DEVELOPMENT_MODE=${DEVELOPMENT_MODE:-true}
    volumes:
      # Mount source code for hot reloading
      - ./src:/app/src
      - ./package.json:/app/package.json
      - ./tsconfig.json:/app/tsconfig.json
      - ./logs:/app/logs
      - ./uploads:/app/uploads
      # Exclude node_modules from being overwritten
      - /app/node_modules
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - glucomonitor-network
    profiles:
      - dev
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/api/health/live"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Test service
  api-test:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    container_name: glucomonitor-api-test
    environment:
      - NODE_ENV=test
      - PORT=5000
      - MONGODB_URI=${MONGODB_URI_TEST:-mongodb://localhost:27017/glucomonitor_test}
      - JWT_SECRET=${JWT_SECRET}
      - JWT_EXPIRE=1h
      - REDIS_URL=redis://redis:6379
      - DEVELOPMENT_MODE=true
    volumes:
      - .:/app
      - /app/node_modules
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - glucomonitor-network
    profiles:
      - test
    command: ["npm", "test"]

volumes:
  redis_data:

networks:
  glucomonitor-network:
    driver: bridge
