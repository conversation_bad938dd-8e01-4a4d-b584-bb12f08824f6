import { Schema, model, Document, Types } from 'mongoose';

// Interface for image features
interface IImageFeatures {
    colorFeatures: {
        avgRed: number;
        avgGreen: number;
        avgBlue: number;
        dominantColor: string;
        brightness: number;
        saturation: number;
    };
    textureFeatures: {
        smoothness: number;
        uniformity: number;
        graininess: number;
    };
    shapeFeatures: {
        roundness: number;
        elongation: number;
        compactness: number;
    };
    sizeFeatures: {
        totalPixels: number;
        estimatedArea: number;
    };
}

// Interface for nutrition data
interface INutritionData {
    calories: number;
    carbohydrates: number;
    protein: number;
    fat: number;
    fiber: number;
    glycemicIndex: 'low' | 'medium' | 'high';
    category: string;
    portionSize: number;
    unit: string;
}

// Interface for custom food model
export interface ICustomFoodModel extends Document {
    userId: Types.ObjectId;
    foodName: string;
    description: string;
    nutritionData: INutritionData;
    trainingFeatures: IImageFeatures[];
    tags: string[];
    confidence: number;
    recognitionCount: number;
    successRate: number;
    createdAt: Date;
    lastTrained: Date;
    lastRecognized?: Date;
}

// Image features schema
const imageFeaturesSchema = new Schema<IImageFeatures>({
    colorFeatures: {
        avgRed: { type: Number, required: true },
        avgGreen: { type: Number, required: true },
        avgBlue: { type: Number, required: true },
        dominantColor: { type: String, required: true },
        brightness: { type: Number, required: true },
        saturation: { type: Number, required: true }
    },
    textureFeatures: {
        smoothness: { type: Number, required: true },
        uniformity: { type: Number, required: true },
        graininess: { type: Number, required: true }
    },
    shapeFeatures: {
        roundness: { type: Number, required: true },
        elongation: { type: Number, required: true },
        compactness: { type: Number, required: true }
    },
    sizeFeatures: {
        totalPixels: { type: Number, required: true },
        estimatedArea: { type: Number, required: true }
    }
}, { _id: false });

// Nutrition data schema
const nutritionDataSchema = new Schema<INutritionData>({
    calories: { type: Number, required: true, min: 0 },
    carbohydrates: { type: Number, required: true, min: 0 },
    protein: { type: Number, required: true, min: 0 },
    fat: { type: Number, required: true, min: 0 },
    fiber: { type: Number, required: true, min: 0 },
    glycemicIndex: {
        type: String,
        enum: ['low', 'medium', 'high'],
        required: true
    },
    category: { type: String, required: true },
    portionSize: { type: Number, required: true, min: 0 },
    unit: { type: String, required: true }
}, { _id: false });

// Custom food model schema
const customFoodModelSchema = new Schema<ICustomFoodModel>({
    userId: {
        type: Schema.Types.ObjectId,
        ref: 'User',
        required: true,
        index: true
    },
    foodName: {
        type: String,
        required: true,
        trim: true,
        maxlength: 100
    },
    description: {
        type: String,
        trim: true,
        maxlength: 500,
        default: ''
    },
    nutritionData: {
        type: nutritionDataSchema,
        required: true
    },
    trainingFeatures: {
        type: [imageFeaturesSchema],
        required: true,
        validate: {
            validator: function(features: IImageFeatures[]) {
                return features.length >= 1 && features.length <= 20;
            },
            message: 'Training features must contain between 1 and 20 examples'
        }
    },
    tags: {
        type: [String],
        default: [],
        validate: {
            validator: function(tags: string[]) {
                return tags.length <= 10;
            },
            message: 'Maximum 10 tags allowed'
        }
    },
    confidence: {
        type: Number,
        required: true,
        min: 0,
        max: 1,
        default: 0.8
    },
    recognitionCount: {
        type: Number,
        default: 0,
        min: 0
    },
    successRate: {
        type: Number,
        default: 0,
        min: 0,
        max: 1
    },
    createdAt: {
        type: Date,
        default: Date.now,
        index: true
    },
    lastTrained: {
        type: Date,
        default: Date.now
    },
    lastRecognized: {
        type: Date
    }
}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
});

// Indexes for better query performance
customFoodModelSchema.index({ userId: 1, foodName: 1 }, { unique: true });
customFoodModelSchema.index({ userId: 1, createdAt: -1 });
customFoodModelSchema.index({ userId: 1, lastTrained: -1 });
customFoodModelSchema.index({ userId: 1, confidence: -1 });

// Virtual for training quality score
customFoodModelSchema.virtual('trainingQuality').get(function() {
    const featureCount = this.trainingFeatures.length;
    const baseScore = Math.min(featureCount / 5, 1.0); // Optimal at 5+ examples
    const confidenceBonus = this.confidence * 0.2;
    return Math.min(baseScore + confidenceBonus, 1.0);
});

// Virtual for overall performance score
customFoodModelSchema.virtual('performanceScore').get(function() {
    if (this.recognitionCount === 0) return this.confidence;
    return (this.successRate * 0.7) + (this.confidence * 0.3);
});

// Instance methods
customFoodModelSchema.methods.updateRecognitionStats = function(wasSuccessful: boolean) {
    this.recognitionCount += 1;
    this.lastRecognized = new Date();
    
    // Update success rate using exponential moving average
    const alpha = 0.1; // Learning rate
    if (this.recognitionCount === 1) {
        this.successRate = wasSuccessful ? 1 : 0;
    } else {
        this.successRate = (alpha * (wasSuccessful ? 1 : 0)) + ((1 - alpha) * this.successRate);
    }
    
    return this.save();
};

customFoodModelSchema.methods.addTrainingExample = function(imageFeatures: IImageFeatures) {
    if (this.trainingFeatures.length >= 20) {
        // Remove oldest example if at limit
        this.trainingFeatures.shift();
    }
    
    this.trainingFeatures.push(imageFeatures);
    this.lastTrained = new Date();
    
    // Recalculate confidence based on training examples
    this.confidence = Math.min(this.trainingFeatures.length / 5 * 0.9, 0.95);
    
    return this.save();
};

// Static methods
customFoodModelSchema.statics.findByUserId = function(userId: Types.ObjectId) {
    return this.find({ userId }).sort({ lastTrained: -1 });
};

customFoodModelSchema.statics.findTopPerformers = function(userId: Types.ObjectId, limit: number = 10) {
    return this.find({ userId })
        .sort({ performanceScore: -1, recognitionCount: -1 })
        .limit(limit);
};

customFoodModelSchema.statics.getTrainingStats = function(userId: Types.ObjectId) {
    return this.aggregate([
        { $match: { userId } },
        {
            $group: {
                _id: null,
                totalCustomFoods: { $sum: 1 },
                totalTrainingExamples: { $sum: { $size: '$trainingFeatures' } },
                averageConfidence: { $avg: '$confidence' },
                averageSuccessRate: { $avg: '$successRate' },
                totalRecognitions: { $sum: '$recognitionCount' }
            }
        }
    ]);
};

// Pre-save middleware
customFoodModelSchema.pre('save', function(next) {
    // Ensure food name is properly formatted
    if (this.isModified('foodName')) {
        this.foodName = this.foodName.trim().toLowerCase()
            .split(' ')
            .map(word => word.charAt(0).toUpperCase() + word.slice(1))
            .join(' ');
    }
    
    // Validate training features
    if (this.isModified('trainingFeatures') && this.trainingFeatures.length === 0) {
        return next(new Error('At least one training example is required'));
    }
    
    next();
});

// Post-save middleware for logging
customFoodModelSchema.post('save', function(doc) {
    console.log(`Custom food model saved: ${doc.foodName} for user ${doc.userId}`);
});

// Create and export the model
const CustomFoodModel = model<ICustomFoodModel>('CustomFoodModel', customFoodModelSchema);

export default CustomFoodModel;
