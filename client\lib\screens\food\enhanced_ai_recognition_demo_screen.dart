import 'package:flutter/material.dart';
import '../../constants/app_colors.dart';
import '../../models/food_entry.dart';
import '../../models/food_recognition_result.dart';
import '../../widgets/food_diary/realtime_camera_widget.dart';
import '../../widgets/food_diary/confidence_indicator.dart';
import '../../services/portion_estimation_service.dart';

/// Demo screen showcasing enhanced AI Food Recognition features
class EnhancedAIRecognitionDemoScreen extends StatefulWidget {
  const EnhancedAIRecognitionDemoScreen({super.key});

  @override
  State<EnhancedAIRecognitionDemoScreen> createState() =>
      _EnhancedAIRecognitionDemoScreenState();
}

class _EnhancedAIRecognitionDemoScreenState
    extends State<EnhancedAIRecognitionDemoScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  FoodRecognitionResult? _currentResult;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: const Text('Enhanced AI Food Recognition'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: const [
            Tab(icon: Icon(Icons.camera_alt), text: 'Real-time'),
            Tab(icon: Icon(Icons.grid_view), text: 'Multi-Detection'),
            Tab(icon: Icon(Icons.straighten), text: 'Portion Size'),
            Tab(icon: Icon(Icons.analytics), text: 'Confidence'),
            Tab(icon: Icon(Icons.school), text: 'Custom Training'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildRealtimeCameraTab(),
          _buildMultiDetectionTab(),
          _buildPortionEstimationTab(),
          _buildConfidenceScoringTab(),
          _buildCustomTrainingTab(),
        ],
      ),
    );
  }

  Widget _buildRealtimeCameraTab() {
    return Column(
      children: [
        // Feature description
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppColors.primary.withValues(alpha: 0.1),
            border: Border(
              bottom: BorderSide(
                color: AppColors.primary.withValues(alpha: 0.2),
              ),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(Icons.camera_alt, color: AppColors.primary),
                  const SizedBox(width: 8),
                  const Text(
                    'Real-time Camera Analysis',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: AppColors.primary,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              const Text(
                'Point your camera at food for instant identification with live detection feedback and optimized camera settings.',
                style: TextStyle(fontSize: 14),
              ),
            ],
          ),
        ),

        // Camera widget
        Expanded(
          child: RealtimeCameraWidget(
            mealType: MealType.lunch,
            onFoodDetected: (result) {
              setState(() {
                _currentResult = result;
              });
            },
            onFoodsSelected: (foods) {
              _showSuccessMessage('Added ${foods.length} food(s) to your meal');
            },
          ),
        ),

        // Results panel
        if (_currentResult != null)
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withValues(alpha: 0.2),
                  spreadRadius: 1,
                  blurRadius: 5,
                  offset: const Offset(0, -2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Detection Results',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                if (_currentResult!.detectedFoods.isNotEmpty)
                  ...(_currentResult!.detectedFoods
                      .take(3)
                      .map(
                        (food) => Padding(
                          padding: const EdgeInsets.only(bottom: 4),
                          child: Row(
                            children: [
                              Expanded(child: Text(food.name)),
                              ConfidenceIndicator(
                                confidence: _currentResult!.confidence,
                                isCompact: true,
                              ),
                            ],
                          ),
                        ),
                      )),
              ],
            ),
          ),
      ],
    );
  }

  Widget _buildMultiDetectionTab() {
    return Column(
      children: [
        // Feature description
        _buildFeatureHeader(
          icon: Icons.grid_view,
          title: 'Multi-food Detection',
          description:
              'Identify multiple food items in a single photo with individual bounding boxes and confidence scores.',
        ),

        // Demo content
        Expanded(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                // Sample multi-detection result
                Container(
                  width: double.infinity,
                  height: 200,
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.grey[300]!),
                  ),
                  child: Stack(
                    children: [
                      const Center(
                        child: Text(
                          'Sample Multi-Detection Image\n(Tap camera to try real detection)',
                          textAlign: TextAlign.center,
                          style: TextStyle(color: Colors.grey),
                        ),
                      ),
                      // Sample bounding boxes
                      Positioned(
                        left: 20,
                        top: 20,
                        width: 80,
                        height: 60,
                        child: Container(
                          decoration: BoxDecoration(
                            border: Border.all(
                              color: AppColors.primary,
                              width: 2,
                            ),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Align(
                            alignment: Alignment.topLeft,
                            child: Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 4,
                                vertical: 2,
                              ),
                              decoration: const BoxDecoration(
                                color: AppColors.primary,
                                borderRadius: BorderRadius.only(
                                  topLeft: Radius.circular(4),
                                  bottomRight: Radius.circular(4),
                                ),
                              ),
                              child: const Text(
                                'Pap 85%',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 8,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                      Positioned(
                        right: 30,
                        top: 40,
                        width: 70,
                        height: 50,
                        child: Container(
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.green, width: 2),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Align(
                            alignment: Alignment.topLeft,
                            child: Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 4,
                                vertical: 2,
                              ),
                              decoration: const BoxDecoration(
                                color: Colors.green,
                                borderRadius: BorderRadius.only(
                                  topLeft: Radius.circular(4),
                                  bottomRight: Radius.circular(4),
                                ),
                              ),
                              child: const Text(
                                'Morogo 92%',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 8,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 20),

                // Multi-detection features
                _buildFeatureList([
                  'Detect multiple foods simultaneously',
                  'Individual confidence scores per item',
                  'Visual bounding boxes for each food',
                  'Select/deselect detected items',
                  'Batch nutrition calculation',
                ]),

                const SizedBox(height: 20),

                ElevatedButton.icon(
                  onPressed: () => _tabController.animateTo(0),
                  icon: const Icon(Icons.camera_alt),
                  label: const Text('Try Multi-Detection'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 12,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPortionEstimationTab() {
    return Column(
      children: [
        _buildFeatureHeader(
          icon: Icons.straighten,
          title: 'AI-powered Portion Size Estimation',
          description:
              'Automatically estimate serving sizes using object detection and reference objects for scale.',
        ),

        Expanded(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                // Portion estimation demo
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.blue.withValues(alpha: 0.05),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: Colors.blue.withValues(alpha: 0.2),
                    ),
                  ),
                  child: Column(
                    children: [
                      Row(
                        children: [
                          Icon(Icons.straighten, color: Colors.blue),
                          const SizedBox(width: 8),
                          const Text(
                            'Portion Estimation Example',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Colors.blue,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      Row(
                        children: [
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Text('Food: Pap'),
                                const Text('Estimated: 180g'),
                                const Text('Method: Reference object'),
                                const SizedBox(height: 8),
                                Row(
                                  children: [
                                    const Text('Confidence: '),
                                    ConfidenceIndicator(
                                      confidence: 0.82,
                                      isCompact: true,
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                          Container(
                            width: 80,
                            height: 80,
                            decoration: BoxDecoration(
                              color: Colors.grey[200],
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: const Icon(
                              Icons.restaurant,
                              size: 40,
                              color: Colors.grey,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 20),

                // Visual portion guides
                const Text(
                  'Visual Portion Guides',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 12),

                ...PortionEstimationService.getPortionGuides('rice').map(
                  (guide) => Container(
                    margin: const EdgeInsets.only(bottom: 8),
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.grey[300]!),
                    ),
                    child: Row(
                      children: [
                        Text(
                          guide.visualReference,
                          style: const TextStyle(fontSize: 24),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                guide.description,
                                style: const TextStyle(
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              Text(
                                '≈ ${guide.weight.toInt()}g',
                                style: TextStyle(
                                  color: Colors.grey[600],
                                  fontSize: 12,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                const Spacer(),

                _buildFeatureList([
                  'Reference object detection',
                  'Visual portion guides',
                  'Automatic weight calculation',
                  'Nutrition scaling',
                ]),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildConfidenceScoringTab() {
    return Column(
      children: [
        _buildFeatureHeader(
          icon: Icons.analytics,
          title: 'Enhanced Confidence Scoring',
          description:
              'Visual indicators and accuracy percentages with user correction tracking for continuous improvement.',
        ),

        Expanded(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                // Confidence examples
                const Text(
                  'Confidence Indicators',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 16),

                // Different confidence levels
                _buildConfidenceExample('Pap', 0.95, 'Excellent recognition'),
                const SizedBox(height: 12),
                _buildConfidenceExample('Morogo', 0.78, 'Good recognition'),
                const SizedBox(height: 12),
                _buildConfidenceExample('Unknown Food', 0.45, 'Low confidence'),

                const SizedBox(height: 24),

                // Multi-confidence indicator
                MultiConfidenceIndicator(
                  items: const [
                    ConfidenceItem(name: 'Boerewors', confidence: 0.89),
                    ConfidenceItem(name: 'Pap', confidence: 0.82),
                    ConfidenceItem(name: 'Chakalaka', confidence: 0.67),
                  ],
                  isExpanded: true,
                ),

                const SizedBox(height: 24),

                // Confidence bar demo
                const Text(
                  'Animated Confidence Bars',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 12),

                const ConfidenceBar(
                  confidence: 0.85,
                  width: 200,
                  showLabel: true,
                ),

                const Spacer(),

                _buildFeatureList([
                  'Real-time confidence scoring',
                  'Visual progress indicators',
                  'Color-coded accuracy levels',
                  'User feedback integration',
                ]),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildCustomTrainingTab() {
    return Column(
      children: [
        _buildFeatureHeader(
          icon: Icons.school,
          title: 'Custom Food Training',
          description:
              'Train the AI on your specific homemade dishes and family recipes with photo examples.',
        ),

        Expanded(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                // Training process
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.purple.withValues(alpha: 0.05),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: Colors.purple.withValues(alpha: 0.2),
                    ),
                  ),
                  child: Column(
                    children: [
                      Row(
                        children: [
                          Icon(Icons.school, color: Colors.purple),
                          const SizedBox(width: 8),
                          const Text(
                            'Training Process',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Colors.purple,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      _buildTrainingStep(1, 'Take 3-5 photos of your dish'),
                      _buildTrainingStep(2, 'Add nutrition information'),
                      _buildTrainingStep(3, 'AI learns your food patterns'),
                      _buildTrainingStep(4, 'Improved recognition accuracy'),
                    ],
                  ),
                ),

                const SizedBox(height: 20),

                // Sample custom foods
                const Text(
                  'Sample Custom Foods',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 12),

                _buildCustomFoodCard(
                  'Gogo\'s Potjiekos',
                  'Traditional stew',
                  0.92,
                ),
                const SizedBox(height: 8),
                _buildCustomFoodCard('Family Bobotie', 'Homemade recipe', 0.88),
                const SizedBox(height: 8),
                _buildCustomFoodCard('Special Pap Mix', 'With herbs', 0.85),

                const SizedBox(height: 20),

                ElevatedButton.icon(
                  onPressed: _showTrainingDialog,
                  icon: const Icon(Icons.add),
                  label: const Text('Train New Food'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.purple,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 12,
                    ),
                  ),
                ),

                const Spacer(),

                _buildFeatureList([
                  'Photo-based training',
                  'Custom nutrition data',
                  'Learning algorithm integration',
                  'Improved personal accuracy',
                ]),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildFeatureHeader({
    required IconData icon,
    required String title,
    required String description,
  }) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.1),
        border: Border(
          bottom: BorderSide(color: AppColors.primary.withValues(alpha: 0.2)),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: AppColors.primary),
              const SizedBox(width: 8),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppColors.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(description, style: const TextStyle(fontSize: 14)),
        ],
      ),
    );
  }

  Widget _buildFeatureList(List<String> features) {
    return Column(
      children:
          features
              .map(
                (feature) => Padding(
                  padding: const EdgeInsets.only(bottom: 8),
                  child: Row(
                    children: [
                      Icon(
                        Icons.check_circle,
                        color: AppColors.primary,
                        size: 16,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          feature,
                          style: const TextStyle(fontSize: 14),
                        ),
                      ),
                    ],
                  ),
                ),
              )
              .toList(),
    );
  }

  Widget _buildConfidenceExample(
    String foodName,
    double confidence,
    String description,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  foodName,
                  style: const TextStyle(fontWeight: FontWeight.w500),
                ),
                Text(
                  description,
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                ),
              ],
            ),
          ),
          ConfidenceIndicator(
            confidence: confidence,
            showPercentage: true,
            showIcon: true,
          ),
        ],
      ),
    );
  }

  Widget _buildTrainingStep(int step, String description) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Container(
            width: 24,
            height: 24,
            decoration: const BoxDecoration(
              color: Colors.purple,
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Text(
                step.toString(),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(child: Text(description)),
        ],
      ),
    );
  }

  Widget _buildCustomFoodCard(
    String name,
    String description,
    double performance,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: Colors.purple.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(Icons.restaurant, color: Colors.purple),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(name, style: const TextStyle(fontWeight: FontWeight.w500)),
                Text(
                  description,
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                ),
              ],
            ),
          ),
          ConfidenceIndicator(confidence: performance, isCompact: true),
        ],
      ),
    );
  }

  void _showTrainingDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Train Custom Food'),
            content: const Text(
              'This would open the custom food training interface where you can:\n\n'
              '• Take multiple photos of your dish\n'
              '• Add nutrition information\n'
              '• Train the AI to recognize it\n\n'
              'Feature coming soon!',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('OK'),
              ),
            ],
          ),
    );
  }

  void _showSuccessMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 2),
      ),
    );
  }
}
