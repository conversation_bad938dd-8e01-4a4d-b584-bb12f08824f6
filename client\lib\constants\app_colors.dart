import 'package:flutter/material.dart';

/// App color palette based on the new design system
class AppColors {
  // Primary colors
  static const Color surfCrest = Color(0xFFC7E3CE);
  static const Color pickledBluewood = Color(0xFF2F4750);
  static const Color acapulco = Color(0xFF7CBBA3);
  static const Color ecruWhite = Color(0xFFFBFBF3);
  static const Color copperRose = Color(0xFF91636D);
  static const Color apple = Color(0xFF6DB445);
  static const Color santasGray = Color(0xFF9FA0A6);
  static const Color neptune = Color(0xFF87C4AC);
  static const Color harp = Color(0xFFDAEAE4);
  static const Color feijoa = Color(0xFFA6D18E);

  // Semantic color assignments
  static const Color primary = acapulco; // Main brand color
  static const Color primaryDark = pickledBluewood; // Dark theme primary
  static const Color secondary = apple; // Success/positive actions
  static const Color accent = neptune; // Highlights and accents
  static const Color background =
      primary; // Consistent background throughout app
  static const Color backgroundDark = pickledBluewood; // Dark background
  static const Color surface = Colors.white; // White container/card backgrounds
  static const Color surfaceDark = Color(
    0xFF3A5A63,
  ); // Darker surface for dark mode
  static const Color error = copperRose; // Error states
  static const Color onPrimary = Colors.white; // Text on primary color
  static const Color onSecondary = Colors.white; // Text on secondary color
  static const Color onBackground = Colors.white; // White text throughout app
  static const Color onSurface = pickledBluewood; // Dark text on white surface
  static const Color textSecondary =
      santasGray; // Gray text for secondary content on white backgrounds

  // Light theme color scheme
  static const ColorScheme lightColorScheme = ColorScheme(
    brightness: Brightness.light,
    primary: primary,
    onPrimary: onPrimary,
    secondary: secondary,
    onSecondary: onSecondary,
    error: error,
    onError: Colors.white,
    surface: surface,
    onSurface: onSurface,
  );

  // Dark theme color scheme
  static const ColorScheme darkColorScheme = ColorScheme(
    brightness: Brightness.dark,
    primary: neptune,
    onPrimary: pickledBluewood,
    secondary: feijoa,
    onSecondary: pickledBluewood,
    error: copperRose,
    onError: Colors.white,
    surface: surfaceDark,
    onSurface: surfCrest,
  );
}
