# Export Files Directory

## 📁 Directory Purpose
This directory stores locally exported glucose reading files when using the `local` destination option.

## 📂 File Organization
Exported files are automatically organized with timestamps:
- `glucose_chart_line_2024-01-15T10-30-00-000Z.pdf`
- `glucose_chart_bar_2024-01-15T10-30-00-000Z.xlsx`
- `glucose_chart_area_2024-01-15T10-30-00-000Z.csv`
- `glucose_chart_scatter_2024-01-15T10-30-00-000Z.json`

## 🔄 File Lifecycle
- Files are created when using `destination=local` in export API
- Files are automatically cleaned up based on retention policies
- This directory is mounted in Docker for persistent storage

## 🔒 Security
- This directory is not committed to version control
- Files contain user-specific glucose data
- Access is restricted to the application

## 📊 Supported Export Formats
- **PDF**: Professional reports with charts and statistics
- **Excel**: Multi-sheet workbooks with data and charts
- **CSV**: Comma-separated values for data analysis
- **JSON**: Structured data with metadata
