import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../constants/app_colors.dart';
import '../../models/food_entry.dart';
import '../../providers/food_diary_provider.dart';
import 'food_entry_card.dart';

class MealSection extends StatelessWidget {
  final MealType mealType;

  const MealSection({super.key, required this.mealType});

  @override
  Widget build(BuildContext context) {
    return Consumer<FoodDiaryProvider>(
      builder: (context, provider, child) {
        final mealNutrition = provider.getMealNutrition(mealType);
        final isExpanded = provider.isMealExpanded(mealType);
        final entries = mealNutrition.entries;

        return Container(
          decoration: BoxDecoration(
            color: AppColors.surface,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            children: [
              // Meal header
              InkWell(
                onTap: () => provider.toggleMealExpansion(mealType),
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(16),
                  bottom: Radius.circular(16),
                ),
                child: Container(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: [
                      // Meal icon and color indicator
                      Container(
                        width: 48,
                        height: 48,
                        decoration: BoxDecoration(
                          color: mealType.color.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(24),
                        ),
                        child: Icon(
                          mealType.icon,
                          color: mealType.color,
                          size: 24,
                        ),
                      ),
                      const SizedBox(width: 16),

                      // Meal info
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Text(
                                  mealType.displayName,
                                  style: const TextStyle(
                                    fontSize: 20,
                                    fontWeight: FontWeight.bold,
                                    color: AppColors.onSurface,
                                  ),
                                ),
                                if (mealNutrition.mealTime != null) ...[
                                  const SizedBox(width: 8),
                                  Text(
                                    mealNutrition.formattedMealTime,
                                    style: const TextStyle(
                                      fontSize: 14,
                                      color: AppColors.textSecondary,
                                    ),
                                  ),
                                ],
                              ],
                            ),
                            const SizedBox(height: 4),
                            if (entries.isNotEmpty) ...[
                              Row(
                                children: [
                                  _buildNutrientChip(
                                    '${mealNutrition.totalCarbs.toStringAsFixed(0)}g carbs',
                                    Colors.orange,
                                  ),
                                  const SizedBox(width: 8),
                                  _buildNutrientChip(
                                    '${mealNutrition.totalCalories.toStringAsFixed(0)} cal',
                                    Colors.red,
                                  ),
                                  const SizedBox(width: 8),
                                  _buildBloodSugarImpactChip(
                                    mealNutrition.mealBloodSugarImpact,
                                  ),
                                ],
                              ),
                            ] else ...[
                              Text(
                                'No foods added yet',
                                style: const TextStyle(
                                  fontSize: 14,
                                  color: AppColors.textSecondary,
                                  fontStyle: FontStyle.italic,
                                ),
                              ),
                            ],
                          ],
                        ),
                      ),

                      // Expand/collapse icon
                      Icon(
                        isExpanded ? Icons.expand_less : Icons.expand_more,
                        color: AppColors.textSecondary,
                        size: 24,
                      ),
                    ],
                  ),
                ),
              ),

              // Expandable content
              if (isExpanded) ...[
                const Divider(height: 1, color: AppColors.textSecondary),
                if (entries.isNotEmpty) ...[
                  // Food entries list
                  Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      children:
                          entries.map((entry) {
                            return Padding(
                              padding: const EdgeInsets.only(bottom: 12),
                              child: FoodEntryCard(
                                entry: entry,
                                onEdit: () => _editFoodEntry(context, entry),
                                onDelete:
                                    () => _deleteFoodEntry(
                                      context,
                                      provider,
                                      entry,
                                    ),
                              ),
                            );
                          }).toList(),
                    ),
                  ),
                ] else ...[
                  // Empty state
                  Padding(
                    padding: const EdgeInsets.all(32),
                    child: Column(
                      children: [
                        Icon(
                          Icons.restaurant_outlined,
                          size: 48,
                          color: AppColors.textSecondary,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'No foods added for ${mealType.displayName.toLowerCase()}',
                          style: const TextStyle(
                            fontSize: 16,
                            color: AppColors.textSecondary,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 16),
                        ElevatedButton.icon(
                          onPressed: () => _addFoodToMeal(context, mealType),
                          icon: const Icon(Icons.add),
                          label: const Text('Add Food'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: mealType.color,
                            foregroundColor: Colors.white,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],

                // Add food button (when there are existing entries)
                if (entries.isNotEmpty) ...[
                  Padding(
                    padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                    child: SizedBox(
                      width: double.infinity,
                      child: OutlinedButton.icon(
                        onPressed: () => _addFoodToMeal(context, mealType),
                        icon: const Icon(Icons.add),
                        label: const Text('Add Food'),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: mealType.color,
                          side: BorderSide(color: mealType.color),
                        ),
                      ),
                    ),
                  ),
                ],
              ],
            ],
          ),
        );
      },
    );
  }

  Widget _buildNutrientChip(String text, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        text,
        style: TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w600,
          color: color,
        ),
      ),
    );
  }

  Widget _buildBloodSugarImpactChip(BloodSugarImpact impact) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: impact.color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(impact.icon, size: 12, color: impact.color),
          const SizedBox(width: 4),
          Text(
            impact.displayName.split(' ')[0], // Just "Low", "Moderate", "High"
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: impact.color,
            ),
          ),
        ],
      ),
    );
  }

  void _addFoodToMeal(BuildContext context, MealType mealType) {
    // This will be implemented in the add food modal
    // For now, we'll show a placeholder
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Add food to ${mealType.displayName} - Coming soon!'),
        backgroundColor: mealType.color,
      ),
    );
  }

  void _editFoodEntry(BuildContext context, FoodEntry entry) {
    // This will be implemented when we create the edit food modal
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Edit ${entry.name} - Coming soon!'),
        backgroundColor: AppColors.primary,
      ),
    );
  }

  void _deleteFoodEntry(
    BuildContext context,
    FoodDiaryProvider provider,
    FoodEntry entry,
  ) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Delete Food Entry'),
            content: Text('Are you sure you want to delete "${entry.name}"?'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () async {
                  Navigator.of(context).pop();
                  try {
                    await provider.deleteFoodEntry(entry.id);
                    if (context.mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('Deleted ${entry.name}'),
                          backgroundColor: Colors.green,
                        ),
                      );
                    }
                  } catch (e) {
                    if (context.mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('Failed to delete food entry: $e'),
                          backgroundColor: Colors.red,
                        ),
                      );
                    }
                  }
                },
                child: const Text(
                  'Delete',
                  style: TextStyle(color: Colors.red),
                ),
              ),
            ],
          ),
    );
  }
}
