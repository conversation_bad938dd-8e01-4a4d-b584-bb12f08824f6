import { Request, Response } from 'express';
import mongoose from 'mongoose';
import { createClient } from 'redis';
import { dbManager } from '../config/db';
import logger from '../utils/logger';
import { env } from '../config/env';
import { catchAsync } from '../utils/errorHandler';

// System health status interface
interface HealthStatus {
    status: 'healthy' | 'degraded' | 'unhealthy';
    timestamp: string;
    uptime: number;
    version: string;
    environment: string;
    services: {
        database: ServiceHealth;
        redis?: ServiceHealth;
        memory: ServiceHealth;
        disk?: ServiceHealth;
    };
    details?: any;
}

interface ServiceHealth {
    status: 'healthy' | 'degraded' | 'unhealthy';
    responseTime?: number;
    details?: any;
    lastChecked: string;
}

// Memory usage thresholds (in MB)
const MEMORY_WARNING_THRESHOLD = 512; // 512MB
const MEMORY_CRITICAL_THRESHOLD = 1024; // 1GB

// @desc    Get basic health status
// @route   GET /api/health
// @access  Public
export const getHealthStatus = catchAsync(async (req: Request, res: Response): Promise<void> => {
    const startTime = Date.now();
    
    try {
        // Check database health
        const dbHealth = await checkDatabaseHealth();

        // Check Redis health
        const redisHealth = await checkRedisHealth();

        // Check memory usage
        const memoryHealth = checkMemoryHealth();

        // Determine overall status
        const services = {
            database: dbHealth,
            redis: redisHealth,
            memory: memoryHealth
        };
        
        const overallStatus = determineOverallStatus(services);
        
        const healthStatus: HealthStatus = {
            status: overallStatus,
            timestamp: new Date().toISOString(),
            uptime: process.uptime(),
            version: process.env.npm_package_version || '1.0.0',
            environment: env.NODE_ENV,
            services
        };

        // Log health check (map degraded to unhealthy for logger)
        const logStatus = overallStatus === 'degraded' ? 'unhealthy' : overallStatus as 'healthy' | 'unhealthy';
        logger.health('system', logStatus, {
            responseTime: Date.now() - startTime,
            services: Object.keys(services).map(key => ({
                name: key,
                status: services[key as keyof typeof services].status
            }))
        });

        const statusCode = overallStatus === 'healthy' ? 200 : 
                          overallStatus === 'degraded' ? 200 : 503;

        res.status(statusCode).json({
            success: true,
            data: healthStatus
        });
    } catch (error) {
        logger.error('Health check failed', { error: (error as Error).message });
        
        res.status(503).json({
            success: false,
            data: {
                status: 'unhealthy',
                timestamp: new Date().toISOString(),
                uptime: process.uptime(),
                version: process.env.npm_package_version || '1.0.0',
                environment: env.NODE_ENV,
                error: 'Health check failed'
            }
        });
    }
});

// @desc    Get detailed health status
// @route   GET /api/health/detailed
// @access  Private (Admin only)
export const getDetailedHealthStatus = catchAsync(async (req: Request, res: Response): Promise<void> => {
    const startTime = Date.now();
    
    try {
        // Check all services
        const dbHealth = await checkDatabaseHealth();
        const redisHealth = await checkRedisHealth();
        const memoryHealth = checkMemoryHealth();
        const diskHealth = await checkDiskHealth();

        // Get additional system information
        const systemInfo = getSystemInfo();

        const services = {
            database: dbHealth,
            redis: redisHealth,
            memory: memoryHealth,
            disk: diskHealth
        };
        
        const overallStatus = determineOverallStatus(services);
        
        const detailedHealth: HealthStatus = {
            status: overallStatus,
            timestamp: new Date().toISOString(),
            uptime: process.uptime(),
            version: process.env.npm_package_version || '1.0.0',
            environment: env.NODE_ENV,
            services,
            details: {
                system: systemInfo,
                responseTime: Date.now() - startTime,
                requestId: req.headers['x-request-id']
            }
        };

        // Log health check (map degraded to unhealthy for logger)
        const detailedLogStatus = overallStatus === 'degraded' ? 'unhealthy' : overallStatus as 'healthy' | 'unhealthy';
        logger.health('detailed-system', detailedLogStatus, {
            responseTime: Date.now() - startTime,
            userId: (req as any).user?.id
        });

        const statusCode = overallStatus === 'healthy' ? 200 : 
                          overallStatus === 'degraded' ? 200 : 503;

        res.status(statusCode).json({
            success: true,
            data: detailedHealth
        });
    } catch (error) {
        logger.error('Detailed health check failed', { error: (error as Error).message });
        
        res.status(503).json({
            success: false,
            error: 'Detailed health check failed'
        });
    }
});

// @desc    Get database health status
// @route   GET /api/health/database
// @access  Private
export const getDatabaseHealth = catchAsync(async (req: Request, res: Response): Promise<void> => {
    const dbHealth = await checkDatabaseHealth();

    const statusCode = dbHealth.status === 'healthy' ? 200 : 503;

    res.status(statusCode).json({
        success: true,
        data: dbHealth
    });
});

// @desc    Get Redis health status
// @route   GET /api/health/redis
// @access  Private
export const getRedisHealth = catchAsync(async (req: Request, res: Response): Promise<void> => {
    const redisHealth = await checkRedisHealth();

    const statusCode = redisHealth.status === 'healthy' ? 200 : 503;

    res.status(statusCode).json({
        success: true,
        data: redisHealth
    });
});

// Helper functions
async function checkDatabaseHealth(): Promise<ServiceHealth> {
    const startTime = Date.now();
    
    try {
        // Use the database manager health check
        const healthCheck = await dbManager.healthCheck();
        const responseTime = Date.now() - startTime;
        
        return {
            status: healthCheck.status === 'healthy' ? 'healthy' : 'unhealthy',
            responseTime,
            details: healthCheck.details,
            lastChecked: new Date().toISOString()
        };
    } catch (error) {
        return {
            status: 'unhealthy',
            responseTime: Date.now() - startTime,
            details: {
                error: (error as Error).message,
                connected: false
            },
            lastChecked: new Date().toISOString()
        };
    }
}

async function checkRedisHealth(): Promise<ServiceHealth> {
    const startTime = Date.now();

    try {
        // Create a temporary Redis client for health check
        const redisUrl = process.env.REDIS_URL || 'redis://localhost:6379';
        const client = createClient({ url: redisUrl });

        // Set a timeout for the connection
        const timeoutPromise = new Promise((_, reject) => {
            setTimeout(() => reject(new Error('Redis connection timeout')), 5000);
        });

        // Try to connect and ping
        await Promise.race([
            client.connect(),
            timeoutPromise
        ]);

        const pingResult = await client.ping();
        const responseTime = Date.now() - startTime;

        // Get Redis info
        const info = await client.info();
        const infoString = typeof info === 'string' ? info : String(info);
        const redisVersion = infoString.split('\n').find((line: string) => line.startsWith('redis_version:'))?.split(':')[1]?.trim();
        const connectedClients = infoString.split('\n').find((line: string) => line.startsWith('connected_clients:'))?.split(':')[1]?.trim();
        const usedMemory = infoString.split('\n').find((line: string) => line.startsWith('used_memory_human:'))?.split(':')[1]?.trim();

        await client.disconnect();

        return {
            status: pingResult === 'PONG' ? 'healthy' : 'unhealthy',
            responseTime,
            details: {
                connected: true,
                version: redisVersion,
                connectedClients: connectedClients,
                usedMemory: usedMemory,
                url: redisUrl.replace(/\/\/.*@/, '//***:***@') // Hide credentials in logs
            },
            lastChecked: new Date().toISOString()
        };
    } catch (error) {
        return {
            status: 'unhealthy',
            responseTime: Date.now() - startTime,
            details: {
                connected: false,
                error: (error as Error).message,
                url: process.env.REDIS_URL?.replace(/\/\/.*@/, '//***:***@') || 'redis://localhost:6379'
            },
            lastChecked: new Date().toISOString()
        };
    }
}

function checkMemoryHealth(): ServiceHealth {
    const memoryUsage = process.memoryUsage();
    const usedMemoryMB = Math.round(memoryUsage.heapUsed / 1024 / 1024);
    const totalMemoryMB = Math.round(memoryUsage.heapTotal / 1024 / 1024);
    
    let status: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';
    
    if (usedMemoryMB > MEMORY_CRITICAL_THRESHOLD) {
        status = 'unhealthy';
    } else if (usedMemoryMB > MEMORY_WARNING_THRESHOLD) {
        status = 'degraded';
    }
    
    return {
        status,
        details: {
            used: `${usedMemoryMB}MB`,
            total: `${totalMemoryMB}MB`,
            percentage: Math.round((usedMemoryMB / totalMemoryMB) * 100),
            rss: `${Math.round(memoryUsage.rss / 1024 / 1024)}MB`,
            external: `${Math.round(memoryUsage.external / 1024 / 1024)}MB`
        },
        lastChecked: new Date().toISOString()
    };
}

async function checkDiskHealth(): Promise<ServiceHealth> {
    try {
        const fs = require('fs').promises;
        const stats = await fs.stat(process.cwd());
        
        return {
            status: 'healthy',
            details: {
                accessible: true,
                workingDirectory: process.cwd()
            },
            lastChecked: new Date().toISOString()
        };
    } catch (error) {
        return {
            status: 'unhealthy',
            details: {
                accessible: false,
                error: (error as Error).message
            },
            lastChecked: new Date().toISOString()
        };
    }
}

function getSystemInfo() {
    return {
        platform: process.platform,
        arch: process.arch,
        nodeVersion: process.version,
        pid: process.pid,
        uptime: process.uptime(),
        cpuUsage: process.cpuUsage(),
        memoryUsage: process.memoryUsage(),
        loadAverage: require('os').loadavg(),
        freeMemory: `${Math.round(require('os').freemem() / 1024 / 1024)}MB`,
        totalMemory: `${Math.round(require('os').totalmem() / 1024 / 1024)}MB`
    };
}

function determineOverallStatus(services: any): 'healthy' | 'degraded' | 'unhealthy' {
    const statuses = Object.values(services).map((service: any) => service.status);
    
    if (statuses.includes('unhealthy')) {
        return 'unhealthy';
    }
    
    if (statuses.includes('degraded')) {
        return 'degraded';
    }
    
    return 'healthy';
}

// @desc    Readiness probe for Kubernetes/Docker
// @route   GET /api/health/ready
// @access  Public
export const getReadinessProbe = catchAsync(async (req: Request, res: Response): Promise<void> => {
    const isReady = dbManager.isHealthy();
    
    if (isReady) {
        res.status(200).json({ status: 'ready' });
    } else {
        res.status(503).json({ status: 'not ready' });
    }
});

// @desc    Liveness probe for Kubernetes/Docker
// @route   GET /api/health/live
// @access  Public
export const getLivenessProbe = catchAsync(async (req: Request, res: Response): Promise<void> => {
    // Simple liveness check - if we can respond, we're alive
    res.status(200).json({ 
        status: 'alive',
        timestamp: new Date().toISOString(),
        uptime: process.uptime()
    });
});
