import { env } from './env';
import logger from '../utils/logger';

/**
 * Development-specific configuration and utilities
 */

export interface DevelopmentConfig {
    enableDebugMode: boolean;
    enableVerboseLogging: boolean;
    enableRequestLogging: boolean;
    enablePerformanceMonitoring: boolean;
    enableMemoryMonitoring: boolean;
    enableDatabaseQueryLogging: boolean;
    mockExternalServices: boolean;
    seedDatabase: boolean;
    enableHotReload: boolean;
    corsOrigins: string[];
    rateLimiting: {
        enabled: boolean;
        windowMs: number;
        max: number;
    };
}

const developmentConfig: DevelopmentConfig = {
    enableDebugMode: true,
    enableVerboseLogging: true,
    enableRequestLogging: true,
    enablePerformanceMonitoring: true,
    enableMemoryMonitoring: true,
    enableDatabaseQueryLogging: true,
    mockExternalServices: false,
    seedDatabase: false,
    enableHotReload: true,
    corsOrigins: ['http://localhost:3000', 'http://127.0.0.1:3000'],
    rateLimiting: {
        enabled: false, // Disabled in development for easier testing
        windowMs: 15 * 60 * 1000, // 15 minutes
        max: 1000 // Much higher limit for development
    }
};

const productionConfig: DevelopmentConfig = {
    enableDebugMode: false,
    enableVerboseLogging: false,
    enableRequestLogging: true,
    enablePerformanceMonitoring: true,
    enableMemoryMonitoring: false,
    enableDatabaseQueryLogging: false,
    mockExternalServices: false,
    seedDatabase: false,
    enableHotReload: false,
    corsOrigins: [], // Set specific production domains
    rateLimiting: {
        enabled: true,
        windowMs: 15 * 60 * 1000, // 15 minutes
        max: 100 // Stricter limit for production
    }
};

export const getConfig = (): DevelopmentConfig => {
    return env.NODE_ENV === 'development' ? developmentConfig : productionConfig;
};

/**
 * Development utilities
 */
export class DevUtils {
    private static config = getConfig();

    static isDevelopment(): boolean {
        return env.NODE_ENV === 'development';
    }

    static isProduction(): boolean {
        return env.NODE_ENV === 'production';
    }

    static isTest(): boolean {
        return env.NODE_ENV === 'test';
    }

    static shouldEnableFeature(feature: keyof DevelopmentConfig): boolean {
        return this.config[feature] as boolean;
    }

    static getFeatureConfig<T>(feature: keyof DevelopmentConfig): T {
        return this.config[feature] as T;
    }

    static logEnvironmentInfo(): void {
        logger.info('Environment configuration loaded', {
            environment: env.NODE_ENV,
            config: this.config,
            nodeVersion: process.version,
            platform: process.platform,
            arch: process.arch
        });
    }

    static validateEnvironment(): void {
        const requiredEnvVars = [
            'NODE_ENV',
            'PORT',
            'MONGODB_URI',
            'JWT_SECRET'
        ];

        const missing = requiredEnvVars.filter(varName => !process.env[varName]);

        if (missing.length > 0) {
            logger.error('Missing required environment variables', { missing });
            throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
        }

        logger.info('Environment validation passed');
    }

    static setupDevelopmentHelpers(): void {
        if (!this.isDevelopment()) return;

        // Add global development helpers
        (global as any).dev = {
            logger,
            env,
            config: this.config,
            memoryUsage: () => process.memoryUsage(),
            uptime: () => process.uptime(),
            gc: () => {
                if (global.gc) {
                    global.gc();
                    logger.debug('Manual garbage collection triggered');
                } else {
                    logger.warn('Garbage collection not available. Start with --expose-gc flag.');
                }
            }
        };

        logger.debug('Development helpers added to global scope');
    }

    static createMockData(): any {
        if (!this.isDevelopment()) return null;

        return {
            users: [
                {
                    email: '<EMAIL>',
                    password: 'admin123',
                    role: 'admin',
                    name: 'Admin User'
                },
                {
                    email: '<EMAIL>',
                    password: 'user123',
                    role: 'user',
                    name: 'Test User'
                }
            ],
            glucoseReadings: [
                { value: 120, timestamp: new Date(), context: 'fasting' },
                { value: 140, timestamp: new Date(), context: 'after_meal' },
                { value: 110, timestamp: new Date(), context: 'before_meal' }
            ]
        };
    }

    static enableDevelopmentMiddleware(app: any): void {
        if (!this.isDevelopment()) return;

        // Development-only middleware
        app.use('/dev', (req: any, res: any) => {
            res.json({
                environment: env.NODE_ENV,
                config: this.config,
                memoryUsage: process.memoryUsage(),
                uptime: process.uptime(),
                version: process.version,
                platform: process.platform
            });
        });

        logger.debug('Development middleware enabled');
    }
}

/**
 * Performance monitoring utilities
 */
export class PerformanceMonitor {
    private static timers: Map<string, number> = new Map();

    static start(label: string): void {
        this.timers.set(label, Date.now());
    }

    static end(label: string): number {
        const startTime = this.timers.get(label);
        if (!startTime) {
            logger.warn(`Performance timer '${label}' was not started`);
            return 0;
        }

        const duration = Date.now() - startTime;
        this.timers.delete(label);

        if (DevUtils.shouldEnableFeature('enablePerformanceMonitoring')) {
            logger.performance(`Performance: ${label}`, duration);
        }

        return duration;
    }

    static measure<T>(label: string, fn: () => T): T;
    static measure<T>(label: string, fn: () => Promise<T>): Promise<T>;
    static measure<T>(label: string, fn: () => T | Promise<T>): T | Promise<T> {
        this.start(label);
        
        try {
            const result = fn();
            
            if (result instanceof Promise) {
                return result.finally(() => this.end(label));
            } else {
                this.end(label);
                return result;
            }
        } catch (error) {
            this.end(label);
            throw error;
        }
    }
}

/**
 * Memory monitoring utilities
 */
export class MemoryMonitor {
    private static interval: NodeJS.Timeout | null = null;

    static start(intervalMs: number = 30000): void {
        if (!DevUtils.shouldEnableFeature('enableMemoryMonitoring')) return;

        this.stop(); // Stop existing monitor

        this.interval = setInterval(() => {
            const usage = process.memoryUsage();
            const usedMB = Math.round(usage.heapUsed / 1024 / 1024);
            
            logger.debug('Memory usage', {
                heapUsed: `${usedMB}MB`,
                heapTotal: `${Math.round(usage.heapTotal / 1024 / 1024)}MB`,
                rss: `${Math.round(usage.rss / 1024 / 1024)}MB`,
                external: `${Math.round(usage.external / 1024 / 1024)}MB`
            });

            // Warn if memory usage is high
            if (usedMB > 512) {
                logger.warn('High memory usage detected', { usedMB });
            }
        }, intervalMs);

        logger.debug('Memory monitoring started');
    }

    static stop(): void {
        if (this.interval) {
            clearInterval(this.interval);
            this.interval = null;
            logger.debug('Memory monitoring stopped');
        }
    }
}

// Initialize development configuration
export const initializeDevelopmentConfig = (): void => {
    DevUtils.validateEnvironment();
    DevUtils.logEnvironmentInfo();
    DevUtils.setupDevelopmentHelpers();
    
    if (DevUtils.shouldEnableFeature('enableMemoryMonitoring')) {
        MemoryMonitor.start();
    }
};

export default {
    getConfig,
    DevUtils,
    PerformanceMonitor,
    MemoryMonitor,
    initializeDevelopmentConfig
};
