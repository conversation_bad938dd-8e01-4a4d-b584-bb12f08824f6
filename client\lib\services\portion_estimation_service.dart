import 'dart:typed_data';
import 'package:flutter/material.dart';
import '../models/food_recognition_result.dart';

/// Service for AI-powered portion size estimation
class PortionEstimationService {
  /// Estimate portion size using AI analysis
  static Future<PortionEstimation?> estimatePortionSize(
    Uint8List imageBytes,
    String foodName, {
    Rect? foodBoundingBox,
  }) async {
    try {
      debugPrint('🔍 Starting portion estimation for: $foodName');

      // Analyze image for reference objects
      final referenceObjects = await _detectReferenceObjects(imageBytes);

      if (referenceObjects.isNotEmpty) {
        // Use reference object for scale
        return _calculatePortionWithReference(
          imageBytes,
          foodName,
          referenceObjects.first,
          foodBoundingBox,
        );
      } else {
        // Use default estimation based on food type
        return _getDefaultPortionEstimation(foodName);
      }
    } catch (e) {
      debugPrint('💥 Portion estimation error: $e');
      return null;
    }
  }

  /// Detect reference objects in the image
  static Future<List<ReferenceObject>> _detectReferenceObjects(
    Uint8List imageBytes,
  ) async {
    try {
      // Simplified reference object detection
      // In a real implementation, this would use computer vision
      // to detect common objects like coins, cards, utensils, etc.

      final detectedObjects = <ReferenceObject>[];

      // For demo purposes, simulate finding a plate
      // In reality, this would analyze the image
      final hasPlate = await _simulateObjectDetection(imageBytes, 'plate');

      if (hasPlate) {
        detectedObjects.add(
          const ReferenceObject(
            name: 'plate',
            boundingBox: Rect.fromLTWH(50, 50, 300, 300),
            knownSize: 250.0, // 250mm diameter
            unit: 'mm',
          ),
        );
      }

      debugPrint('🔍 Detected ${detectedObjects.length} reference objects');
      return detectedObjects;
    } catch (e) {
      debugPrint('💥 Reference object detection error: $e');
      return [];
    }
  }

  /// Simulate object detection (placeholder for real AI)
  static Future<bool> _simulateObjectDetection(
    Uint8List imageBytes,
    String objectType,
  ) async {
    // Simulate processing time
    await Future.delayed(const Duration(milliseconds: 100));

    // Simulate detection probability based on image characteristics
    final imageSize = imageBytes.length;

    // Larger images more likely to contain reference objects
    final detectionProbability = (imageSize / 1000000).clamp(0.0, 0.8);

    return detectionProbability > 0.5;
  }

  /// Calculate portion size using reference object
  static PortionEstimation _calculatePortionWithReference(
    Uint8List imageBytes,
    String foodName,
    ReferenceObject referenceObject,
    Rect? foodBoundingBox,
  ) {
    try {
      debugPrint(
        '📏 Calculating portion with reference: ${referenceObject.name}',
      );

      // Calculate scale factor from reference object
      final referencePixelSize = referenceObject.boundingBox.width;
      final referenceRealSize = referenceObject.knownSize;
      final pixelsPerMm = referencePixelSize / referenceRealSize;

      // Estimate food dimensions
      double foodPixelSize;
      if (foodBoundingBox != null) {
        foodPixelSize = (foodBoundingBox.width + foodBoundingBox.height) / 2;
      } else {
        // Use default relative size
        foodPixelSize =
            referencePixelSize * 0.6; // Assume food is 60% of reference
      }

      final foodRealSize = foodPixelSize / pixelsPerMm;

      // Convert size to weight based on food type
      final estimatedWeight = _convertSizeToWeight(foodName, foodRealSize);

      return PortionEstimation(
        estimatedWeight: estimatedWeight,
        unit: 'g',
        confidence: 0.8, // High confidence with reference object
        method: 'REFERENCE_OBJECT',
        referenceObjects: [referenceObject],
      );
    } catch (e) {
      debugPrint('💥 Reference calculation error: $e');
      return _getDefaultPortionEstimation(foodName);
    }
  }

  /// Convert physical size to weight based on food type
  static double _convertSizeToWeight(String foodName, double sizeInMm) {
    // Food density and shape factors (simplified)
    final foodProperties = _getFoodProperties(foodName);

    // Estimate volume based on size (assuming roughly spherical/cylindrical)
    final volumeCm3 = (sizeInMm / 10) * (sizeInMm / 10) * (sizeInMm / 10) * 0.5;

    // Convert to weight using density
    final density = foodProperties['density'] ?? 0.8; // Default density if null
    final weight = volumeCm3 * density;

    return weight.clamp(10.0, 1000.0); // Reasonable weight range
  }

  /// Get food properties for weight calculation
  static Map<String, double> _getFoodProperties(String foodName) {
    final lowerName = foodName.toLowerCase();

    // Density in g/cm³ (simplified values)
    if (lowerName.contains('apple') || lowerName.contains('fruit')) {
      return {'density': 0.8};
    } else if (lowerName.contains('bread') || lowerName.contains('pap')) {
      return {'density': 0.3};
    } else if (lowerName.contains('meat') ||
        lowerName.contains('chicken') ||
        lowerName.contains('beef') ||
        lowerName.contains('boerewors')) {
      return {'density': 1.0};
    } else if (lowerName.contains('vegetable') ||
        lowerName.contains('morogo')) {
      return {'density': 0.6};
    } else if (lowerName.contains('rice') || lowerName.contains('grain')) {
      return {'density': 0.7};
    }

    return {'density': 0.8}; // Default density
  }

  /// Get default portion estimation without reference objects
  static PortionEstimation _getDefaultPortionEstimation(String foodName) {
    final defaultWeight = _getDefaultPortionWeight(foodName);

    return PortionEstimation(
      estimatedWeight: defaultWeight,
      unit: 'g',
      confidence: 0.6, // Lower confidence without reference
      method: 'DEFAULT_ESTIMATION',
    );
  }

  /// Get default portion weights for common foods
  static double _getDefaultPortionWeight(String foodName) {
    final lowerName = foodName.toLowerCase();

    // Default portion sizes for South African foods
    final portionMap = {
      'apple': 150.0,
      'banana': 120.0,
      'orange': 180.0,
      'bread': 30.0,
      'slice': 30.0,
      'rice': 150.0,
      'pap': 200.0,
      'morogo': 100.0,
      'spinach': 100.0,
      'chicken': 100.0,
      'beef': 100.0,
      'boerewors': 80.0,
      'sausage': 80.0,
      'potato': 150.0,
      'sweet potato': 120.0,
      'tomato': 100.0,
      'onion': 80.0,
      'carrot': 60.0,
      'beans': 120.0,
      'lentils': 120.0,
      'milk': 250.0,
      'yogurt': 125.0,
      'cheese': 30.0,
      'egg': 50.0,
      'fish': 100.0,
      'pasta': 100.0,
      'cereal': 40.0,
    };

    // Find best match
    for (final entry in portionMap.entries) {
      if (lowerName.contains(entry.key)) {
        return entry.value;
      }
    }

    return 100.0; // Default portion
  }

  /// Provide visual portion guides for users
  static List<PortionGuide> getPortionGuides(String foodName) {
    final lowerName = foodName.toLowerCase();

    if (lowerName.contains('meat') ||
        lowerName.contains('chicken') ||
        lowerName.contains('beef') ||
        lowerName.contains('fish')) {
      return [
        const PortionGuide(
          description: 'Palm of your hand',
          weight: 100.0,
          visualReference: '🤚',
        ),
        const PortionGuide(
          description: 'Deck of cards',
          weight: 85.0,
          visualReference: '🃏',
        ),
      ];
    } else if (lowerName.contains('rice') ||
        lowerName.contains('pap') ||
        lowerName.contains('pasta')) {
      return [
        const PortionGuide(
          description: 'Cupped hand',
          weight: 150.0,
          visualReference: '🤲',
        ),
        const PortionGuide(
          description: 'Tennis ball',
          weight: 140.0,
          visualReference: '🎾',
        ),
      ];
    } else if (lowerName.contains('vegetable') ||
        lowerName.contains('morogo')) {
      return [
        const PortionGuide(
          description: 'Two cupped hands',
          weight: 200.0,
          visualReference: '🤲🤲',
        ),
        const PortionGuide(
          description: 'Baseball',
          weight: 180.0,
          visualReference: '⚾',
        ),
      ];
    }

    return [
      const PortionGuide(
        description: 'Standard serving',
        weight: 100.0,
        visualReference: '🍽️',
      ),
    ];
  }

  /// Adjust portion based on user feedback
  static PortionEstimation adjustPortion(
    PortionEstimation original,
    double userEstimatedWeight,
  ) {
    return PortionEstimation(
      estimatedWeight: userEstimatedWeight,
      unit: original.unit,
      confidence: 0.9, // High confidence for user input
      method: 'USER_ADJUSTED',
      referenceObjects: original.referenceObjects,
    );
  }
}

/// Visual portion guide for users
class PortionGuide {
  final String description;
  final double weight;
  final String visualReference;

  const PortionGuide({
    required this.description,
    required this.weight,
    required this.visualReference,
  });
}
