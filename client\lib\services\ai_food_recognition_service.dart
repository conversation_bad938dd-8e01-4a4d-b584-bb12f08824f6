import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:http/http.dart' as http;
import 'package:flutter/services.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:permission_handler/permission_handler.dart';
import '../models/food_entry.dart';
import '../models/food_recognition_result.dart';
import '../constants/app_colors.dart';
import '../constants/south_african_foods.dart';

class AIFoodRecognitionService {
  static String get _visionApiKey => dotenv.env['GOOGLE_VISION_API_KEY'] ?? '';
  static const String _visionApiUrl =
      'https://vision.googleapis.com/v1/images:annotate';

  /// Request camera permission for food recognition
  static Future<bool> requestCameraPermission() async {
    final status = await Permission.camera.request();
    return status == PermissionStatus.granted;
  }

  /// Check if camera permission is granted
  static Future<bool> hasCameraPermission() async {
    final status = await Permission.camera.status;
    return status == PermissionStatus.granted;
  }

  /// Show food recognition modal with camera/gallery options
  static Future<FoodEntry?> showFoodRecognitionModal(
    BuildContext context, {
    MealType mealType = MealType.breakfast,
  }) async {
    // Check camera permission first
    final hasPermission = await hasCameraPermission();
    if (!hasPermission) {
      final granted = await requestCameraPermission();
      if (!granted) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text(
                'Camera permission is required for food recognition',
              ),
              backgroundColor: Colors.red,
            ),
          );
        }
        return null;
      }
    }

    if (!context.mounted) return null;

    return await showModalBottomSheet<FoodEntry>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => FoodRecognitionModal(mealType: mealType),
    );
  }

  /// Take photo and recognize food
  static Future<FoodEntry?> recognizeFood(
    ImageSource source, {
    MealType mealType = MealType.breakfast,
  }) async {
    try {
      debugPrint('🔍 Starting food recognition with source: $source');

      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: source,
        maxWidth: 1024,
        maxHeight: 1024,
        imageQuality: 85,
      );

      if (image == null) {
        debugPrint('❌ No image selected');
        return null;
      }

      debugPrint('✅ Image captured: ${image.path}');

      // Convert image to base64
      final bytes = await image.readAsBytes();
      final base64Image = base64Encode(bytes);

      debugPrint('📷 Image size: ${bytes.length} bytes');
      debugPrint('🔄 Starting offline recognition...');

      // First try offline recognition for common SA foods
      final offlineResult = await _recognizeOffline(bytes, mealType: mealType);
      if (offlineResult != null) {
        debugPrint('✅ Offline recognition successful: ${offlineResult.name}');
        return offlineResult;
      }

      debugPrint('❌ Offline recognition failed, trying online...');

      // Then try online AI recognition
      final onlineResult = await _recognizeOnline(
        base64Image,
        mealType: mealType,
      );
      if (onlineResult != null) {
        debugPrint('✅ Online recognition successful: ${onlineResult.name}');
        return onlineResult;
      }

      debugPrint('❌ Both offline and online recognition failed');

      // Fallback: Create a generic food entry when recognition fails
      debugPrint('🍽️ Creating fallback food entry for manual editing');
      return FoodEntry(
        id: 'unrecognized_${DateTime.now().millisecondsSinceEpoch}',
        name: 'Unrecognized Food',
        calories: 100,
        carbohydrates: 20,
        protein: 5,
        fat: 3,
        fiber: 2,
        glycemicIndex: GlycemicIndex.medium,
        category: FoodCategory.grains,
        portion: '100g (estimated)',
        portionSize: 100.0,
        unit: 'g',
        mealType: mealType,
        timestamp: DateTime.now(),
        notes: 'Food not recognized - please edit details manually',
        brand: null,
        barcode: null,
        isCustom: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
    } catch (e) {
      debugPrint('💥 Error in food recognition: $e');
      return null;
    }
  }

  /// Offline recognition using local SA food patterns
  static Future<FoodEntry?> _recognizeOffline(
    Uint8List imageBytes, {
    MealType mealType = MealType.breakfast,
  }) async {
    try {
      debugPrint('🎨 Starting offline color analysis...');

      // Simple color/texture analysis for common SA foods
      // This is a simplified approach - in production, you'd use a trained model

      // Analyze image properties (simplified)
      final colorAnalysis = _analyzeImageColors(imageBytes);
      debugPrint('🎨 Color analysis result: $colorAnalysis');

      // Match against SA food patterns
      final recognizedFood = _matchSAFoodPatterns(colorAnalysis);

      if (recognizedFood != null) {
        debugPrint('🍽️ Offline match found: ${recognizedFood.name}');
        return _createFoodEntryFromSAFood(recognizedFood, mealType: mealType);
      }

      debugPrint('❌ No offline pattern match found');
      return null;
    } catch (e) {
      debugPrint('💥 Offline recognition error: $e');
      return null;
    }
  }

  /// Online AI recognition using Google Vision API
  static Future<FoodEntry?> _recognizeOnline(
    String base64Image, {
    MealType mealType = MealType.breakfast,
  }) async {
    try {
      debugPrint('🌐 Starting online recognition with Google Vision API');

      // Check if API key is available
      if (_visionApiKey.isEmpty) {
        debugPrint('❌ Google Vision API key not configured');
        return null;
      }

      debugPrint('🔑 Using API key: ${_visionApiKey.substring(0, 5)}...');
      debugPrint('🌐 Sending request to Vision API...');

      final response = await http
          .post(
            Uri.parse('$_visionApiUrl?key=$_visionApiKey'),
            headers: {'Content-Type': 'application/json'},
            body: jsonEncode({
              'requests': [
                {
                  'image': {'content': base64Image},
                  'features': [
                    {'type': 'LABEL_DETECTION', 'maxResults': 10},
                    {'type': 'OBJECT_LOCALIZATION', 'maxResults': 10},
                  ],
                },
              ],
            }),
          )
          .timeout(const Duration(seconds: 10));

      debugPrint('📡 API response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        debugPrint('✅ API request successful');
        final data = jsonDecode(response.body);
        return _parseVisionApiResponse(data, targetMealType: mealType);
      } else {
        debugPrint('❌ API request failed: ${response.body}');
      }

      return null;
    } catch (e) {
      debugPrint('Online recognition error: $e');
      return null;
    }
  }

  /// Analyze image colors for pattern matching
  static Map<String, dynamic> _analyzeImageColors(Uint8List imageBytes) {
    // Simplified color analysis
    // In production, this would use proper image processing

    int redSum = 0, greenSum = 0, blueSum = 0;
    int pixelCount = imageBytes.length ~/ 3;

    for (int i = 0; i < imageBytes.length - 2; i += 3) {
      redSum += imageBytes[i];
      greenSum += imageBytes[i + 1];
      blueSum += imageBytes[i + 2];
    }

    return {
      'avgRed': redSum / pixelCount,
      'avgGreen': greenSum / pixelCount,
      'avgBlue': blueSum / pixelCount,
      'dominantColor': _getDominantColor(redSum, greenSum, blueSum),
    };
  }

  /// Match color patterns to SA foods
  static SAFood? _matchSAFoodPatterns(Map<String, dynamic> colorAnalysis) {
    final dominantColor = colorAnalysis['dominantColor'] as String;
    final avgRed = colorAnalysis['avgRed'] as double;
    final avgGreen = colorAnalysis['avgGreen'] as double;

    // Simple pattern matching for common SA foods
    if (dominantColor == 'white' && avgRed > 200 && avgGreen > 200) {
      return SouthAfricanFoods.pap;
    } else if (dominantColor == 'green' && avgGreen > 150) {
      return SouthAfricanFoods.morogo;
    } else if (dominantColor == 'brown' && avgRed > 100) {
      return SouthAfricanFoods.boerewors;
    } else if (dominantColor == 'yellow' && avgRed > 180) {
      return SouthAfricanFoods.sampAndBeans;
    }

    return null;
  }

  /// Get dominant color from RGB values
  static String _getDominantColor(int red, int green, int blue) {
    if (red > green && red > blue) return 'red';
    if (green > red && green > blue) return 'green';
    if (blue > red && blue > green) return 'blue';
    if (red > 200 && green > 200 && blue > 200) return 'white';
    if (red < 100 && green < 100 && blue < 100) return 'black';
    if (red > 150 && green > 100 && blue < 100) return 'brown';
    if (red > 200 && green > 200 && blue < 150) return 'yellow';
    return 'mixed';
  }

  /// Create FoodEntry from recognized SA food
  static FoodEntry _createFoodEntryFromSAFood(
    SAFood saFood, {
    MealType mealType = MealType.breakfast,
  }) {
    return FoodEntry(
      id: 'ai_recognized_${DateTime.now().millisecondsSinceEpoch}',
      name: saFood.name,
      carbohydrates: saFood.carbsPer100g,
      calories: saFood.caloriesPer100g,
      protein: saFood.proteinPer100g,
      fat: saFood.fatPer100g,
      fiber: saFood.fiberPer100g,
      glycemicIndex: saFood.glycemicIndex,
      category: saFood.category,
      portion: '100g (estimated)',
      portionSize: 100.0,
      unit: 'g',
      mealType: mealType,
      timestamp: DateTime.now(),
      notes: 'AI recognized from photo',
      brand: null, // No brand for AI recognized foods
      barcode: null, // No barcode for AI recognized foods
      isCustom: false,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  /// Parse Google Vision API response
  static FoodEntry? _parseVisionApiResponse(
    Map<String, dynamic> data, {
    required MealType targetMealType,
  }) {
    try {
      debugPrint('🔍 Parsing Vision API response...');
      debugPrint('📄 Response data: ${data.toString().substring(0, 200)}...');

      final responses = data['responses'] as List;
      if (responses.isEmpty) {
        debugPrint('❌ No responses in API data');
        return null;
      }

      final annotations = responses[0]['labelAnnotations'] as List?;
      if (annotations == null || annotations.isEmpty) {
        debugPrint('❌ No label annotations found');
        return null;
      }

      debugPrint('🏷️ Found ${annotations.length} label annotations');

      // Look for food-related labels, prioritizing specific foods over generic terms
      final validLabels = <Map<String, dynamic>>[];

      for (final annotation in annotations) {
        final description = annotation['description'] as String;
        final confidence = annotation['score'] as double;

        debugPrint(
          '🏷️ Label: "$description" (confidence: ${(confidence * 100).toStringAsFixed(1)}%)',
        );

        if (confidence > 0.7 && _isFoodLabel(description)) {
          validLabels.add({
            'description': description,
            'confidence': confidence,
            'specificity': _getFoodSpecificity(description),
          });
        }
      }

      // Sort by specificity first, then confidence
      validLabels.sort((a, b) {
        final specificityCompare = b['specificity'].compareTo(a['specificity']);
        if (specificityCompare != 0) return specificityCompare;
        return b['confidence'].compareTo(a['confidence']);
      });

      // Process labels in order of specificity and confidence
      for (final label in validLabels) {
        final description = label['description'] as String;
        debugPrint(
          '✅ Processing food label: "$description" (specificity: ${label['specificity']})',
        );

        // Try to match with SA foods first (only for specific foods)
        if (label['specificity'] > 1) {
          final saFood = SouthAfricanFoods.findByName(description);
          if (saFood != null) {
            debugPrint('🍽️ SA food match: ${saFood.name}');
            return _createFoodEntryFromSAFood(saFood, mealType: targetMealType);
          }
        }

        // Create generic food entry
        debugPrint('🍽️ Creating generic food entry for: $description');
        return _createGenericFoodEntry(description, mealType: targetMealType);
      }

      debugPrint('❌ No food labels found with sufficient confidence');
      return null;
    } catch (e) {
      debugPrint('💥 Error parsing Vision API response: $e');
      return null;
    }
  }

  /// Check if label is food-related
  static bool _isFoodLabel(String label) {
    final foodKeywords = [
      // General food terms
      'food',
      'fruit',
      'vegetable',
      'meat',
      'bread',
      'rice',
      'pasta',
      'chicken',
      'beef',
      'fish',
      'apple',
      'banana',
      'potato',
      'tomato',
      'corn',
      'maize',
      'porridge',
      'stew',
      'soup',
      'salad',
      'grain',
      'cereal',
      // South African specific foods
      'pap',
      'morogo',
      'boerewors',
      'biltong',
      'samp',
      'beans',
      'potjiekos',
      'amadumbe',
      'mielie',
      'mealie',
      'mageu',
      'mopane',
      'baobab',
      'marula',
      'rooibos',
      'sosatie',
      'vetkoek',
      'koeksister',
      'beskuit',
      'rusks',
      // Zulu food terms
      'iphutu',
      'umngqusho',
      'amasi',
      // Afrikaans food terms
      'vleis',
      'groente',
      'vrugte',
    ];

    return foodKeywords.any(
      (keyword) => label.toLowerCase().contains(keyword.toLowerCase()),
    );
  }

  /// Get food specificity score (higher = more specific)
  static int _getFoodSpecificity(String label) {
    final lowerLabel = label.toLowerCase();

    // Very generic terms (lowest priority)
    final veryGeneric = ['food', 'dish', 'meal', 'snack', 'ingredient'];
    if (veryGeneric.contains(lowerLabel)) return 0;

    // Descriptive/category terms (very low priority)
    final descriptive = [
      'natural foods',
      'organic foods',
      'fresh foods',
      'healthy foods',
      'produce',
      'superfood',
      'whole foods',
      'raw foods',
    ];
    if (descriptive.contains(lowerLabel)) return 0;

    // Generic categories (low priority)
    final generic = [
      'fruit',
      'fruits',
      'vegetable',
      'vegetables',
      'meat',
      'grain',
      'dairy',
      'beverage',
      'bread',
      'rice',
    ];
    if (generic.contains(lowerLabel)) return 1;

    // Specific food names (highest priority)
    final specificFoods = [
      'apple', 'banana', 'orange', 'tomato', 'potato', 'chicken', 'beef',
      'pineapple', 'mango', 'grape', 'strawberry', 'carrot', 'onion',
      'garlic', 'pepper', 'corn', 'rice', 'pasta', 'cheese', 'milk',
      // Banana varieties
      'cooking banana', 'saba banana', 'plantain', 'matoke',
    ];
    if (specificFoods.any(
      (food) => lowerLabel.contains(food) || food.contains(lowerLabel),
    )) {
      return 3;
    }

    // Default for other food terms
    return 2;
  }

  /// Determine food category based on food name
  static FoodCategory _determineFoodCategory(String foodName) {
    final lowerName = foodName.toLowerCase();

    // Fruits
    if ([
      'banana',
      'apple',
      'orange',
      'grape',
      'strawberry',
      'pineapple',
      'mango',
      'peach',
      'pear',
      'cherry',
      'plum',
      'kiwi',
      'watermelon',
      'melon',
      'papaya',
      'avocado',
      'lemon',
      'lime',
      'fruit',
    ].any((fruit) => lowerName.contains(fruit))) {
      return FoodCategory.fruits;
    }

    // Vegetables
    if ([
      'vegetable',
      'tomato',
      'carrot',
      'broccoli',
      'spinach',
      'lettuce',
      'onion',
      'garlic',
      'pepper',
      'cucumber',
      'cabbage',
      'potato',
      'sweet potato',
      'corn',
      'peas',
      'beans',
    ].any((veg) => lowerName.contains(veg))) {
      return FoodCategory.vegetables;
    }

    // Proteins
    if ([
      'chicken',
      'beef',
      'pork',
      'fish',
      'salmon',
      'tuna',
      'egg',
      'meat',
      'protein',
      'turkey',
      'lamb',
      'shrimp',
      'crab',
    ].any((protein) => lowerName.contains(protein))) {
      return FoodCategory.proteins;
    }

    // Dairy
    if ([
      'milk',
      'cheese',
      'yogurt',
      'butter',
      'cream',
      'dairy',
    ].any((dairy) => lowerName.contains(dairy))) {
      return FoodCategory.dairy;
    }

    // Grains and starches
    if ([
      'bread',
      'rice',
      'pasta',
      'cereal',
      'oats',
      'wheat',
      'grain',
      'noodle',
      'quinoa',
      'barley',
    ].any((grain) => lowerName.contains(grain))) {
      return FoodCategory.grains;
    }

    // Default to grains if no match
    return FoodCategory.grains;
  }

  /// Create generic food entry for unrecognized foods
  static FoodEntry _createGenericFoodEntry(
    String foodName, {
    MealType mealType = MealType.breakfast,
  }) {
    // Determine category based on food name
    final category = _determineFoodCategory(foodName);

    return FoodEntry(
      id: 'ai_generic_${DateTime.now().millisecondsSinceEpoch}',
      name: foodName,
      carbohydrates: 20.0, // Default estimates
      calories: 150.0,
      protein: 5.0,
      fat: 3.0,
      fiber: 2.0,
      glycemicIndex: GlycemicIndex.medium,
      category: category,
      portion: '100g (estimated)',
      portionSize: 100.0,
      unit: 'g',
      mealType: mealType,
      timestamp: DateTime.now(),
      notes: 'AI recognized - please verify nutrition data',
      brand: null, // No brand for AI recognized foods
      barcode: null, // No barcode for AI recognized foods
      isCustom: true,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  /// Enhanced offline recognition for real-time analysis (quick)
  static Future<FoodEntry?> recognizeOfflineQuick(
    Uint8List imageBytes, {
    MealType mealType = MealType.breakfast,
  }) async {
    try {
      // Simplified analysis for speed
      final colorAnalysis = _analyzeImageColors(imageBytes);
      final recognizedFood = _matchSAFoodPatterns(colorAnalysis);

      if (recognizedFood != null) {
        return _createFoodEntryFromSAFood(recognizedFood, mealType: mealType);
      }

      return null;
    } catch (e) {
      debugPrint('💥 Quick offline recognition error: $e');
      return null;
    }
  }

  /// Enhanced offline recognition with multi-detection
  static Future<dynamic> recognizeOfflineEnhanced(
    Uint8List imageBytes, {
    MealType mealType = MealType.breakfast,
    bool enableMultiDetection = false,
  }) async {
    try {
      debugPrint('🔍 Enhanced offline recognition started');

      if (enableMultiDetection) {
        // Multi-food detection logic
        final detectedFoods = <FoodEntry>[];

        // Analyze different regions of the image
        final regions = _divideImageIntoRegions(imageBytes);

        for (final region in regions) {
          final colorAnalysis = _analyzeImageColors(region);
          final recognizedFood = _matchSAFoodPatterns(colorAnalysis);

          if (recognizedFood != null) {
            final foodEntry = _createFoodEntryFromSAFood(
              recognizedFood,
              mealType: mealType,
            );
            detectedFoods.add(foodEntry);
          }
        }

        return detectedFoods.isNotEmpty ? detectedFoods : null;
      } else {
        // Single food detection
        return recognizeOfflineQuick(imageBytes, mealType: mealType);
      }
    } catch (e) {
      debugPrint('💥 Enhanced offline recognition error: $e');
      return null;
    }
  }

  /// Enhanced online recognition with multi-detection
  static Future<dynamic> recognizeOnlineEnhanced(
    Uint8List imageBytes, {
    MealType mealType = MealType.breakfast,
    bool enableMultiDetection = false,
  }) async {
    try {
      debugPrint('🌐 Enhanced online recognition started');

      if (_visionApiKey.isEmpty) {
        debugPrint('❌ Google Vision API key not configured');
        return null;
      }

      final base64Image = base64Encode(imageBytes);

      final features = [
        {'type': 'LABEL_DETECTION', 'maxResults': 15},
        {'type': 'OBJECT_LOCALIZATION', 'maxResults': 15},
      ];

      if (enableMultiDetection) {
        features.add({'type': 'CROP_HINTS', 'maxResults': 10});
      }

      final response = await http
          .post(
            Uri.parse('$_visionApiUrl?key=$_visionApiKey'),
            headers: {'Content-Type': 'application/json'},
            body: jsonEncode({
              'requests': [
                {
                  'image': {'content': base64Image},
                  'features': features,
                },
              ],
            }),
          )
          .timeout(const Duration(seconds: 15));

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return _parseEnhancedVisionApiResponse(
          data,
          targetMealType: mealType,
          enableMultiDetection: enableMultiDetection,
        );
      }

      return null;
    } catch (e) {
      debugPrint('💥 Enhanced online recognition error: $e');
      return null;
    }
  }

  /// Divide image into regions for multi-detection
  static List<Uint8List> _divideImageIntoRegions(Uint8List imageBytes) {
    // Simplified region division
    // In a real implementation, this would use proper image processing
    return [imageBytes]; // For now, return the whole image
  }

  /// Parse enhanced Vision API response with multi-detection
  static dynamic _parseEnhancedVisionApiResponse(
    Map<String, dynamic> data, {
    MealType targetMealType = MealType.breakfast,
    bool enableMultiDetection = false,
  }) {
    try {
      final responses = data['responses'] as List;
      if (responses.isEmpty) return null;

      final response = responses[0];
      final objects = response['localizedObjectAnnotations'] as List? ?? [];

      if (enableMultiDetection && objects.isNotEmpty) {
        // Multi-food detection using object localization
        final detectedFoods = <FoodEntry>[];

        for (final obj in objects) {
          final name = obj['name'] as String;
          final confidence = obj['score'] as double;

          if (confidence > 0.6 && _isFoodLabel(name)) {
            final foodEntry = _createGenericFoodEntry(
              name,
              mealType: targetMealType,
            );
            detectedFoods.add(foodEntry);
          }
        }

        return detectedFoods.isNotEmpty ? detectedFoods : null;
      } else {
        // Single food detection using labels
        return _parseVisionApiResponse(data, targetMealType: targetMealType);
      }
    } catch (e) {
      debugPrint('💥 Enhanced parsing error: $e');
      return null;
    }
  }

  /// Estimate portion size using AI
  static PortionEstimation? estimatePortionSize(
    Uint8List imageBytes,
    String foodName,
  ) {
    try {
      // Simplified portion estimation
      // In a real implementation, this would use computer vision
      // to detect reference objects and calculate relative sizes

      final estimatedWeight = _getDefaultPortionWeight(foodName);

      return PortionEstimation(
        estimatedWeight: estimatedWeight,
        unit: 'g',
        confidence: 0.7,
        method: 'AI_ESTIMATION',
      );
    } catch (e) {
      debugPrint('💥 Portion estimation error: $e');
      return null;
    }
  }

  /// Get default portion weight for food
  static double _getDefaultPortionWeight(String foodName) {
    // Default portion sizes for common foods
    final portionMap = {
      'apple': 150.0,
      'banana': 120.0,
      'bread': 30.0,
      'rice': 150.0,
      'chicken': 100.0,
      'beef': 100.0,
      'pap': 200.0,
      'morogo': 100.0,
      'boerewors': 80.0,
    };

    final lowerName = foodName.toLowerCase();
    for (final entry in portionMap.entries) {
      if (lowerName.contains(entry.key)) {
        return entry.value;
      }
    }

    return 100.0; // Default portion
  }
}

class FoodRecognitionModal extends StatefulWidget {
  final MealType mealType;

  const FoodRecognitionModal({super.key, this.mealType = MealType.breakfast});

  @override
  State<FoodRecognitionModal> createState() => _FoodRecognitionModalState();
}

class _FoodRecognitionModalState extends State<FoodRecognitionModal> {
  bool _isProcessing = false;

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.4,
      decoration: const BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
                const Expanded(
                  child: Text(
                    'AI Food Recognition',
                    style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                    textAlign: TextAlign.center,
                  ),
                ),
                const SizedBox(width: 48),
              ],
            ),
          ),

          // Options
          Expanded(
            child:
                _isProcessing
                    ? const Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          CircularProgressIndicator(color: AppColors.primary),
                          SizedBox(height: 16),
                          Text('Recognizing food...'),
                        ],
                      ),
                    )
                    : Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        children: [
                          _buildOptionCard(
                            icon: Icons.camera_alt,
                            title: 'Take Photo',
                            subtitle: 'Capture food with camera',
                            onTap: () => _recognizeFood(ImageSource.camera),
                          ),
                          const SizedBox(height: 16),
                          _buildOptionCard(
                            icon: Icons.photo_library,
                            title: 'Choose from Gallery',
                            subtitle: 'Select existing photo',
                            onTap: () => _recognizeFood(ImageSource.gallery),
                          ),
                        ],
                      ),
                    ),
          ),
        ],
      ),
    );
  }

  Widget _buildOptionCard({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border.all(color: AppColors.primary.withValues(alpha: 0.3)),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: AppColors.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(24),
              ),
              child: Icon(icon, color: AppColors.primary),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Text(
                    subtitle,
                    style: const TextStyle(
                      fontSize: 14,
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
            const Icon(Icons.arrow_forward_ios, size: 16),
          ],
        ),
      ),
    );
  }

  void _recognizeFood(ImageSource source) async {
    setState(() {
      _isProcessing = true;
    });

    try {
      final recognizedFood = await AIFoodRecognitionService.recognizeFood(
        source,
        mealType: widget.mealType,
      );

      if (mounted) {
        setState(() {
          _isProcessing = false;
        });

        if (recognizedFood != null) {
          Navigator.of(context).pop(recognizedFood);
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text(
                'Could not recognize food. Please try manual entry.',
              ),
              backgroundColor: Colors.orange,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isProcessing = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error recognizing food: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
