import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;
import '../models/food_entry.dart';
import '../models/recipe.dart';
import '../services/backend_service.dart';
import '../services/food_diary_service.dart';

class RecipeService {
  static const String _recipesKey = 'user_recipes';

  /// Get user's custom recipes
  static Future<List<Recipe>> getRecipes() async {
    try {
      // Try to get from backend first
      final response = await http
          .get(
            Uri.parse('${BackendService.baseUrl}/food-diary/recipes'),
            headers: {'Content-Type': 'application/json'},
          )
          .timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final List<dynamic> recipesJson = data['recipes'] ?? [];
        return recipesJson.map((json) => Recipe.fromJson(json)).toList();
      }
    } catch (e) {
      debugPrint('Error fetching recipes from backend: $e');
    }

    // Fallback to local storage
    return await _getLocalRecipes();
  }

  /// Get local recipes from SharedPreferences
  static Future<List<Recipe>> _getLocalRecipes() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final recipesJson = prefs.getStringList(_recipesKey) ?? [];

      return recipesJson
          .map((json) => Recipe.fromJson(jsonDecode(json)))
          .toList();
    } catch (e) {
      debugPrint('Error loading local recipes: $e');
      return [];
    }
  }

  /// Save a new recipe
  static Future<bool> saveRecipe(Recipe recipe) async {
    try {
      // Try to save to backend first
      final response = await http
          .post(
            Uri.parse('${BackendService.baseUrl}/food-diary/recipes'),
            headers: {'Content-Type': 'application/json'},
            body: jsonEncode(recipe.toJson()),
          )
          .timeout(const Duration(seconds: 10));

      if (response.statusCode == 200 || response.statusCode == 201) {
        // Also save locally as backup
        await _saveLocalRecipe(recipe);
        return true;
      }
    } catch (e) {
      debugPrint('Error saving recipe to backend: $e');
    }

    // Fallback to local storage
    return await _saveLocalRecipe(recipe);
  }

  /// Save recipe locally
  static Future<bool> _saveLocalRecipe(Recipe recipe) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final recipes = await _getLocalRecipes();

      recipes.add(recipe);
      final recipesJson = recipes.map((r) => jsonEncode(r.toJson())).toList();

      await prefs.setStringList(_recipesKey, recipesJson);
      return true;
    } catch (e) {
      debugPrint('Error saving local recipe: $e');
      return false;
    }
  }

  /// Update an existing recipe
  static Future<bool> updateRecipe(Recipe recipe) async {
    try {
      // Try to update on backend first
      final response = await http
          .put(
            Uri.parse(
              '${BackendService.baseUrl}/food-diary/recipes/${recipe.id}',
            ),
            headers: {'Content-Type': 'application/json'},
            body: jsonEncode(recipe.toJson()),
          )
          .timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        // Also update locally
        await _updateLocalRecipe(recipe);
        return true;
      }
    } catch (e) {
      debugPrint('Error updating recipe on backend: $e');
    }

    // Fallback to local storage
    return await _updateLocalRecipe(recipe);
  }

  /// Update recipe locally
  static Future<bool> _updateLocalRecipe(Recipe recipe) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final recipes = await _getLocalRecipes();

      final index = recipes.indexWhere((r) => r.id == recipe.id);
      if (index != -1) {
        recipes[index] = recipe;
        final recipesJson = recipes.map((r) => jsonEncode(r.toJson())).toList();
        await prefs.setStringList(_recipesKey, recipesJson);
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('Error updating local recipe: $e');
      return false;
    }
  }

  /// Delete a recipe
  static Future<bool> deleteRecipe(String recipeId) async {
    try {
      // Try to delete from backend first
      final response = await http
          .delete(
            Uri.parse('${BackendService.baseUrl}/food-diary/recipes/$recipeId'),
            headers: {'Content-Type': 'application/json'},
          )
          .timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        // Also delete locally
        await _deleteLocalRecipe(recipeId);
        return true;
      }
    } catch (e) {
      debugPrint('Error deleting recipe from backend: $e');
    }

    // Fallback to local storage
    return await _deleteLocalRecipe(recipeId);
  }

  /// Delete recipe locally
  static Future<bool> _deleteLocalRecipe(String recipeId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final recipes = await _getLocalRecipes();

      recipes.removeWhere((r) => r.id == recipeId);
      final recipesJson = recipes.map((r) => jsonEncode(r.toJson())).toList();

      await prefs.setStringList(_recipesKey, recipesJson);
      return true;
    } catch (e) {
      debugPrint('Error deleting local recipe: $e');
      return false;
    }
  }

  /// Convert recipe to food entry
  static FoodEntry recipeToFoodEntry(
    Recipe recipe, {
    required MealType mealType,
    double servings = 1.0,
  }) {
    // Calculate total nutrition for the recipe
    double totalCalories = 0;
    double totalCarbs = 0;
    double totalProtein = 0;
    double totalFat = 0;
    double totalFiber = 0;

    for (final ingredient in recipe.ingredients) {
      final multiplier =
          ingredient.amount / 100; // Assuming nutrition is per 100g
      totalCalories += ingredient.calories * multiplier;
      totalCarbs += ingredient.carbohydrates * multiplier;
      totalProtein += ingredient.protein * multiplier;
      totalFat += ingredient.fat * multiplier;
      totalFiber += ingredient.fiber * multiplier;
    }

    // Adjust for servings
    totalCalories *= servings;
    totalCarbs *= servings;
    totalProtein *= servings;
    totalFat *= servings;
    totalFiber *= servings;

    return FoodEntry(
      id: FoodDiaryService.generateId(),
      name: recipe.name,
      carbohydrates: totalCarbs,
      calories: totalCalories,
      protein: totalProtein,
      fat: totalFat,
      fiber: totalFiber,
      glycemicIndex: recipe.estimatedGlycemicIndex,
      category: recipe.category,
      portion:
          '${servings.toStringAsFixed(1)} serving${servings != 1 ? 's' : ''}',
      portionSize: recipe.totalWeight * servings,
      unit: 'g',
      mealType: mealType,
      timestamp: DateTime.now(),
      notes: 'Recipe: ${recipe.description ?? ''}',
      isCustom: true,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  /// Get recipe suggestions based on available ingredients
  static Future<List<Recipe>> getRecipeSuggestions(
    List<String> availableIngredients,
  ) async {
    final recipes = await getRecipes();

    return recipes.where((recipe) {
      final recipeIngredients =
          recipe.ingredients.map((i) => i.name.toLowerCase()).toList();
      final available =
          availableIngredients.map((i) => i.toLowerCase()).toList();

      // Check if at least 50% of ingredients are available
      final matchCount =
          recipeIngredients
              .where(
                (ingredient) => available.any(
                  (available) =>
                      available.contains(ingredient) ||
                      ingredient.contains(available),
                ),
              )
              .length;

      return matchCount >= (recipeIngredients.length * 0.5);
    }).toList();
  }

  /// Get popular recipe templates
  static List<Recipe> getRecipeTemplates() {
    return [
      Recipe(
        id: 'template_1',
        name: 'Basic Smoothie',
        description: 'A healthy fruit smoothie template',
        category: FoodCategory.beverages,
        servings: 1,
        prepTime: 5,
        cookTime: 0,
        difficulty: RecipeDifficulty.easy,
        ingredients: [
          RecipeIngredient(
            name: 'Banana',
            amount: 100,
            unit: 'g',
            calories: 89,
            carbohydrates: 23,
            protein: 1.1,
            fat: 0.3,
            fiber: 2.6,
          ),
          RecipeIngredient(
            name: 'Greek Yogurt',
            amount: 150,
            unit: 'g',
            calories: 100,
            carbohydrates: 6,
            protein: 17,
            fat: 0.4,
            fiber: 0,
          ),
        ],
        instructions: [
          'Add banana and yogurt to blender',
          'Blend until smooth',
          'Serve immediately',
        ],
        tags: ['healthy', 'quick', 'breakfast'],
        estimatedGlycemicIndex: GlycemicIndex.low,
        totalWeight: 250,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      Recipe(
        id: 'template_2',
        name: 'Simple Salad',
        description: 'A basic green salad template',
        category: FoodCategory.vegetables,
        servings: 1,
        prepTime: 10,
        cookTime: 0,
        difficulty: RecipeDifficulty.easy,
        ingredients: [
          RecipeIngredient(
            name: 'Mixed Greens',
            amount: 100,
            unit: 'g',
            calories: 20,
            carbohydrates: 4,
            protein: 2,
            fat: 0.3,
            fiber: 2,
          ),
          RecipeIngredient(
            name: 'Olive Oil',
            amount: 10,
            unit: 'ml',
            calories: 88,
            carbohydrates: 0,
            protein: 0,
            fat: 10,
            fiber: 0,
          ),
        ],
        instructions: [
          'Wash and dry greens',
          'Drizzle with olive oil',
          'Toss and serve',
        ],
        tags: ['healthy', 'vegetarian', 'low-carb'],
        estimatedGlycemicIndex: GlycemicIndex.low,
        totalWeight: 110,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
    ];
  }
}
