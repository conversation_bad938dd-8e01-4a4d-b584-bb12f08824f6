import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:timezone/timezone.dart' as tz;
import 'package:timezone/data/latest.dart' as tz;
import '../models/medication.dart';
import '../models/medication_reminder.dart';

class NotificationService {
  static bool _initialized = false;
  static bool _permissionGranted = false;
  static final FlutterLocalNotificationsPlugin _notifications =
      FlutterLocalNotificationsPlugin();

  // Initialize notification service
  static Future<void> initialize() async {
    if (_initialized) return;

    try {
      // Initialize timezone data
      tz.initializeTimeZones();

      // Android initialization settings
      const AndroidInitializationSettings initializationSettingsAndroid =
          AndroidInitializationSettings('@mipmap/ic_launcher');

      // iOS initialization settings
      const DarwinInitializationSettings initializationSettingsIOS =
          DarwinInitializationSettings(
            requestAlertPermission: true,
            requestBadgePermission: true,
            requestSoundPermission: true,
          );

      // Combined initialization settings
      const InitializationSettings initializationSettings =
          InitializationSettings(
            android: initializationSettingsAndroid,
            iOS: initializationSettingsIOS,
          );

      // Initialize the plugin
      await _notifications.initialize(
        initializationSettings,
        onDidReceiveNotificationResponse: _onNotificationTapped,
      );

      _initialized = true;
      debugPrint('Notification service initialized successfully');
    } catch (e) {
      debugPrint('Failed to initialize notification service: $e');
      _initialized = true;
      _permissionGranted = false;
    }
  }

  // Request notification permissions
  static Future<bool> requestPermissions() async {
    debugPrint('=== REQUESTING NOTIFICATION PERMISSIONS ===');

    if (!_initialized) {
      debugPrint('Not initialized, initializing first...');
      await initialize();
    }

    try {
      debugPrint('Requesting Android notification permissions...');

      // Request permissions for Android 13+
      final bool? result =
          await _notifications
              .resolvePlatformSpecificImplementation<
                AndroidFlutterLocalNotificationsPlugin
              >()
              ?.requestNotificationsPermission();

      _permissionGranted = result ?? true;
      debugPrint('Permission request result: $result');
      debugPrint('Final permission granted status: $_permissionGranted');

      // Also check if notifications are enabled at system level
      final androidImpl =
          _notifications
              .resolvePlatformSpecificImplementation<
                AndroidFlutterLocalNotificationsPlugin
              >();
      if (androidImpl != null) {
        final bool? areNotificationsEnabled =
            await androidImpl.areNotificationsEnabled();
        debugPrint('System notifications enabled: $areNotificationsEnabled');
      }

      return _permissionGranted;
    } catch (e) {
      debugPrint('Failed to request notification permissions: $e');
      _permissionGranted = false;
      return false;
    }
  }

  // Check if notifications are enabled
  static bool get isEnabled => _initialized && _permissionGranted;

  // Schedule medication reminder notification
  static Future<void> scheduleMedicationReminder(
    MedicationReminder reminder,
    Medication medication,
  ) async {
    if (!isEnabled) {
      debugPrint('Notifications not enabled, skipping reminder scheduling');
      return;
    }

    try {
      // Calculate notification time (subtract reminder minutes)
      final notificationTime = reminder.scheduledTime.subtract(
        Duration(minutes: medication.reminderMinutesBefore),
      );

      // Only schedule if the notification time is in the future
      if (notificationTime.isAfter(DateTime.now())) {
        await _scheduleNotification(
          id: reminder.id.hashCode,
          title: 'Medication Reminder',
          body:
              'Time to take ${medication.displayName} (${medication.dosageDisplay})',
          scheduledTime: notificationTime,
          payload: reminder.id,
        );

        debugPrint(
          'Scheduled notification for ${medication.displayName} at $notificationTime',
        );
      }
    } catch (e) {
      debugPrint('Failed to schedule medication reminder: $e');
    }
  }

  // Schedule multiple reminders for a medication
  static Future<void> scheduleMedicationReminders(
    List<MedicationReminder> reminders,
    Medication medication,
  ) async {
    for (final reminder in reminders) {
      await scheduleMedicationReminder(reminder, medication);
    }
  }

  // Cancel medication reminder notification
  static Future<void> cancelMedicationReminder(String reminderId) async {
    if (!isEnabled) return;

    try {
      await _cancelNotification(reminderId.hashCode);
      debugPrint('Cancelled notification for reminder: $reminderId');
    } catch (e) {
      debugPrint('Failed to cancel medication reminder: $e');
    }
  }

  // Cancel all reminders for a medication
  static Future<void> cancelMedicationReminders(String medicationId) async {
    if (!isEnabled) return;

    try {
      // Get all pending notifications
      final List<PendingNotificationRequest> pending =
          await _notifications.pendingNotificationRequests();

      // Cancel notifications that match this medication
      for (final notification in pending) {
        if (notification.payload?.contains(medicationId) == true) {
          await _notifications.cancel(notification.id);
        }
      }

      debugPrint('Cancelled all notifications for medication: $medicationId');
    } catch (e) {
      debugPrint('Failed to cancel medication reminders: $e');
    }
  }

  // Show immediate notification (for testing or immediate alerts)
  static Future<void> showImmediateNotification({
    required String title,
    required String body,
    String? payload,
  }) async {
    debugPrint('=== NOTIFICATION DEBUG ===');
    debugPrint('Initialized: $_initialized');
    debugPrint('Permission granted: $_permissionGranted');
    debugPrint('Is enabled: $isEnabled');

    if (!isEnabled) {
      debugPrint('Notifications not enabled, cannot show notification');
      debugPrint('Trying to re-initialize...');

      // Try to re-initialize
      await initialize();
      await requestPermissions();

      debugPrint(
        'After re-init - Initialized: $_initialized, Permission: $_permissionGranted',
      );

      if (!isEnabled) {
        debugPrint('Still not enabled after re-initialization');
        return;
      }
    }

    try {
      debugPrint('Attempting to show notification: $title');
      await _showNotification(
        id: DateTime.now().millisecondsSinceEpoch,
        title: title,
        body: body,
        payload: payload,
      );
      debugPrint('Successfully showed immediate notification: $title');
    } catch (e) {
      debugPrint('Failed to show immediate notification: $e');
      rethrow;
    }
  }

  // Notification tap handler
  static void _onNotificationTapped(NotificationResponse response) {
    debugPrint('Notification tapped with payload: ${response.payload}');
    handleNotificationTap(response.payload);
  }

  // Private method to schedule a notification
  static Future<void> _scheduleNotification({
    required int id,
    required String title,
    required String body,
    required DateTime scheduledTime,
    String? payload,
  }) async {
    if (!isEnabled) return;

    try {
      // Create notification details
      const AndroidNotificationDetails androidDetails =
          AndroidNotificationDetails(
            'medication_reminders',
            'Medication Reminders',
            channelDescription: 'Notifications for medication reminders',
            importance: Importance.high,
            priority: Priority.high,
            showWhen: true,
            enableVibration: true,
            playSound: true,
          );

      const DarwinNotificationDetails iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );

      const NotificationDetails notificationDetails = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      // Convert DateTime to TZDateTime
      final tz.TZDateTime scheduledTZ = tz.TZDateTime.from(
        scheduledTime,
        tz.local,
      );

      // Schedule the notification
      await _notifications.zonedSchedule(
        id,
        title,
        body,
        scheduledTZ,
        notificationDetails,
        payload: payload,
        androidScheduleMode: AndroidScheduleMode.inexactAllowWhileIdle,
      );

      debugPrint('Scheduled notification $id: $title at $scheduledTime');
    } catch (e) {
      debugPrint('Failed to schedule notification: $e');
    }
  }

  // Private method to show immediate notification
  static Future<void> _showNotification({
    required int id,
    required String title,
    required String body,
    String? payload,
  }) async {
    if (!isEnabled) return;

    try {
      // Create notification details
      const AndroidNotificationDetails androidDetails =
          AndroidNotificationDetails(
            'medication_reminders',
            'Medication Reminders',
            channelDescription: 'Notifications for medication reminders',
            importance: Importance.high,
            priority: Priority.high,
            showWhen: true,
            enableVibration: true,
            playSound: true,
          );

      const DarwinNotificationDetails iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );

      const NotificationDetails notificationDetails = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      // Show the notification immediately
      await _notifications.show(
        id,
        title,
        body,
        notificationDetails,
        payload: payload,
      );

      debugPrint('Showed immediate notification $id: $title');
    } catch (e) {
      debugPrint('Failed to show notification: $e');
    }
  }

  // Private method to cancel a notification
  static Future<void> _cancelNotification(int id) async {
    if (!isEnabled) return;

    try {
      await _notifications.cancel(id);
      debugPrint('Cancelled notification $id');
    } catch (e) {
      debugPrint('Failed to cancel notification: $e');
    }
  }

  // Get pending notifications count (for debugging)
  static Future<int> getPendingNotificationsCount() async {
    if (!isEnabled) return 0;

    try {
      final List<PendingNotificationRequest> pending =
          await _notifications.pendingNotificationRequests();
      return pending.length;
    } catch (e) {
      debugPrint('Failed to get pending notifications count: $e');
      return 0;
    }
  }

  // Clear all notifications
  static Future<void> clearAllNotifications() async {
    if (!isEnabled) return;

    try {
      await _notifications.cancelAll();
      debugPrint('Cleared all notifications');
    } catch (e) {
      debugPrint('Failed to clear all notifications: $e');
    }
  }

  // Handle notification tap (callback for when user taps notification)
  static void handleNotificationTap(String? payload) {
    if (payload != null) {
      debugPrint('Notification tapped with payload: $payload');
      // In a real implementation, you would navigate to the appropriate screen
      // or perform the relevant action based on the payload
    }
  }

  // Test notification (for development/testing)
  static Future<void> testNotification() async {
    await showImmediateNotification(
      title: 'GlucoMonitor Test',
      body: 'Real notification system is working! 🎉',
      payload: 'test',
    );
  }
}

// Extension to add notification helpers to MedicationReminder
extension MedicationReminderNotification on MedicationReminder {
  // Get notification ID for this reminder
  int get notificationId => id.hashCode;

  // Check if this reminder should have a notification
  bool get shouldNotify {
    return status == ReminderStatus.pending &&
        scheduledTime.isAfter(DateTime.now());
  }

  // Get notification title
  String getNotificationTitle(Medication medication) {
    return 'Medication Reminder';
  }

  // Get notification body
  String getNotificationBody(Medication medication) {
    return 'Time to take ${medication.displayName} (${medication.dosageDisplay})';
  }
}

// Extension to add notification helpers to Medication
extension MedicationNotification on Medication {
  // Check if this medication should send notifications
  bool get shouldSendNotifications {
    return isActive && reminderEnabled;
  }

  // Get notification channel ID
  String get notificationChannelId {
    return 'medication_reminders';
  }

  // Get notification channel name
  String get notificationChannelName {
    return 'Medication Reminders';
  }

  // Get notification channel description
  String get notificationChannelDescription {
    return 'Notifications for medication reminders';
  }
}
