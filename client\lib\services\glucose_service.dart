import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart';
import '../models/glucose_reading.dart';
import '../utils/retry_utils.dart';
import './offline_queue.dart';
import './backend_service.dart';

class GlucoseService {
  static final OfflineQueue _offlineQueue = OfflineQueue();
  static final ValueNotifier<String?> retryStatus = ValueNotifier<String?>(
    null,
  );

  static final _defaultRetryConfig = RetryConfig(
    maxAttempts: 3,
    initialDelay: const Duration(seconds: 1),
    backoffMultiplier: 2.0,
    maxDelay: const Duration(seconds: 10),
  );

  static bool _shouldRetryRequest(Exception e) {
    if (e is http.ClientException) {
      return true;
    }
    if (e is RetryException) {
      return false;
    }
    return false;
  }

  static void _updateRetryStatus(String message) {
    retryStatus.value = message;
    Future.delayed(const Duration(seconds: 3), () {
      if (retryStatus.value == message) {
        retryStatus.value = null;
      }
    });
  }

  // Create a new glucose reading
  static Future<GlucoseReading> createGlucoseReading({
    required String token,
    required GlucoseReading reading,
  }) async {
    try {
      return await withRetry(
        operation: () async {
          final response = await http.post(
            Uri.parse('${BackendService.baseUrl}/glucose'),
            headers: {
              'Content-Type': 'application/json',
              'Authorization': 'Bearer $token',
            },
            body: jsonEncode(reading.toCreateJson()),
          );

          debugPrint('Create glucose reading response: ${response.statusCode}');
          debugPrint('Response body: ${response.body}');

          if (response.statusCode == 201) {
            final data = jsonDecode(response.body);
            return GlucoseReading.fromJson(data['data']);
          }

          final errorData = jsonDecode(response.body);
          throw Exception(
            errorData['message'] ?? 'Failed to create glucose reading',
          );
        },
        operationName: 'createGlucoseReading',
        config: _defaultRetryConfig,
        shouldRetry: _shouldRetryRequest,
      );
    } catch (e) {
      _updateRetryStatus(
        'Failed to save glucose reading. Will retry when online.',
      );

      // Queue for offline sync
      await _offlineQueue.add(
        QueuedOperation(
          type: 'createGlucoseReading',
          data: {'token': token, 'reading': reading.toCreateJson()},
        ),
      );
      rethrow;
    }
  }

  // Get glucose readings with optional filters
  static Future<GlucoseReadingResponse> getGlucoseReadings({
    required String token,
    DateTime? startDate,
    DateTime? endDate,
    MealTiming? mealTiming,
    int limit = 50,
    int page = 1,
    String sortBy = 'timestamp',
    String sortOrder = 'desc',
  }) async {
    return withRetry(
      operation: () async {
        final queryParams = <String, String>{
          'limit': limit.toString(),
          'page': page.toString(),
          'sortBy': sortBy,
          'sortOrder': sortOrder,
        };

        if (startDate != null) {
          queryParams['startDate'] = startDate.toIso8601String();
        }
        if (endDate != null) {
          queryParams['endDate'] = endDate.toIso8601String();
        }
        if (mealTiming != null) {
          queryParams['mealTiming'] = mealTiming.value;
        }

        final uri = Uri.parse(
          '${BackendService.baseUrl}/glucose',
        ).replace(queryParameters: queryParams);

        final response = await http.get(
          uri,
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer $token',
          },
        );

        debugPrint('Get glucose readings response: ${response.statusCode}');

        if (response.statusCode == 200) {
          final data = jsonDecode(response.body);
          return GlucoseReadingResponse.fromJson(data['data']);
        }

        final errorData = jsonDecode(response.body);
        throw Exception(
          errorData['message'] ?? 'Failed to get glucose readings',
        );
      },
      operationName: 'getGlucoseReadings',
      config: _defaultRetryConfig,
      shouldRetry: _shouldRetryRequest,
    );
  }

  // Get a single glucose reading
  static Future<GlucoseReading> getGlucoseReading({
    required String token,
    required String readingId,
  }) async {
    return withRetry(
      operation: () async {
        final response = await http.get(
          Uri.parse('${BackendService.baseUrl}/glucose/$readingId'),
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer $token',
          },
        );

        debugPrint('Get glucose reading response: ${response.statusCode}');

        if (response.statusCode == 200) {
          final data = jsonDecode(response.body);
          return GlucoseReading.fromJson(data['data']);
        }

        final errorData = jsonDecode(response.body);
        throw Exception(
          errorData['message'] ?? 'Failed to get glucose reading',
        );
      },
      operationName: 'getGlucoseReading',
      config: _defaultRetryConfig,
      shouldRetry: _shouldRetryRequest,
    );
  }

  // Update a glucose reading
  static Future<GlucoseReading> updateGlucoseReading({
    required String token,
    required String readingId,
    required GlucoseReading reading,
  }) async {
    try {
      return await withRetry(
        operation: () async {
          final response = await http.put(
            Uri.parse('${BackendService.baseUrl}/glucose/$readingId'),
            headers: {
              'Content-Type': 'application/json',
              'Authorization': 'Bearer $token',
            },
            body: jsonEncode(reading.toCreateJson()),
          );

          debugPrint('Update glucose reading response: ${response.statusCode}');

          if (response.statusCode == 200) {
            final data = jsonDecode(response.body);
            return GlucoseReading.fromJson(data['data']);
          }

          final errorData = jsonDecode(response.body);
          throw Exception(
            errorData['message'] ?? 'Failed to update glucose reading',
          );
        },
        operationName: 'updateGlucoseReading',
        config: _defaultRetryConfig,
        shouldRetry: _shouldRetryRequest,
      );
    } catch (e) {
      _updateRetryStatus(
        'Failed to update glucose reading. Will retry when online.',
      );

      // Queue for offline sync
      await _offlineQueue.add(
        QueuedOperation(
          type: 'updateGlucoseReading',
          data: {
            'token': token,
            'readingId': readingId,
            'reading': reading.toCreateJson(),
          },
        ),
      );
      rethrow;
    }
  }

  // Delete a glucose reading
  static Future<void> deleteGlucoseReading({
    required String token,
    required String readingId,
  }) async {
    try {
      return await withRetry(
        operation: () async {
          final response = await http.delete(
            Uri.parse('${BackendService.baseUrl}/glucose/$readingId'),
            headers: {
              'Content-Type': 'application/json',
              'Authorization': 'Bearer $token',
            },
          );

          debugPrint('Delete glucose reading response: ${response.statusCode}');

          if (response.statusCode == 200) {
            return;
          }

          final errorData = jsonDecode(response.body);
          throw Exception(
            errorData['message'] ?? 'Failed to delete glucose reading',
          );
        },
        operationName: 'deleteGlucoseReading',
        config: _defaultRetryConfig,
        shouldRetry: _shouldRetryRequest,
      );
    } catch (e) {
      _updateRetryStatus(
        'Failed to delete glucose reading. Will retry when online.',
      );

      // Queue for offline sync
      await _offlineQueue.add(
        QueuedOperation(
          type: 'deleteGlucoseReading',
          data: {'token': token, 'readingId': readingId},
        ),
      );
      rethrow;
    }
  }

  // Get glucose statistics
  static Future<GlucoseReadingStats> getGlucoseStats({
    required String token,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    return withRetry(
      operation: () async {
        final queryParams = <String, String>{};

        if (startDate != null) {
          queryParams['startDate'] = startDate.toIso8601String();
        }
        if (endDate != null) {
          queryParams['endDate'] = endDate.toIso8601String();
        }

        final uri = Uri.parse(
          '${BackendService.baseUrl}/glucose/stats',
        ).replace(queryParameters: queryParams.isNotEmpty ? queryParams : null);

        final response = await http.get(
          uri,
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer $token',
          },
        );

        debugPrint('Get glucose stats response: ${response.statusCode}');

        if (response.statusCode == 200) {
          final data = jsonDecode(response.body);
          return GlucoseReadingStats.fromJson(data['data']);
        }

        final errorData = jsonDecode(response.body);
        throw Exception(
          errorData['message'] ?? 'Failed to get glucose statistics',
        );
      },
      operationName: 'getGlucoseStats',
      config: _defaultRetryConfig,
      shouldRetry: _shouldRetryRequest,
    );
  }

  // Get recent readings (last 7 days)
  static Future<List<GlucoseReading>> getRecentReadings({
    required String token,
    int days = 7,
  }) async {
    final endDate = DateTime.now();
    final startDate = endDate.subtract(Duration(days: days));

    final response = await getGlucoseReadings(
      token: token,
      startDate: startDate,
      endDate: endDate,
      limit: 100,
      sortOrder: 'desc',
    );

    return response.readings;
  }

  // Export chart data
  static Future<ExportResponse> exportChart({
    required String token,
    DateTime? startDate,
    DateTime? endDate,
    String chartType = 'line',
    String format = 'json',
    String destination = 'download',
    String? email,
  }) async {
    return withRetry(
      operation: () async {
        final queryParams = <String, String>{
          'chartType': chartType,
          'format': format,
          'destination': destination,
        };

        if (startDate != null) {
          queryParams['startDate'] = startDate.toIso8601String();
        }
        if (endDate != null) {
          queryParams['endDate'] = endDate.toIso8601String();
        }
        if (email != null && email.isNotEmpty) {
          queryParams['email'] = email;
        }

        final uri = Uri.parse(
          '${BackendService.baseUrl}/glucose/export/chart',
        ).replace(queryParameters: queryParams);

        final response = await http.get(
          uri,
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer $token',
          },
        );

        debugPrint('Export chart response: ${response.statusCode}');

        if (response.statusCode == 200) {
          // For download destination, return the file data
          if (destination == 'download') {
            return ExportResponse(
              success: true,
              message: 'Export completed successfully',
              data: response.bodyBytes,
              fileName: _getFileNameFromHeaders(response.headers),
              mimeType:
                  response.headers['content-type'] ??
                  'application/octet-stream',
            );
          } else {
            // For other destinations, return the JSON response
            final data = jsonDecode(response.body);
            return ExportResponse(
              success: data['success'] ?? true,
              message: data['message'] ?? 'Export completed successfully',
              link: data['data']?['link'],
              fileName: data['data']?['fileName'],
            );
          }
        }

        final errorData = jsonDecode(response.body);
        throw Exception(errorData['message'] ?? 'Failed to export chart');
      },
      operationName: 'exportChart',
      config: _defaultRetryConfig,
      shouldRetry: _shouldRetryRequest,
    );
  }

  // Bulk export chart data
  static Future<BulkExportResponse> bulkExportChart({
    required String token,
    DateTime? startDate,
    DateTime? endDate,
    String chartType = 'line',
    List<String> formats = const ['json'],
    List<String> destinations = const ['download'],
    String? email,
    bool includeAllFormats = false,
  }) async {
    return withRetry(
      operation: () async {
        final requestBody = {
          'chartType': chartType,
          'formats': formats,
          'destinations': destinations,
          'includeAllFormats': includeAllFormats,
        };

        if (startDate != null) {
          requestBody['startDate'] = startDate.toIso8601String();
        }
        if (endDate != null) {
          requestBody['endDate'] = endDate.toIso8601String();
        }
        if (email != null && email.isNotEmpty) {
          requestBody['email'] = email;
        }

        final response = await http.post(
          Uri.parse('${BackendService.baseUrl}/glucose/export/bulk'),
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer $token',
          },
          body: jsonEncode(requestBody),
        );

        debugPrint('Bulk export response: ${response.statusCode}');

        if (response.statusCode == 200) {
          final data = jsonDecode(response.body);
          return BulkExportResponse.fromJson(data['data']);
        }

        final errorData = jsonDecode(response.body);
        throw Exception(errorData['message'] ?? 'Failed to bulk export chart');
      },
      operationName: 'bulkExportChart',
      config: _defaultRetryConfig,
      shouldRetry: _shouldRetryRequest,
    );
  }

  static String? _getFileNameFromHeaders(Map<String, String> headers) {
    final contentDisposition = headers['content-disposition'];
    if (contentDisposition != null) {
      final match = RegExp(r'filename=([^;]+)').firstMatch(contentDisposition);
      return match?.group(1)?.replaceAll('"', '');
    }
    return null;
  }
}

// Export response models
class ExportResponse {
  final bool success;
  final String message;
  final List<int>? data; // For download destination
  final String? fileName;
  final String? mimeType;
  final String? link; // For cloud destinations

  ExportResponse({
    required this.success,
    required this.message,
    this.data,
    this.fileName,
    this.mimeType,
    this.link,
  });
}

class BulkExportResponse {
  final int totalExports;
  final int successful;
  final int failed;
  final List<ExportResult> results;

  BulkExportResponse({
    required this.totalExports,
    required this.successful,
    required this.failed,
    required this.results,
  });

  factory BulkExportResponse.fromJson(Map<String, dynamic> json) {
    return BulkExportResponse(
      totalExports: json['totalExports'] ?? 0,
      successful: json['successful'] ?? 0,
      failed: json['failed'] ?? 0,
      results:
          (json['results'] as List<dynamic>?)
              ?.map((e) => ExportResult.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
    );
  }
}

class ExportResult {
  final String format;
  final String destination;
  final bool success;
  final String? message;
  final String? fileName;
  final String? link;

  ExportResult({
    required this.format,
    required this.destination,
    required this.success,
    this.message,
    this.fileName,
    this.link,
  });

  factory ExportResult.fromJson(Map<String, dynamic> json) {
    return ExportResult(
      format: json['format'] ?? '',
      destination: json['destination'] ?? '',
      success: json['success'] ?? false,
      message: json['message'],
      fileName: json['fileName'],
      link: json['link'],
    );
  }
}
