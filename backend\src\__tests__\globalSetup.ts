/**
 * Global setup for Jest tests
 * Runs once before all test suites
 */

import { MongoMemoryServer } from 'mongodb-memory-server';

let mongod: MongoMemoryServer;

export default async function globalSetup() {
  // Start in-memory MongoDB instance for testing
  mongod = await MongoMemoryServer.create();
  const uri = mongod.getUri();
  
  // Set the MongoDB URI for tests
  process.env.MONGODB_URI = uri;
  process.env.NODE_ENV = 'test';
  
  // Set other test environment variables
  process.env.JWT_SECRET = 'test-jwt-secret-key-for-testing-purposes-only';
  process.env.JWT_EXPIRE = '1h';
  
  console.log('Global test setup completed');
  console.log(`Test MongoDB URI: ${uri}`);
  
  // Store the mongod instance globally so it can be accessed in teardown
  (global as any).__MONGOD__ = mongod;
};
