import FoodDatabase, { IFoodDatabaseEntry } from '../models/FoodDatabase';
import { FoodCategory, GlycemicIndex, MealType } from '../models/FoodEntry';

export class FoodDatabaseService {
    
    /**
     * Search foods in the database with text search
     */
    static async searchFoods(
        query: string, 
        category?: FoodCategory, 
        limit: number = 50
    ): Promise<IFoodDatabaseEntry[]> {
        try {
            const searchCriteria: any = {
                isVerified: true,
                $text: { $search: query }
            };
            
            if (category) {
                searchCriteria.category = category;
            }
            
            return await FoodDatabase.find(searchCriteria)
                .select('name brand category caloriesPer100g carbohydratesPer100g proteinPer100g fatPer100g fiberPer100g glycemicIndex commonPortions isTraditionalSA localNames')
                .limit(limit)
                .sort({ score: { $meta: 'textScore' } })
                .lean();
        } catch (error) {
            console.error('Error searching foods:', error);
            throw new Error('Failed to search foods');
        }
    }

    /**
     * Get food suggestions based on meal type and user preferences
     */
    static async getFoodSuggestions(
        mealType: MealType,
        isDiabetic: boolean = true,
        limit: number = 20
    ): Promise<IFoodDatabaseEntry[]> {
        try {
            let query: any = { isVerified: true };

            // Meal-specific suggestions
            switch (mealType) {
                case MealType.BREAKFAST:
                    query.category = { 
                        $in: [FoodCategory.GRAINS_STARCHES, FoodCategory.FRUITS, FoodCategory.DAIRY] 
                    };
                    break;
                case MealType.LUNCH:
                case MealType.DINNER:
                    query.category = { 
                        $in: [FoodCategory.PROTEINS, FoodCategory.VEGETABLES, FoodCategory.GRAINS_STARCHES] 
                    };
                    break;
                case MealType.MORNING_SNACK:
                case MealType.AFTERNOON_SNACK:
                case MealType.EVENING_SNACK:
                    query.category = { 
                        $in: [FoodCategory.FRUITS, FoodCategory.VEGETABLES, FoodCategory.SNACKS_SWEETS] 
                    };
                    if (isDiabetic) {
                        query.carbohydratesPer100g = { $lte: 15 };
                    }
                    break;
            }

            // Diabetes-friendly filters
            if (isDiabetic) {
                query.glycemicIndex = { $in: [GlycemicIndex.LOW, GlycemicIndex.MEDIUM] };
            }

            return await FoodDatabase.find(query)
                .select('name brand category caloriesPer100g carbohydratesPer100g proteinPer100g fatPer100g fiberPer100g glycemicIndex commonPortions')
                .limit(limit)
                .sort({ carbohydratesPer100g: 1 })
                .lean();
        } catch (error) {
            console.error('Error getting food suggestions:', error);
            throw new Error('Failed to get food suggestions');
        }
    }

    /**
     * Get foods by category
     */
    static async getFoodsByCategory(
        category: FoodCategory, 
        limit: number = 100
    ): Promise<IFoodDatabaseEntry[]> {
        try {
            return await FoodDatabase.find({ category, isVerified: true })
                .select('name brand caloriesPer100g carbohydratesPer100g proteinPer100g fatPer100g fiberPer100g glycemicIndex commonPortions')
                .limit(limit)
                .sort({ name: 1 })
                .lean();
        } catch (error) {
            console.error('Error getting foods by category:', error);
            throw new Error('Failed to get foods by category');
        }
    }

    /**
     * Get diabetes-friendly foods
     */
    static async getDiabetesFriendlyFoods(limit: number = 50): Promise<IFoodDatabaseEntry[]> {
        try {
            return await FoodDatabase.find({
                isVerified: true,
                carbohydratesPer100g: { $lte: 15 },
                glycemicIndex: GlycemicIndex.LOW
            })
                .select('name brand category caloriesPer100g carbohydratesPer100g proteinPer100g fatPer100g fiberPer100g glycemicIndex commonPortions')
                .limit(limit)
                .sort({ carbohydratesPer100g: 1 })
                .lean();
        } catch (error) {
            console.error('Error getting diabetes-friendly foods:', error);
            throw new Error('Failed to get diabetes-friendly foods');
        }
    }

    /**
     * Get traditional South African foods
     */
    static async getTraditionalSAFoods(limit: number = 100): Promise<IFoodDatabaseEntry[]> {
        try {
            return await FoodDatabase.find({ isTraditionalSA: true, isVerified: true })
                .select('name localNames category caloriesPer100g carbohydratesPer100g proteinPer100g fatPer100g fiberPer100g glycemicIndex commonPortions region')
                .limit(limit)
                .sort({ name: 1 })
                .lean();
        } catch (error) {
            console.error('Error getting traditional SA foods:', error);
            throw new Error('Failed to get traditional SA foods');
        }
    }

    /**
     * Get popular foods (most commonly used)
     */
    static async getPopularFoods(limit: number = 30): Promise<IFoodDatabaseEntry[]> {
        try {
            // This would typically be based on usage statistics
            // For now, return a mix of common foods
            return await FoodDatabase.find({ 
                isVerified: true,
                category: { 
                    $in: [
                        FoodCategory.GRAINS_STARCHES, 
                        FoodCategory.PROTEINS, 
                        FoodCategory.VEGETABLES,
                        FoodCategory.FRUITS
                    ] 
                }
            })
                .select('name brand category caloriesPer100g carbohydratesPer100g proteinPer100g fatPer100g fiberPer100g glycemicIndex commonPortions')
                .limit(limit)
                .sort({ name: 1 })
                .lean();
        } catch (error) {
            console.error('Error getting popular foods:', error);
            throw new Error('Failed to get popular foods');
        }
    }

    /**
     * Add a custom food to the database
     */
    static async addCustomFood(foodData: Partial<IFoodDatabaseEntry>): Promise<IFoodDatabaseEntry> {
        try {
            const customFood = new FoodDatabase({
                ...foodData,
                isVerified: false, // Custom foods need verification
                source: 'user_custom'
            });

            return await customFood.save();
        } catch (error) {
            console.error('Error adding custom food:', error);
            throw new Error('Failed to add custom food');
        }
    }

    /**
     * Get food by ID
     */
    static async getFoodById(id: string): Promise<IFoodDatabaseEntry | null> {
        try {
            return await FoodDatabase.findById(id).lean();
        } catch (error) {
            console.error('Error getting food by ID:', error);
            throw new Error('Failed to get food by ID');
        }
    }

    /**
     * Get foods with similar nutritional profile
     */
    static async getSimilarFoods(
        targetCarbs: number, 
        targetCalories: number, 
        category?: FoodCategory,
        limit: number = 10
    ): Promise<IFoodDatabaseEntry[]> {
        try {
            const carbsRange = targetCarbs * 0.2; // 20% tolerance
            const caloriesRange = targetCalories * 0.2; // 20% tolerance

            const query: any = {
                isVerified: true,
                carbohydratesPer100g: { 
                    $gte: targetCarbs - carbsRange, 
                    $lte: targetCarbs + carbsRange 
                },
                caloriesPer100g: { 
                    $gte: targetCalories - caloriesRange, 
                    $lte: targetCalories + caloriesRange 
                }
            };

            if (category) {
                query.category = category;
            }

            return await FoodDatabase.find(query)
                .select('name brand category caloriesPer100g carbohydratesPer100g proteinPer100g fatPer100g fiberPer100g glycemicIndex commonPortions')
                .limit(limit)
                .sort({ carbohydratesPer100g: 1 })
                .lean();
        } catch (error) {
            console.error('Error getting similar foods:', error);
            throw new Error('Failed to get similar foods');
        }
    }

    /**
     * Get food categories with counts
     */
    static async getFoodCategoriesWithCounts(): Promise<Array<{category: FoodCategory, count: number}>> {
        try {
            const result = await FoodDatabase.aggregate([
                { $match: { isVerified: true } },
                { $group: { _id: '$category', count: { $sum: 1 } } },
                { $sort: { count: -1 } }
            ]);

            return result.map(item => ({
                category: item._id as FoodCategory,
                count: item.count
            }));
        } catch (error) {
            console.error('Error getting food categories with counts:', error);
            throw new Error('Failed to get food categories with counts');
        }
    }

    /**
     * Validate food data before saving
     */
    static validateFoodData(foodData: Partial<IFoodDatabaseEntry>): { isValid: boolean; errors: string[] } {
        const errors: string[] = [];

        if (!foodData.name || foodData.name.trim().length === 0) {
            errors.push('Food name is required');
        }

        if (!foodData.category || !Object.values(FoodCategory).includes(foodData.category)) {
            errors.push('Valid food category is required');
        }

        if (!foodData.glycemicIndex || !Object.values(GlycemicIndex).includes(foodData.glycemicIndex)) {
            errors.push('Valid glycemic index is required');
        }

        if (foodData.caloriesPer100g === undefined || foodData.caloriesPer100g < 0) {
            errors.push('Valid calories per 100g is required');
        }

        if (foodData.carbohydratesPer100g === undefined || foodData.carbohydratesPer100g < 0) {
            errors.push('Valid carbohydrates per 100g is required');
        }

        if (foodData.proteinPer100g === undefined || foodData.proteinPer100g < 0) {
            errors.push('Valid protein per 100g is required');
        }

        if (foodData.fatPer100g === undefined || foodData.fatPer100g < 0) {
            errors.push('Valid fat per 100g is required');
        }

        if (foodData.fiberPer100g === undefined || foodData.fiberPer100g < 0) {
            errors.push('Valid fiber per 100g is required');
        }

        return {
            isValid: errors.length === 0,
            errors
        };
    }
}
