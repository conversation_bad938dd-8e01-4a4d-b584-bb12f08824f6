@echo off
REM GlucoMonitor Docker Management Script for Windows
REM This script provides convenient commands for managing the GlucoMonitor Docker environment

setlocal enabledelayedexpansion

REM Function to print colored output (simplified for Windows)
set "INFO=[INFO]"
set "SUCCESS=[SUCCESS]"
set "WARNING=[WARNING]"
set "ERROR=[ERROR]"

REM Function to check if Docker is running
:check_docker
docker info >nul 2>&1
if errorlevel 1 (
    echo %ERROR% Docker is not running. Please start Docker and try again.
    exit /b 1
)
goto :eof

REM Function to check if environment files exist
:check_env_files
if not exist ".env.docker" (
    echo %WARNING% .env.docker not found. Creating from template...
    if exist ".env.docker.example" (
        copy ".env.docker.example" ".env.docker" >nul
        echo %WARNING% Please edit .env.docker with your configuration before proceeding.
        exit /b 1
    ) else (
        echo %ERROR% .env.docker.example not found. Please create environment configuration.
        exit /b 1
    )
)
goto :eof

REM Function to show usage
:show_usage
echo GlucoMonitor Docker Management Script
echo.
echo Usage: %0 [COMMAND]
echo.
echo Commands:
echo   dev         Start development environment
echo   prod        Start production environment
echo   test        Run tests
echo   stop        Stop all services
echo   restart     Restart all services
echo   logs        Show logs for all services
echo   logs-dev    Show logs for development services
echo   logs-prod   Show logs for production services
echo   health      Check health of all services
echo   clean       Clean up Docker resources
echo   rebuild     Rebuild all images without cache
echo   shell       Access backend container shell
echo   redis       Access Redis CLI
echo   status      Show status of all services
echo   setup       Initial setup (copy env files)
echo   help        Show this help message
echo.
echo Examples:
echo   %0 dev              # Start development environment
echo   %0 prod             # Start production environment
echo   %0 logs-dev         # View development logs
echo   %0 health           # Check service health
echo   %0 clean            # Clean up Docker resources
goto :eof

REM Function to start development environment
:start_dev
echo %INFO% Starting development environment...
call :check_docker
if errorlevel 1 exit /b 1
call :check_env_files
if errorlevel 1 exit /b 1
docker-compose --profile dev up --build -d
echo %SUCCESS% Development environment started!
echo %INFO% Backend available at: http://localhost:5000
echo %INFO% Debug port available at: localhost:9229
echo %INFO% Use '%0 logs-dev' to view logs
goto :eof

REM Function to start production environment
:start_prod
echo %INFO% Starting production environment...
call :check_docker
if errorlevel 1 exit /b 1
call :check_env_files
if errorlevel 1 exit /b 1
docker-compose --profile prod up --build -d
echo %SUCCESS% Production environment started!
echo %INFO% Backend available at: http://localhost:5000
echo %INFO% Use '%0 logs-prod' to view logs
goto :eof

REM Function to run tests
:run_tests
echo %INFO% Running tests...
call :check_docker
if errorlevel 1 exit /b 1
if not exist ".env.test" (
    echo %WARNING% .env.test not found. Creating from template...
    if exist ".env.test.example" (
        copy ".env.test.example" ".env.test" >nul
    )
)
docker-compose --profile test up --build --abort-on-container-exit
echo %SUCCESS% Tests completed!
goto :eof

REM Function to stop all services
:stop_services
echo %INFO% Stopping all services...
docker-compose down
echo %SUCCESS% All services stopped!
goto :eof

REM Function to restart services
:restart_services
echo %INFO% Restarting services...
docker-compose restart
echo %SUCCESS% Services restarted!
goto :eof

REM Function to show logs
:show_logs
echo %INFO% Showing logs for all services...
docker-compose logs -f
goto :eof

REM Function to show development logs
:show_logs_dev
echo %INFO% Showing logs for development services...
docker-compose logs -f backend-dev redis
goto :eof

REM Function to show production logs
:show_logs_prod
echo %INFO% Showing logs for production services...
docker-compose logs -f backend-prod redis
goto :eof

REM Function to check health
:check_health
echo %INFO% Checking service health...

REM Check if services are running
docker-compose ps | findstr "Up" >nul
if errorlevel 1 (
    echo %ERROR% No services are currently running. Start services first.
    goto :eof
)

REM Check backend health
echo %INFO% Checking backend health...
curl -f -s http://localhost:5000/api/health/live >nul 2>&1
if errorlevel 1 (
    echo %ERROR% Backend health check failed
) else (
    echo %SUCCESS% Backend is healthy
)

REM Check Redis health
echo %INFO% Checking Redis health...
docker-compose exec -T redis redis-cli ping | findstr "PONG" >nul
if errorlevel 1 (
    echo %ERROR% Redis health check failed
) else (
    echo %SUCCESS% Redis is healthy
)

REM Show detailed health
echo %INFO% Detailed health information:
curl -s http://localhost:5000/api/health
goto :eof

REM Function to clean up Docker resources
:clean_docker
echo %WARNING% This will remove all stopped containers, unused networks, and dangling images.
set /p "confirm=Are you sure? (y/N): "
if /i "!confirm!"=="y" (
    echo %INFO% Cleaning up Docker resources...
    docker-compose down -v
    docker system prune -f
    docker volume prune -f
    echo %SUCCESS% Docker cleanup completed!
) else (
    echo %INFO% Cleanup cancelled.
)
goto :eof

REM Function to rebuild images
:rebuild_images
echo %INFO% Rebuilding all images without cache...
docker-compose build --no-cache --pull
echo %SUCCESS% Images rebuilt successfully!
goto :eof

REM Function to access backend shell
:access_shell
echo %INFO% Accessing backend container shell...
docker-compose ps | findstr "backend-dev.*Up" >nul
if not errorlevel 1 (
    docker-compose exec backend-dev sh
    goto :eof
)
docker-compose ps | findstr "backend-prod.*Up" >nul
if not errorlevel 1 (
    docker-compose exec backend-prod sh
    goto :eof
)
echo %ERROR% No backend container is running. Start services first.
goto :eof

REM Function to access Redis CLI
:access_redis
echo %INFO% Accessing Redis CLI...
docker-compose ps | findstr "redis.*Up" >nul
if not errorlevel 1 (
    docker-compose exec redis redis-cli
) else (
    echo %ERROR% Redis container is not running. Start services first.
)
goto :eof

REM Function to show service status
:show_status
echo %INFO% Service status:
docker-compose ps
echo.
echo %INFO% Resource usage:
docker stats --no-stream
goto :eof

REM Function to setup environment
:setup_env
echo %INFO% Setting up environment files...

if not exist ".env.docker" (
    if exist ".env.docker.example" (
        copy ".env.docker.example" ".env.docker" >nul
        echo %SUCCESS% Created .env.docker from template
    ) else (
        echo %ERROR% .env.docker.example not found
    )
) else (
    echo %WARNING% .env.docker already exists
)

if not exist ".env.test" (
    if exist ".env.test.example" (
        copy ".env.test.example" ".env.test" >nul
        echo %SUCCESS% Created .env.test from template
    ) else (
        echo %ERROR% .env.test.example not found
    )
) else (
    echo %WARNING% .env.test already exists
)

echo %WARNING% Please edit the environment files with your configuration:
echo %INFO%   - .env.docker (for production/development)
echo %INFO%   - .env.test (for testing)
goto :eof

REM Main script logic
set "command=%1"
if "%command%"=="" set "command=help"

if "%command%"=="dev" (
    call :start_dev
) else if "%command%"=="prod" (
    call :start_prod
) else if "%command%"=="test" (
    call :run_tests
) else if "%command%"=="stop" (
    call :stop_services
) else if "%command%"=="restart" (
    call :restart_services
) else if "%command%"=="logs" (
    call :show_logs
) else if "%command%"=="logs-dev" (
    call :show_logs_dev
) else if "%command%"=="logs-prod" (
    call :show_logs_prod
) else if "%command%"=="health" (
    call :check_health
) else if "%command%"=="clean" (
    call :clean_docker
) else if "%command%"=="rebuild" (
    call :rebuild_images
) else if "%command%"=="shell" (
    call :access_shell
) else if "%command%"=="redis" (
    call :access_redis
) else if "%command%"=="status" (
    call :show_status
) else if "%command%"=="setup" (
    call :setup_env
) else (
    call :show_usage
)

endlocal
