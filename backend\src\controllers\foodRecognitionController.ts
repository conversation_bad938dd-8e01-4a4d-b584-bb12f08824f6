import { Response } from 'express';
import { AuthRequest } from '../middleware/auth';
import FoodRecognitionHistory, { 
    IFoodRecognitionHistory, 
    RecognitionMethod, 
    RecognitionStatus, 
    ConfidenceLevel 
} from '../models/FoodRecognitionHistory';
import { FoodCategory } from '../models/FoodEntry';
import { catchAsync } from '../utils/errorHandler';
import logger from '../utils/logger';

/**
 * Record a food recognition attempt
 */
export const recordRecognitionAttempt = catchAsync(async (req: AuthRequest, res: Response): Promise<void> => {
    const userId = req.user!._id;
    const {
        recognitionAttempt,
        originalQuery,
        imageMetadata,
        recognizedFood,
        finalResult,
        status,
        userCorrection,
        mealContext,
        learningData,
        deviceInfo
    } = req.body;

    // Validate required fields
    if (!recognitionAttempt || !finalResult || !status || !mealContext || !learningData) {
        res.status(400).json({
            success: false,
            message: 'Missing required fields'
        });
        return;
    }

    // Determine confidence level based on confidence score
    const getConfidenceLevel = (confidence: number): ConfidenceLevel => {
        if (confidence <= 20) return ConfidenceLevel.VERY_LOW;
        if (confidence <= 40) return ConfidenceLevel.LOW;
        if (confidence <= 60) return ConfidenceLevel.MEDIUM;
        if (confidence <= 80) return ConfidenceLevel.HIGH;
        return ConfidenceLevel.VERY_HIGH;
    };

    // Create recognition history entry
    const recognitionHistory = new FoodRecognitionHistory({
        userId,
        recognitionAttempt: {
            ...recognitionAttempt,
            confidenceLevel: getConfidenceLevel(recognitionAttempt.confidence)
        },
        originalQuery,
        imageMetadata,
        recognizedFood,
        finalResult,
        status,
        userCorrection,
        mealContext,
        learningData,
        deviceInfo
    });

    await recognitionHistory.save();

    logger.info('Food recognition attempt recorded', {
        userId: userId.toString(),
        method: recognitionAttempt.method,
        status,
        confidence: recognitionAttempt.confidence,
        foodName: finalResult.foodName
    });

    res.status(201).json({
        success: true,
        message: 'Recognition attempt recorded successfully',
        data: {
            id: recognitionHistory._id,
            status,
            confidence: recognitionAttempt.confidence
        }
    });
});

/**
 * Get user's recognition history
 */
export const getRecognitionHistory = catchAsync(async (req: AuthRequest, res: Response): Promise<void> => {
    const userId = req.user!._id;
    const {
        page = 1,
        limit = 20,
        method,
        status,
        startDate,
        endDate,
        mealType
    } = req.query;

    // Build filter criteria
    const filter: any = { userId };

    if (method) {
        filter['recognitionAttempt.method'] = method;
    }

    if (status) {
        filter.status = status;
    }

    if (startDate || endDate) {
        filter.createdAt = {};
        if (startDate) {
            filter.createdAt.$gte = new Date(startDate as string);
        }
        if (endDate) {
            filter.createdAt.$lte = new Date(endDate as string);
        }
    }

    if (mealType) {
        filter['mealContext.mealType'] = mealType;
    }

    // Calculate pagination
    const skip = (Number(page) - 1) * Number(limit);

    // Get recognition history
    const [history, total] = await Promise.all([
        FoodRecognitionHistory.find(filter)
            .sort({ createdAt: -1 })
            .skip(skip)
            .limit(Number(limit))
            .select('-deviceInfo -imageMetadata.size'), // Exclude sensitive/large data
        FoodRecognitionHistory.countDocuments(filter)
    ]);

    res.json({
        success: true,
        data: {
            history,
            pagination: {
                currentPage: Number(page),
                totalPages: Math.ceil(total / Number(limit)),
                totalItems: total,
                itemsPerPage: Number(limit)
            }
        }
    });
});

/**
 * Get recognition analytics for user
 */
export const getRecognitionAnalytics = catchAsync(async (req: AuthRequest, res: Response): Promise<void> => {
    const userId = req.user!._id;
    const { timeframe = 30 } = req.query; // days

    const startDate = new Date();
    startDate.setDate(startDate.getDate() - Number(timeframe));

    // Get analytics using the static method
    const analytics = await (FoodRecognitionHistory as any).getUserAnalytics(userId);

    // Get additional statistics
    const [
        totalAttempts,
        methodBreakdown,
        recentTrends,
        accuracyByCategory
    ] = await Promise.all([
        FoodRecognitionHistory.countDocuments({
            userId,
            createdAt: { $gte: startDate }
        }),
        getMethodBreakdown(userId, startDate),
        getRecentTrends(userId, startDate),
        getAccuracyByCategory(userId, startDate)
    ]);

    res.json({
        success: true,
        data: {
            ...analytics,
            timeframe: Number(timeframe),
            totalAttemptsInTimeframe: totalAttempts,
            methodBreakdown,
            recentTrends,
            accuracyByCategory
        }
    });
});

/**
 * Update recognition entry with user feedback
 */
export const updateRecognitionFeedback = catchAsync(async (req: AuthRequest, res: Response): Promise<void> => {
    const userId = req.user!._id;
    const { recognitionId } = req.params;
    const { userCorrection, learningData } = req.body;

    // Find the recognition entry
    const recognition = await FoodRecognitionHistory.findOne({
        _id: recognitionId,
        userId
    });

    if (!recognition) {
        res.status(404).json({
            success: false,
            message: 'Recognition entry not found'
        });
        return;
    }

    // Update with user feedback
    if (userCorrection) {
        recognition.userCorrection = {
            ...userCorrection,
            timestamp: new Date()
        };
        recognition.status = RecognitionStatus.USER_CORRECTED;
    }

    if (learningData) {
        recognition.learningData = {
            ...recognition.learningData,
            ...learningData
        };
    }

    await recognition.save();

    logger.info('Recognition feedback updated', {
        userId: userId.toString(),
        recognitionId,
        hasCorrectionData: !!userCorrection,
        hasLearningData: !!learningData
    });

    res.json({
        success: true,
        message: 'Feedback updated successfully',
        data: {
            id: recognition._id,
            status: recognition.status
        }
    });
});

/**
 * Get recognition accuracy trends
 */
export const getAccuracyTrends = catchAsync(async (req: AuthRequest, res: Response): Promise<void> => {
    const userId = req.user!._id;
    const { period = 'week' } = req.query; // week, month, quarter

    const trends = await calculateAccuracyTrends(userId, period as string);

    res.json({
        success: true,
        data: {
            period,
            trends
        }
    });
});

/**
 * Get most problematic foods for recognition
 */
export const getProblematicFoods = catchAsync(async (req: AuthRequest, res: Response): Promise<void> => {
    const userId = req.user!._id;
    const { limit = 10 } = req.query;

    const problematicFoods = await FoodRecognitionHistory.aggregate([
        {
            $match: {
                userId,
                status: { $in: [RecognitionStatus.FAILED, RecognitionStatus.USER_CORRECTED] }
            }
        },
        {
            $group: {
                _id: '$finalResult.foodName',
                failureCount: { $sum: 1 },
                averageConfidence: { $avg: '$recognitionAttempt.confidence' },
                commonIssues: { $push: '$userCorrection.correctionType' },
                category: { $first: '$finalResult.category' }
            }
        },
        {
            $sort: { failureCount: -1 }
        },
        {
            $limit: Number(limit)
        }
    ]);

    res.json({
        success: true,
        data: {
            problematicFoods: problematicFoods.map(food => ({
                foodName: food._id,
                category: food.category,
                failureCount: food.failureCount,
                averageConfidence: Math.round(food.averageConfidence * 100) / 100,
                commonIssues: [...new Set(food.commonIssues.filter(Boolean))]
            }))
        }
    });
});

/**
 * Get recognition success rate by method
 */
export const getSuccessRateByMethod = catchAsync(async (req: AuthRequest, res: Response): Promise<void> => {
    const userId = req.user!._id;

    const methodStats = await FoodRecognitionHistory.aggregate([
        { $match: { userId } },
        {
            $group: {
                _id: '$recognitionAttempt.method',
                totalAttempts: { $sum: 1 },
                successfulAttempts: {
                    $sum: {
                        $cond: [
                            { $in: ['$status', [RecognitionStatus.SUCCESS, RecognitionStatus.PARTIALLY_CORRECT]] },
                            1,
                            0
                        ]
                    }
                },
                averageConfidence: { $avg: '$recognitionAttempt.confidence' },
                averageProcessingTime: { $avg: '$recognitionAttempt.processingTime' }
            }
        },
        {
            $project: {
                method: '$_id',
                totalAttempts: 1,
                successfulAttempts: 1,
                successRate: {
                    $multiply: [
                        { $divide: ['$successfulAttempts', '$totalAttempts'] },
                        100
                    ]
                },
                averageConfidence: { $round: ['$averageConfidence', 2] },
                averageProcessingTime: { $round: ['$averageProcessingTime', 0] }
            }
        },
        { $sort: { successRate: -1 } }
    ]);

    res.json({
        success: true,
        data: {
            methodStats
        }
    });
});

/**
 * Delete recognition history entries
 */
export const deleteRecognitionHistory = catchAsync(async (req: AuthRequest, res: Response): Promise<void> => {
    const userId = req.user!._id;
    const { recognitionIds } = req.body;

    if (!recognitionIds || !Array.isArray(recognitionIds)) {
        res.status(400).json({
            success: false,
            message: 'Recognition IDs array is required'
        });
        return;
    }

    const result = await FoodRecognitionHistory.deleteMany({
        _id: { $in: recognitionIds },
        userId
    });

    logger.info('Recognition history entries deleted', {
        userId: userId.toString(),
        deletedCount: result.deletedCount,
        requestedCount: recognitionIds.length
    });

    res.json({
        success: true,
        message: `${result.deletedCount} recognition entries deleted successfully`,
        data: {
            deletedCount: result.deletedCount
        }
    });
});

// Helper methods
async function getMethodBreakdown(userId: any, startDate: Date) {
    return await FoodRecognitionHistory.aggregate([
        {
            $match: {
                userId,
                createdAt: { $gte: startDate }
            }
        },
        {
            $group: {
                _id: '$recognitionAttempt.method',
                count: { $sum: 1 },
                averageConfidence: { $avg: '$recognitionAttempt.confidence' }
            }
        },
        {
            $sort: { count: -1 }
        }
    ]);
}

async function getRecentTrends(userId: any, startDate: Date) {
    return await FoodRecognitionHistory.aggregate([
        {
            $match: {
                userId,
                createdAt: { $gte: startDate }
            }
        },
        {
            $group: {
                _id: {
                    $dateToString: {
                        format: '%Y-%m-%d',
                        date: '$createdAt'
                    }
                },
                attempts: { $sum: 1 },
                successes: {
                    $sum: {
                        $cond: [
                            { $eq: ['$status', RecognitionStatus.SUCCESS] },
                            1,
                            0
                        ]
                    }
                },
                averageConfidence: { $avg: '$recognitionAttempt.confidence' }
            }
        },
        {
            $sort: { _id: 1 }
        }
    ]);
}

async function getAccuracyByCategory(userId: any, startDate: Date) {
    return await FoodRecognitionHistory.aggregate([
        {
            $match: {
                userId,
                createdAt: { $gte: startDate }
            }
        },
        {
            $group: {
                _id: '$finalResult.category',
                totalAttempts: { $sum: 1 },
                successfulAttempts: {
                    $sum: {
                        $cond: [
                            { $in: ['$status', [RecognitionStatus.SUCCESS, RecognitionStatus.PARTIALLY_CORRECT]] },
                            1,
                            0
                        ]
                    }
                }
            }
        },
        {
            $project: {
                category: '$_id',
                totalAttempts: 1,
                successRate: {
                    $multiply: [
                        { $divide: ['$successfulAttempts', '$totalAttempts'] },
                        100
                    ]
                }
            }
        },
        {
            $sort: { successRate: -1 }
        }
    ]);
}

async function calculateAccuracyTrends(userId: any, period: string) {
    // Implementation would calculate trends over time
    // This is a simplified version
    return {
        labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
        accuracyData: [65, 72, 78, 82],
        confidenceData: [60, 68, 75, 80]
    };
}
