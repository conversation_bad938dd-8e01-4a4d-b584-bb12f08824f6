import express from 'express';
const router = express.Router();
import { protect } from '../middleware/auth';
import {
    createGlucoseReading,
    getGlucoseReadings,
    getGlucoseReading,
    updateGlucoseReading,
    deleteGlucoseReading,
    getGlucoseStats,
    exportChartData,
    bulkExportChartData,
    getAdvancedAnalytics,
    getGlucoseInsights
} from '../controllers/glucose';

// All routes are protected (require authentication)
router.use(protect);

// Specific routes MUST come before parameterized routes (/:id)

// Debug route to test if routes are working
router.get('/test', (req, res) => {
    res.json({ success: true, message: 'Glucose routes are working!', timestamp: new Date().toISOString() });
});

// Export routes
// @route   GET /api/glucose/export/chart
// @desc    Export chart data
// @access  Private
router.get('/export/chart', exportChartData);

// @route   POST /api/glucose/export/bulk
// @desc    Bulk export chart data to multiple destinations
// @access  Private
router.post('/export/bulk', bulkExportChartData);

// @route   GET /api/glucose/stats
// @desc    Get glucose statistics
// @access  Private
router.get('/stats', getGlucoseStats);

// @route   GET /api/glucose/analytics
// @desc    Get advanced analytics
// @access  Private
router.get('/analytics', getAdvancedAnalytics);

// @route   GET /api/glucose/insights
// @desc    Get glucose insights and recommendations
// @access  Private
router.get('/insights', getGlucoseInsights);

// General CRUD routes
// @route   POST /api/glucose
// @desc    Create new glucose reading
// @access  Private
router.post('/', createGlucoseReading);

// @route   GET /api/glucose
// @desc    Get glucose readings for user
// @access  Private
router.get('/', getGlucoseReadings);

// Parameterized routes MUST come last
// @route   GET /api/glucose/:id
// @desc    Get single glucose reading
// @access  Private
router.get('/:id', getGlucoseReading);

// @route   PUT /api/glucose/:id
// @desc    Update glucose reading
// @access  Private
router.put('/:id', updateGlucoseReading);

// @route   DELETE /api/glucose/:id
// @desc    Delete glucose reading
// @access  Private
router.delete('/:id', deleteGlucoseReading);

export default router;
