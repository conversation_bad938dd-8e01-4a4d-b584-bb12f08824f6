import mongoose from 'mongoose';
import FoodDatabase from '../models/FoodDatabase';
import { FoodCategory, GlycemicIndex } from '../models/FoodEntry';
import { env } from '../config/env';

// South African food database
const southAfricanFoods = [
    // Traditional South African Foods
    {
        name: 'Pap (Maize Porridge)',
        category: FoodCategory.GRAINS_STARCHES,
        caloriesPer100g: 112,
        carbohydratesPer100g: 23,
        proteinPer100g: 2.5,
        fatPer100g: 0.5,
        fiberPer100g: 1.2,
        glycemicIndex: GlycemicIndex.HIGH,
        isTraditionalSA: true,
        localNames: ['Pap', 'Mieliepap', 'Stywe pap'],
        region: 'Nationwide',
        description: 'Traditional South African maize porridge, a staple food',
        diabeticNotes: 'High GI - consume in small portions with protein',
        commonPortions: [
            { description: '1 cup cooked', weight: 240, unit: 'g', isCommon: true },
            { description: '1/2 cup cooked', weight: 120, unit: 'g', isCommon: true }
        ],
        isVerified: true,
        source: 'SA Food Composition Tables'
    },
    {
        name: 'Boerewors',
        category: FoodCategory.PROTEINS,
        caloriesPer100g: 285,
        carbohydratesPer100g: 2,
        proteinPer100g: 16,
        fatPer100g: 24,
        fiberPer100g: 0,
        glycemicIndex: GlycemicIndex.LOW,
        isTraditionalSA: true,
        localNames: ['Boerewors', 'Farmer\'s sausage'],
        region: 'Nationwide',
        description: 'Traditional South African sausage made from beef and pork',
        diabeticNotes: 'Good protein source, watch sodium content',
        commonPortions: [
            { description: '1 coil (100g)', weight: 100, unit: 'g', isCommon: true },
            { description: '1 piece (50g)', weight: 50, unit: 'g', isCommon: true }
        ],
        isVerified: true,
        source: 'SA Food Composition Tables'
    },
    {
        name: 'Biltong (Beef)',
        category: FoodCategory.PROTEINS,
        caloriesPer100g: 550,
        carbohydratesPer100g: 4,
        proteinPer100g: 55,
        fatPer100g: 34,
        fiberPer100g: 0,
        glycemicIndex: GlycemicIndex.LOW,
        isTraditionalSA: true,
        localNames: ['Biltong'],
        region: 'Nationwide',
        description: 'Traditional South African dried meat snack',
        diabeticNotes: 'Excellent low-carb protein snack, high in sodium',
        commonPortions: [
            { description: '1 slice (10g)', weight: 10, unit: 'g', isCommon: true },
            { description: '1 handful (25g)', weight: 25, unit: 'g', isCommon: true }
        ],
        isVerified: true,
        source: 'SA Food Composition Tables'
    },
    {
        name: 'Morogo (African Spinach)',
        category: FoodCategory.VEGETABLES,
        caloriesPer100g: 23,
        carbohydratesPer100g: 3.6,
        proteinPer100g: 2.9,
        fatPer100g: 0.4,
        fiberPer100g: 2.2,
        glycemicIndex: GlycemicIndex.LOW,
        isTraditionalSA: true,
        localNames: ['Morogo', 'African spinach', 'Wild spinach'],
        region: 'Northern provinces',
        description: 'Traditional leafy green vegetable, highly nutritious',
        diabeticNotes: 'Excellent choice - low carbs, high nutrients',
        commonPortions: [
            { description: '1 cup cooked', weight: 180, unit: 'g', isCommon: true },
            { description: '1/2 cup cooked', weight: 90, unit: 'g', isCommon: true }
        ],
        isVerified: true,
        source: 'SA Food Composition Tables'
    },
    {
        name: 'Samp and Beans',
        category: FoodCategory.GRAINS_STARCHES,
        caloriesPer100g: 130,
        carbohydratesPer100g: 22,
        proteinPer100g: 6,
        fatPer100g: 2,
        fiberPer100g: 5,
        glycemicIndex: GlycemicIndex.MEDIUM,
        isTraditionalSA: true,
        localNames: ['Samp and beans', 'Umngqusho'],
        region: 'Eastern Cape, KwaZulu-Natal',
        description: 'Traditional dish of crushed maize kernels and beans',
        diabeticNotes: 'Good fiber content helps moderate blood sugar',
        commonPortions: [
            { description: '1 cup cooked', weight: 200, unit: 'g', isCommon: true },
            { description: '1/2 cup cooked', weight: 100, unit: 'g', isCommon: true }
        ],
        isVerified: true,
        source: 'SA Food Composition Tables'
    },
    {
        name: 'Rooibos Tea',
        category: FoodCategory.BEVERAGES,
        caloriesPer100g: 2,
        carbohydratesPer100g: 0.4,
        proteinPer100g: 0.1,
        fatPer100g: 0,
        fiberPer100g: 0,
        glycemicIndex: GlycemicIndex.LOW,
        isTraditionalSA: true,
        localNames: ['Rooibos', 'Red bush tea', 'Redbush'],
        region: 'Western Cape (Cederberg)',
        description: 'Caffeine-free herbal tea indigenous to South Africa',
        diabeticNotes: 'Excellent choice - no calories or carbs when unsweetened',
        commonPortions: [
            { description: '1 cup (250ml)', weight: 250, unit: 'ml', isCommon: true },
            { description: '1 mug (300ml)', weight: 300, unit: 'ml', isCommon: true }
        ],
        isVerified: true,
        source: 'SA Food Composition Tables'
    },

    // Common South African Fruits
    {
        name: 'Naartjie (Mandarin)',
        category: FoodCategory.FRUITS,
        caloriesPer100g: 53,
        carbohydratesPer100g: 13.3,
        proteinPer100g: 0.8,
        fatPer100g: 0.3,
        fiberPer100g: 1.8,
        glycemicIndex: GlycemicIndex.LOW,
        isTraditionalSA: false,
        localNames: ['Naartjie', 'Mandarin'],
        region: 'Western Cape, Eastern Cape',
        seasonality: ['May', 'June', 'July', 'August', 'September'],
        description: 'Popular South African citrus fruit',
        diabeticNotes: 'Good choice - moderate carbs, high fiber',
        commonPortions: [
            { description: '1 medium fruit', weight: 88, unit: 'g', isCommon: true },
            { description: '1 large fruit', weight: 120, unit: 'g', isCommon: true }
        ],
        isVerified: true,
        source: 'SA Food Composition Tables'
    },
    {
        name: 'Spanspek (Cantaloupe)',
        category: FoodCategory.FRUITS,
        caloriesPer100g: 34,
        carbohydratesPer100g: 8.2,
        proteinPer100g: 0.8,
        fatPer100g: 0.2,
        fiberPer100g: 0.9,
        glycemicIndex: GlycemicIndex.LOW,
        isTraditionalSA: false,
        localNames: ['Spanspek', 'Sweet melon'],
        region: 'Northern Cape, Limpopo',
        seasonality: ['November', 'December', 'January', 'February', 'March'],
        description: 'Sweet orange melon popular in South Africa',
        diabeticNotes: 'Good choice - low calories and carbs',
        commonPortions: [
            { description: '1 cup cubed', weight: 160, unit: 'g', isCommon: true },
            { description: '1 slice (1/8 melon)', weight: 134, unit: 'g', isCommon: true }
        ],
        isVerified: true,
        source: 'SA Food Composition Tables'
    },

    // Common Vegetables
    {
        name: 'Butternut Squash',
        category: FoodCategory.VEGETABLES,
        caloriesPer100g: 45,
        carbohydratesPer100g: 11.7,
        proteinPer100g: 1,
        fatPer100g: 0.1,
        fiberPer100g: 2,
        glycemicIndex: GlycemicIndex.MEDIUM,
        isTraditionalSA: false,
        localNames: ['Butternut', 'Butternut squash'],
        region: 'Nationwide',
        description: 'Popular orange winter squash',
        diabeticNotes: 'Moderate carbs - watch portion sizes',
        commonPortions: [
            { description: '1 cup cubed', weight: 140, unit: 'g', isCommon: true },
            { description: '1/2 cup mashed', weight: 122, unit: 'g', isCommon: true }
        ],
        isVerified: true,
        source: 'SA Food Composition Tables'
    },
    {
        name: 'Sweet Potato',
        category: FoodCategory.VEGETABLES,
        caloriesPer100g: 86,
        carbohydratesPer100g: 20.1,
        proteinPer100g: 1.6,
        fatPer100g: 0.1,
        fiberPer100g: 3,
        glycemicIndex: GlycemicIndex.MEDIUM,
        isTraditionalSA: false,
        localNames: ['Sweet potato', 'Patats'],
        region: 'KwaZulu-Natal, Limpopo',
        description: 'Nutritious orange root vegetable',
        diabeticNotes: 'Higher carbs - eat in moderation',
        commonPortions: [
            { description: '1 medium baked', weight: 128, unit: 'g', isCommon: true },
            { description: '1/2 cup mashed', weight: 100, unit: 'g', isCommon: true }
        ],
        isVerified: true,
        source: 'SA Food Composition Tables'
    },

    // Grains and Starches
    {
        name: 'Brown Bread (Whole Wheat)',
        category: FoodCategory.GRAINS_STARCHES,
        caloriesPer100g: 247,
        carbohydratesPer100g: 41,
        proteinPer100g: 13,
        fatPer100g: 4.2,
        fiberPer100g: 7,
        glycemicIndex: GlycemicIndex.MEDIUM,
        isTraditionalSA: false,
        description: 'Whole wheat brown bread',
        diabeticNotes: 'Better choice than white bread - higher fiber',
        commonPortions: [
            { description: '1 slice', weight: 28, unit: 'g', isCommon: true },
            { description: '2 slices', weight: 56, unit: 'g', isCommon: true }
        ],
        isVerified: true,
        source: 'SA Food Composition Tables'
    },
    {
        name: 'Mealie Meal (Maize Meal)',
        category: FoodCategory.GRAINS_STARCHES,
        caloriesPer100g: 365,
        carbohydratesPer100g: 77,
        proteinPer100g: 8.5,
        fatPer100g: 1.5,
        fiberPer100g: 3.5,
        glycemicIndex: GlycemicIndex.HIGH,
        isTraditionalSA: true,
        localNames: ['Mealie meal', 'Mieliemeel', 'Maize meal'],
        region: 'Nationwide',
        description: 'Ground maize used to make pap and other dishes',
        diabeticNotes: 'High GI - use sparingly, combine with protein',
        commonPortions: [
            { description: '1/4 cup dry', weight: 40, unit: 'g', isCommon: true },
            { description: '1/2 cup dry', weight: 80, unit: 'g', isCommon: true }
        ],
        isVerified: true,
        source: 'SA Food Composition Tables'
    }
];

export async function seedFoodDatabase(): Promise<void> {
    try {
        console.log('🌱 Starting food database seeding...');

        // Connect to database if not already connected
        if (mongoose.connection.readyState === 0) {
            await mongoose.connect(env.MONGODB_URI);
            console.log('📦 Connected to MongoDB for seeding');
        }

        // Clear existing food database entries (optional - comment out to preserve existing data)
        // await FoodDatabase.deleteMany({});
        // console.log('🗑️  Cleared existing food database entries');

        // Check if foods already exist to avoid duplicates
        const existingFoodsCount = await FoodDatabase.countDocuments({ isVerified: true });
        
        if (existingFoodsCount > 0) {
            console.log(`📊 Found ${existingFoodsCount} existing verified foods in database`);
            console.log('⏭️  Skipping seeding to avoid duplicates. Delete existing entries first if you want to re-seed.');
            return;
        }

        // Insert South African foods
        const insertedFoods = await FoodDatabase.insertMany(southAfricanFoods);
        console.log(`✅ Successfully seeded ${insertedFoods.length} South African foods`);

        // Create text indexes for search functionality
        await FoodDatabase.collection.createIndex({ 
            name: 'text', 
            brand: 'text', 
            localNames: 'text', 
            description: 'text' 
        });
        console.log('🔍 Created text search indexes');

        console.log('🎉 Food database seeding completed successfully!');
        
        // Log summary
        const categoryCounts = await FoodDatabase.aggregate([
            { $match: { isVerified: true } },
            { $group: { _id: '$category', count: { $sum: 1 } } },
            { $sort: { count: -1 } }
        ]);
        
        console.log('📈 Food database summary by category:');
        categoryCounts.forEach(cat => {
            console.log(`   ${cat._id}: ${cat.count} items`);
        });

    } catch (error) {
        console.error('❌ Error seeding food database:', error);
        throw error;
    }
}

// Run seeder if called directly
if (require.main === module) {
    seedFoodDatabase()
        .then(() => {
            console.log('✅ Seeding completed');
            process.exit(0);
        })
        .catch((error) => {
            console.error('❌ Seeding failed:', error);
            process.exit(1);
        });
}
