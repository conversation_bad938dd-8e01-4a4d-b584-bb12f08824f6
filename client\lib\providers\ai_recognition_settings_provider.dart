import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/ai_recognition_settings.dart';

/// Provider for managing AI Recognition settings
class AIRecognitionSettingsProvider extends ChangeNotifier {
  static const String _settingsKey = 'ai_recognition_settings';
  
  AIRecognitionSettings _settings = AIRecognitionSettings.defaultSettings;
  bool _isLoading = false;

  /// Current settings
  AIRecognitionSettings get settings => _settings;

  /// Loading state
  bool get isLoading => _isLoading;

  /// Initialize and load settings
  Future<void> initialize() async {
    _isLoading = true;
    notifyListeners();

    try {
      await _loadSettings();
    } catch (e) {
      debugPrint('Error loading AI recognition settings: $e');
      // Use default settings if loading fails
      _settings = AIRecognitionSettings.defaultSettings;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Load settings from storage
  Future<void> _loadSettings() async {
    final prefs = await SharedPreferences.getInstance();
    final settingsJson = prefs.getString(_settingsKey);
    
    if (settingsJson != null) {
      try {
        final Map<String, dynamic> json = {};
        // Parse JSON string if needed
        _settings = AIRecognitionSettings.fromJson(json);
      } catch (e) {
        debugPrint('Error parsing settings JSON: $e');
        _settings = AIRecognitionSettings.defaultSettings;
      }
    } else {
      _settings = AIRecognitionSettings.defaultSettings;
    }
  }

  /// Save settings to storage
  Future<void> _saveSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final settingsJson = _settings.toJson();
      await prefs.setString(_settingsKey, settingsJson.toString());
    } catch (e) {
      debugPrint('Error saving AI recognition settings: $e');
    }
  }

  /// Update settings
  Future<void> updateSettings(AIRecognitionSettings newSettings) async {
    _settings = newSettings;
    notifyListeners();
    await _saveSettings();
  }

  /// Update confidence thresholds
  Future<void> updateConfidenceThresholds({
    double? minimum,
    double? high,
    double? medium,
  }) async {
    _settings = _settings.copyWith(
      minimumConfidence: minimum,
      highConfidenceThreshold: high,
      mediumConfidenceThreshold: medium,
    );
    notifyListeners();
    await _saveSettings();
  }

  /// Update recognition preferences
  Future<void> updateRecognitionPreferences({
    bool? enableOfflineFirst,
    bool? enableMultiDetection,
    bool? enablePortionEstimation,
    bool? enableRealtimeAnalysis,
  }) async {
    _settings = _settings.copyWith(
      enableOfflineFirst: enableOfflineFirst,
      enableMultiDetection: enableMultiDetection,
      enablePortionEstimation: enablePortionEstimation,
      enableRealtimeAnalysis: enableRealtimeAnalysis,
    );
    notifyListeners();
    await _saveSettings();
  }

  /// Update performance settings
  Future<void> updatePerformanceSettings({
    int? maxDetectionResults,
    Duration? recognitionTimeout,
    bool? enableImageOptimization,
    double? imageQualityThreshold,
  }) async {
    _settings = _settings.copyWith(
      maxDetectionResults: maxDetectionResults,
      recognitionTimeout: recognitionTimeout,
      enableImageOptimization: enableImageOptimization,
      imageQualityThreshold: imageQualityThreshold,
    );
    notifyListeners();
    await _saveSettings();
  }

  /// Update UX settings
  Future<void> updateUXSettings({
    bool? showConfidenceIndicators,
    bool? enableHapticFeedback,
    bool? autoSaveRecognizedFoods,
    bool? enableLearningMode,
  }) async {
    _settings = _settings.copyWith(
      showConfidenceIndicators: showConfidenceIndicators,
      enableHapticFeedback: enableHapticFeedback,
      autoSaveRecognizedFoods: autoSaveRecognizedFoods,
      enableLearningMode: enableLearningMode,
    );
    notifyListeners();
    await _saveSettings();
  }

  /// Add custom food threshold
  Future<void> addCustomFoodThreshold(String foodName, double threshold) async {
    final updatedThresholds = Map<String, double>.from(_settings.customFoodThresholds);
    updatedThresholds[foodName.toLowerCase()] = threshold;
    
    _settings = _settings.copyWith(customFoodThresholds: updatedThresholds);
    notifyListeners();
    await _saveSettings();
  }

  /// Remove custom food threshold
  Future<void> removeCustomFoodThreshold(String foodName) async {
    final updatedThresholds = Map<String, double>.from(_settings.customFoodThresholds);
    updatedThresholds.remove(foodName.toLowerCase());
    
    _settings = _settings.copyWith(customFoodThresholds: updatedThresholds);
    notifyListeners();
    await _saveSettings();
  }

  /// Reset to default settings
  Future<void> resetToDefaults() async {
    _settings = AIRecognitionSettings.defaultSettings;
    notifyListeners();
    await _saveSettings();
  }

  /// Apply preset configuration
  Future<void> applyPreset(String presetName) async {
    switch (presetName.toLowerCase()) {
      case 'conservative':
        _settings = AIRecognitionSettings.conservativeSettings;
        break;
      case 'aggressive':
        _settings = AIRecognitionSettings.aggressiveSettings;
        break;
      case 'default':
      default:
        _settings = AIRecognitionSettings.defaultSettings;
        break;
    }
    notifyListeners();
    await _saveSettings();
  }

  /// Get confidence level for a score
  ConfidenceLevel getConfidenceLevel(double confidence) {
    return _settings.getConfidenceLevel(confidence);
  }

  /// Check if confidence is acceptable
  bool isConfidenceAcceptable(double confidence, {String? foodName}) {
    if (foodName != null) {
      final threshold = _settings.getThresholdForFood(foodName);
      return confidence >= threshold;
    }
    return _settings.isConfidenceAcceptable(confidence);
  }

  /// Get settings summary for display
  Map<String, dynamic> getSettingsSummary() {
    return {
      'Minimum Confidence': '${(_settings.minimumConfidence * 100).toInt()}%',
      'High Confidence': '${(_settings.highConfidenceThreshold * 100).toInt()}%',
      'Medium Confidence': '${(_settings.mediumConfidenceThreshold * 100).toInt()}%',
      'Offline First': _settings.enableOfflineFirst ? 'Enabled' : 'Disabled',
      'Multi-Detection': _settings.enableMultiDetection ? 'Enabled' : 'Disabled',
      'Portion Estimation': _settings.enablePortionEstimation ? 'Enabled' : 'Disabled',
      'Real-time Analysis': _settings.enableRealtimeAnalysis ? 'Enabled' : 'Disabled',
      'Max Results': _settings.maxDetectionResults.toString(),
      'Timeout': '${_settings.recognitionTimeout.inSeconds}s',
      'Custom Thresholds': _settings.customFoodThresholds.length.toString(),
    };
  }

  /// Export settings for backup
  Map<String, dynamic> exportSettings() {
    return _settings.toJson();
  }

  /// Import settings from backup
  Future<void> importSettings(Map<String, dynamic> settingsData) async {
    try {
      _settings = AIRecognitionSettings.fromJson(settingsData);
      notifyListeners();
      await _saveSettings();
    } catch (e) {
      debugPrint('Error importing settings: $e');
      throw Exception('Invalid settings data');
    }
  }
}
