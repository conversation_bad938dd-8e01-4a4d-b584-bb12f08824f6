import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;
import '../models/food_entry.dart';
import '../services/backend_service.dart';

class FavoritesService {
  static const String _favoritesKey = 'favorite_foods';
  static const String _frequentFoodsKey = 'frequent_foods';

  /// Get user's favorite foods
  static Future<List<FoodEntry>> getFavorites() async {
    try {
      // Try to get from backend first
      final response = await http
          .get(
            Uri.parse('${BackendService.baseUrl}/food-diary/favorites'),
            headers: {'Content-Type': 'application/json'},
          )
          .timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final List<dynamic> favoritesJson = data['favorites'] ?? [];
        return favoritesJson.map((json) => FoodEntry.fromJson(json)).toList();
      }
    } catch (e) {
      debugPrint('Error fetching favorites from backend: $e');
    }

    // Fallback to local storage
    return await _getLocalFavorites();
  }

  /// Get local favorites from SharedPreferences
  static Future<List<FoodEntry>> _getLocalFavorites() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final favoritesJson = prefs.getStringList(_favoritesKey) ?? [];

      return favoritesJson
          .map((json) => FoodEntry.fromJson(jsonDecode(json)))
          .toList();
    } catch (e) {
      debugPrint('Error loading local favorites: $e');
      return [];
    }
  }

  /// Add food to favorites
  static Future<bool> addToFavorites(FoodEntry food) async {
    try {
      // Try to add to backend first
      final response = await http
          .post(
            Uri.parse('${BackendService.baseUrl}/food-diary/favorites'),
            headers: {'Content-Type': 'application/json'},
            body: jsonEncode({'foodEntry': food.toJson()}),
          )
          .timeout(const Duration(seconds: 10));

      if (response.statusCode == 200 || response.statusCode == 201) {
        // Also save locally as backup
        await _addToLocalFavorites(food);
        return true;
      }
    } catch (e) {
      debugPrint('Error adding to backend favorites: $e');
    }

    // Fallback to local storage
    return await _addToLocalFavorites(food);
  }

  /// Add to local favorites
  static Future<bool> _addToLocalFavorites(FoodEntry food) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final favorites = await _getLocalFavorites();

      // Check if already in favorites
      if (favorites.any(
        (f) => f.name.toLowerCase() == food.name.toLowerCase(),
      )) {
        return false; // Already in favorites
      }

      favorites.add(food);
      final favoritesJson =
          favorites.map((f) => jsonEncode(f.toJson())).toList();

      await prefs.setStringList(_favoritesKey, favoritesJson);
      return true;
    } catch (e) {
      debugPrint('Error adding to local favorites: $e');
      return false;
    }
  }

  /// Remove from favorites
  static Future<bool> removeFromFavorites(String foodId) async {
    try {
      // Try to remove from backend first
      final response = await http
          .delete(
            Uri.parse('${BackendService.baseUrl}/food-diary/favorites/$foodId'),
            headers: {'Content-Type': 'application/json'},
          )
          .timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        // Also remove locally
        await _removeFromLocalFavorites(foodId);
        return true;
      }
    } catch (e) {
      debugPrint('Error removing from backend favorites: $e');
    }

    // Fallback to local storage
    return await _removeFromLocalFavorites(foodId);
  }

  /// Remove from local favorites
  static Future<bool> _removeFromLocalFavorites(String foodId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final favorites = await _getLocalFavorites();

      favorites.removeWhere((f) => f.id == foodId);
      final favoritesJson =
          favorites.map((f) => jsonEncode(f.toJson())).toList();

      await prefs.setStringList(_favoritesKey, favoritesJson);
      return true;
    } catch (e) {
      debugPrint('Error removing from local favorites: $e');
      return false;
    }
  }

  /// Check if food is in favorites
  static Future<bool> isFavorite(String foodName) async {
    final favorites = await getFavorites();
    return favorites.any((f) => f.name.toLowerCase() == foodName.toLowerCase());
  }

  /// Get frequently eaten foods
  static Future<List<FoodEntry>> getFrequentFoods() async {
    try {
      // Try to get from backend first
      final response = await http
          .get(
            Uri.parse('${BackendService.baseUrl}/food-diary/frequent-foods'),
            headers: {'Content-Type': 'application/json'},
          )
          .timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final List<dynamic> frequentJson = data['frequentFoods'] ?? [];
        return frequentJson.map((json) => FoodEntry.fromJson(json)).toList();
      }
    } catch (e) {
      debugPrint('Error fetching frequent foods from backend: $e');
    }

    // Fallback to local storage
    return await _getLocalFrequentFoods();
  }

  /// Get local frequent foods
  static Future<List<FoodEntry>> _getLocalFrequentFoods() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final frequentJson = prefs.getStringList(_frequentFoodsKey) ?? [];

      return frequentJson
          .map((json) => FoodEntry.fromJson(jsonDecode(json)))
          .toList();
    } catch (e) {
      debugPrint('Error loading local frequent foods: $e');
      return [];
    }
  }

  /// Track food consumption for frequency analysis
  static Future<void> trackFoodConsumption(FoodEntry food) async {
    try {
      // Send to backend for tracking
      await http
          .post(
            Uri.parse('${BackendService.baseUrl}/food-diary/track-consumption'),
            headers: {'Content-Type': 'application/json'},
            body: jsonEncode({'foodEntry': food.toJson()}),
          )
          .timeout(const Duration(seconds: 10));
    } catch (e) {
      debugPrint('Error tracking food consumption: $e');
    }

    // Also track locally
    await _trackLocalConsumption(food);
  }

  /// Track local consumption
  static Future<void> _trackLocalConsumption(FoodEntry food) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final consumptionData = prefs.getString('consumption_tracking') ?? '{}';
      final Map<String, dynamic> tracking = jsonDecode(consumptionData);

      final foodKey = food.name.toLowerCase();
      tracking[foodKey] = (tracking[foodKey] ?? 0) + 1;

      await prefs.setString('consumption_tracking', jsonEncode(tracking));
    } catch (e) {
      debugPrint('Error tracking local consumption: $e');
    }
  }

  /// Get quick access foods (combination of favorites and frequent)
  static Future<List<FoodEntry>> getQuickAccessFoods() async {
    final favorites = await getFavorites();
    final frequent = await getFrequentFoods();

    // Combine and deduplicate
    final combined = <String, FoodEntry>{};

    for (final food in favorites) {
      combined[food.name.toLowerCase()] = food;
    }

    for (final food in frequent) {
      combined[food.name.toLowerCase()] = food;
    }

    return combined.values.toList();
  }

  /// Clear all favorites
  static Future<void> clearFavorites() async {
    try {
      await http
          .delete(
            Uri.parse('${BackendService.baseUrl}/food-diary/favorites'),
            headers: {'Content-Type': 'application/json'},
          )
          .timeout(const Duration(seconds: 10));
    } catch (e) {
      debugPrint('Error clearing backend favorites: $e');
    }

    // Clear local favorites
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_favoritesKey);
  }
}
