/**
 * Authentication controller tests
 * Tests for user registration, login, and OTP verification
 */

import request from 'supertest';
import express from 'express';
import mongoose from 'mongoose';
import User from '../models/User';
import authRoutes from '../routes/auth';
import { errorHandler } from '../utils/errorHandler';

// Create test app
const app = express();
app.use(express.json());
app.use('/api/auth', authRoutes);
app.use(errorHandler);

describe('Authentication Endpoints', () => {
  beforeAll(async () => {
    // Connect to test database
    await mongoose.connect(process.env.MONGODB_URI!);
  });

  afterAll(async () => {
    // Clean up and close database connection
    await mongoose.connection.dropDatabase();
    await mongoose.connection.close();
  });

  beforeEach(async () => {
    // Clear users collection before each test
    await User.deleteMany({});
  });

  describe('POST /api/auth/register', () => {
    const validUserData = {
      email: '<EMAIL>',
      password: 'password123',
      phoneNumber: '+27123456789',
      name: 'Test User'
    };

    it('should register a new user successfully', async () => {
      const response = await request(app)
        .post('/api/auth/register')
        .send(validUserData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toContain('registered successfully');
      expect(response.body.userId).toBeDefined();

      // Verify user was created in database
      const user = await User.findOne({ email: validUserData.email });
      expect(user).toBeTruthy();
      expect(user?.email).toBe(validUserData.email);
      expect(user?.phoneNumber).toBe(validUserData.phoneNumber);
      expect(user?.isPhoneVerified).toBe(false);
    });

    it('should reject registration with invalid email', async () => {
      const invalidData = { ...validUserData, email: 'invalid-email' };
      
      const response = await request(app)
        .post('/api/auth/register')
        .send(invalidData)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('valid email');
    });

    it('should reject registration with short password', async () => {
      const invalidData = { ...validUserData, password: '123' };
      
      const response = await request(app)
        .post('/api/auth/register')
        .send(invalidData)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('password');
    });

    it('should reject registration with invalid phone number', async () => {
      const invalidData = { ...validUserData, phoneNumber: '123456789' };
      
      const response = await request(app)
        .post('/api/auth/register')
        .send(invalidData)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('phone number');
    });

    it('should reject duplicate email registration', async () => {
      // Create first user
      await request(app)
        .post('/api/auth/register')
        .send(validUserData)
        .expect(201);

      // Try to register with same email
      const duplicateData = { ...validUserData, phoneNumber: '+27987654321' };
      
      const response = await request(app)
        .post('/api/auth/register')
        .send(duplicateData)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('already exists');
    });
  });

  describe('POST /api/auth/verify-otp', () => {
    let testUser: any;

    beforeEach(async () => {
      // Create a test user
      testUser = await User.create({
        email: '<EMAIL>',
        password: 'password123',
        phoneNumber: '+27123456789',
        name: 'Test User'
      });
      
      // Set OTP for testing
      await testUser.setOTP('123456');
    });

    it('should verify OTP successfully', async () => {
      const response = await request(app)
        .post('/api/auth/verify-otp')
        .send({
          phoneNumber: '+27123456789',
          otp: '123456'
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.token).toBeDefined();
      expect(response.body.user).toBeDefined();
      expect(response.body.user.name).toBe('Test User');

      // Verify user is marked as verified
      const updatedUser = await User.findById(testUser._id);
      expect(updatedUser?.isPhoneVerified).toBe(true);
    });

    it('should reject invalid OTP', async () => {
      const response = await request(app)
        .post('/api/auth/verify-otp')
        .send({
          phoneNumber: '+27123456789',
          otp: '999999'
        })
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('Invalid or expired OTP');
    });

    it('should reject OTP for non-existent phone number', async () => {
      const response = await request(app)
        .post('/api/auth/verify-otp')
        .send({
          phoneNumber: '+27999999999',
          otp: '123456'
        })
        .expect(404);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('User not found');
    });
  });

  describe('POST /api/auth/login', () => {
    let verifiedUser: any;

    beforeEach(async () => {
      // Create a verified test user
      verifiedUser = await User.create({
        email: '<EMAIL>',
        password: 'password123',
        phoneNumber: '+27123456789',
        name: 'Verified User',
        isPhoneVerified: true
      });
    });

    it('should login successfully with valid credentials', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'password123'
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.token).toBeDefined();
      expect(response.body.user).toBeDefined();
      expect(response.body.user.email).toBe('<EMAIL>');
    });

    it('should reject login with invalid email', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'password123'
        })
        .expect(401);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('Invalid credentials');
    });

    it('should reject login with invalid password', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'wrongpassword'
        })
        .expect(401);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('Invalid credentials');
    });

    it('should reject login for unverified user', async () => {
      // Create unverified user
      await User.create({
        email: '<EMAIL>',
        password: 'password123',
        phoneNumber: '+27987654321',
        name: 'Unverified User',
        isPhoneVerified: false
      });

      const response = await request(app)
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'password123'
        })
        .expect(401);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('verify your phone number');
    });
  });
});
