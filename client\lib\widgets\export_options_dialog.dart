import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../constants/app_colors.dart';
import '../services/glucose_service.dart';
import '../providers/auth_provider.dart';
import 'package:intl/intl.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import 'dart:io';

class ExportOptionsDialog extends StatefulWidget {
  final DateTime? defaultStartDate;
  final DateTime? defaultEndDate;
  final String? defaultChartType;

  const ExportOptionsDialog({
    super.key,
    this.defaultStartDate,
    this.defaultEndDate,
    this.defaultChartType,
  });

  @override
  State<ExportOptionsDialog> createState() => _ExportOptionsDialogState();
}

class _ExportOptionsDialogState extends State<ExportOptionsDialog> {
  String _selectedFormat = '';
  String _selectedDestination = '';
  String _selectedChartType = '';
  DateTime? _startDate;
  DateTime? _endDate;
  final _emailController = TextEditingController();
  bool _isLoading = false;

  final List<Map<String, String>> _formats = [
    {'value': 'pdf', 'label': 'PDF Report', 'icon': '📄'},
    {'value': 'excel', 'label': 'Excel Spreadsheet', 'icon': '📊'},
    {'value': 'csv', 'label': 'CSV Data', 'icon': '📋'},
    {'value': 'json', 'label': 'JSON Data', 'icon': '🔧'},
  ];

  final List<Map<String, String>> _destinations = [
    {'value': 'download', 'label': 'Download', 'icon': '⬇️'},
    {'value': 'email', 'label': 'Email', 'icon': '📧'},
    {'value': 'googledrive', 'label': 'Google Drive', 'icon': '☁️'},
    {'value': 'dropbox', 'label': 'Dropbox', 'icon': '📦'},
  ];

  final List<Map<String, String>> _chartTypes = [
    {'value': 'line', 'label': 'Line Chart', 'icon': '📈'},
    {'value': 'area', 'label': 'Area Chart', 'icon': '📊'},
    {'value': 'bar', 'label': 'Bar Chart', 'icon': '📊'},
    {'value': 'scatter', 'label': 'Scatter Plot', 'icon': '⚫'},
  ];

  @override
  void initState() {
    super.initState();
    _startDate = widget.defaultStartDate;
    _endDate = widget.defaultEndDate;
    if (widget.defaultChartType != null) {
      _selectedChartType = widget.defaultChartType!;
    }
  }

  @override
  void dispose() {
    _emailController.dispose();
    super.dispose();
  }

  Future<void> _selectDateRange() async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now(),
      initialDateRange:
          _startDate != null && _endDate != null
              ? DateTimeRange(start: _startDate!, end: _endDate!)
              : null,
    );

    if (picked != null) {
      setState(() {
        _startDate = picked.start;
        _endDate = picked.end;
      });
    }
  }

  String _getDateRangeText() {
    if (_startDate == null || _endDate == null) {
      return 'Select date range';
    }
    return '${DateFormat('MMM dd').format(_startDate!)} - ${DateFormat('MMM dd, yyyy').format(_endDate!)}';
  }

  bool get _canExport {
    return _selectedFormat.isNotEmpty &&
        _selectedDestination.isNotEmpty &&
        _selectedChartType.isNotEmpty &&
        _startDate != null &&
        _endDate != null &&
        (_selectedDestination != 'email' ||
            _emailController.text.trim().isNotEmpty);
  }

  Future<void> _performExport() async {
    if (!_canExport) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please fill in all required fields'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final token = authProvider.backendToken;

      if (token == null) {
        throw Exception('Authentication required');
      }

      final response = await GlucoseService.exportChart(
        token: token,
        startDate: _startDate,
        endDate: _endDate,
        chartType: _selectedChartType,
        format: _selectedFormat,
        destination: _selectedDestination,
        email:
            _selectedDestination == 'email'
                ? _emailController.text.trim()
                : null,
      );

      if (mounted) {
        Navigator.of(context).pop();

        String message = response.message;

        // Handle file download for local destination
        if (_selectedDestination == 'download' && response.data != null) {
          try {
            final savedPath = await _saveFileToDevice(
              response.data!,
              response.fileName,
            );
            message = 'File saved successfully to: $savedPath';

            // Show option to share the file
            _showFileActionDialog(savedPath, response.fileName ?? 'export');
          } catch (e) {
            message = 'File downloaded but could not be saved: $e';
          }
        } else if (response.link != null) {
          message += '\nLink: ${response.link}';
        }

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(message),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 4),
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Export failed: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: AppColors.surface,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        constraints: BoxConstraints(
          maxWidth: 500,
          maxHeight:
              MediaQuery.of(context).size.height *
              0.9, // Increased from 600 to 90% of screen height
        ),
        padding: const EdgeInsets.all(24),
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                children: [
                  const Icon(
                    Icons.download,
                    color: AppColors.primary,
                    size: 28,
                  ),
                  const SizedBox(width: 12),
                  const Text(
                    'Export Options',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: AppColors.onSurface,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close, color: AppColors.onSurface),
                  ),
                ],
              ),
              const SizedBox(height: 24),

              // Format Selection
              _buildSectionTitle('Export Format'),
              _buildOptionGrid(_formats, _selectedFormat, (value) {
                setState(() => _selectedFormat = value);
              }),
              const SizedBox(height: 20),

              // Destination Selection
              _buildSectionTitle('Destination'),
              _buildOptionGrid(_destinations, _selectedDestination, (value) {
                setState(() => _selectedDestination = value);
              }),
              const SizedBox(height: 20),

              // Email field for email destination
              if (_selectedDestination == 'email') ...[
                _buildSectionTitle('Email Address'),
                TextField(
                  controller: _emailController,
                  decoration: const InputDecoration(
                    hintText: 'Enter email address',
                    prefixIcon: Icon(Icons.email),
                    border: OutlineInputBorder(),
                  ),
                  keyboardType: TextInputType.emailAddress,
                ),
                const SizedBox(height: 20),
              ],

              // Chart Type Selection
              _buildSectionTitle('Chart Type'),
              _buildOptionGrid(_chartTypes, _selectedChartType, (value) {
                setState(() => _selectedChartType = value);
              }),
              const SizedBox(height: 20),

              // Date Range Selection
              _buildSectionTitle('Date Range'),
              InkWell(
                onTap: _selectDateRange,
                child: Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      const Icon(Icons.date_range),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          _getDateRangeText(),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      const Icon(Icons.arrow_drop_down),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 24),

              // Action Buttons
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed:
                          _isLoading ? null : () => Navigator.of(context).pop(),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: Colors.white,
                        side: const BorderSide(color: Colors.white),
                      ),
                      child: const Text('Cancel'),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _isLoading ? null : _performExport,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primary,
                        foregroundColor: Colors.white,
                      ),
                      child:
                          _isLoading
                              ? const SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    Colors.white,
                                  ),
                                ),
                              )
                              : const Text('Export'),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w600,
          color: AppColors.onSurface,
        ),
      ),
    );
  }

  Widget _buildOptionGrid(
    List<Map<String, String>> options,
    String selectedValue,
    Function(String) onChanged,
  ) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 3,
        crossAxisSpacing: 8,
        mainAxisSpacing: 8,
      ),
      itemCount: options.length,
      itemBuilder: (context, index) {
        final option = options[index];
        final isSelected = option['value'] == selectedValue;

        return InkWell(
          onTap: () => onChanged(option['value']!),
          child: Container(
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(
              color:
                  isSelected
                      ? Colors.white.withValues(alpha: 0.2)
                      : Colors.white.withValues(alpha: 0.05),
              border: Border.all(
                color:
                    isSelected
                        ? Colors.white
                        : Colors.white.withValues(alpha: 0.3),
                width: isSelected ? 2 : 1,
              ),
              borderRadius: BorderRadius.circular(8),
              boxShadow:
                  isSelected
                      ? [
                        BoxShadow(
                          color: Colors.white.withValues(alpha: 0.3),
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ]
                      : null,
            ),
            child: Row(
              children: [
                Text(
                  option['icon']!,
                  style: TextStyle(
                    fontSize: 15,
                    shadows:
                        isSelected
                            ? [const Shadow(color: Colors.white, blurRadius: 2)]
                            : null,
                  ),
                ),
                const SizedBox(width: 6),
                Expanded(
                  child: Text(
                    option['label']!,
                    style: TextStyle(
                      fontSize: 13,
                      fontWeight:
                          isSelected ? FontWeight.w600 : FontWeight.normal,
                      color: AppColors.onSurface,
                      shadows:
                          isSelected
                              ? [
                                const Shadow(
                                  color: Colors.white,
                                  blurRadius: 1,
                                ),
                              ]
                              : null,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                if (isSelected)
                  const Icon(Icons.check_circle, color: Colors.white, size: 10),
              ],
            ),
          ),
        );
      },
    );
  }

  Future<String> _saveFileToDevice(List<int> fileData, String? fileName) async {
    try {
      Directory? directory;

      if (Platform.isAndroid) {
        directory = Directory('/storage/emulated/0/Download');
        if (!await directory.exists()) {
          directory = await getExternalStorageDirectory();
        }
      } else {
        directory = await getApplicationDocumentsDirectory();
      }

      if (directory == null) {
        throw Exception('Could not access storage directory');
      }

      final glucoMonitorDir = Directory('${directory.path}/GlucoMonitor');
      if (!await glucoMonitorDir.exists()) {
        await glucoMonitorDir.create(recursive: true);
      }

      final finalFileName =
          fileName ?? 'glucose_export_${DateTime.now().millisecondsSinceEpoch}';
      final filePath = '${glucoMonitorDir.path}/$finalFileName';

      final file = File(filePath);
      await file.writeAsBytes(fileData);

      return filePath;
    } catch (e) {
      throw Exception('Failed to save file: $e');
    }
  }

  void _showFileActionDialog(String filePath, String fileName) {
    if (!mounted) return;

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            backgroundColor: AppColors.surface,
            title: const Text(
              'File Saved Successfully',
              style: TextStyle(color: AppColors.onSurface),
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Your file has been saved to:',
                  style: TextStyle(color: AppColors.onSurface),
                ),
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    filePath,
                    style: const TextStyle(
                      color: AppColors.onSurface,
                      fontSize: 12,
                      fontFamily: 'monospace',
                    ),
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text(
                  'OK',
                  style: TextStyle(color: AppColors.onSurface),
                ),
              ),
              ElevatedButton(
                onPressed: () async {
                  final navigator = Navigator.of(context);
                  final scaffoldMessenger = ScaffoldMessenger.of(context);
                  navigator.pop();
                  try {
                    await Share.shareXFiles([
                      XFile(filePath),
                    ], text: 'GlucoMonitor Export: $fileName');
                  } catch (e) {
                    if (mounted) {
                      scaffoldMessenger.showSnackBar(
                        SnackBar(
                          content: Text('Could not share file: $e'),
                          backgroundColor: Colors.red,
                        ),
                      );
                    }
                  }
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Share'),
              ),
            ],
          ),
    );
  }
}
