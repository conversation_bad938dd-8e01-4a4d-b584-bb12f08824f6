import express from 'express';
import {
    createFoodEntry,
    getFoodEntries,
    updateFoodEntry,
    deleteFoodEntry,
    getDailyNutrition,
    updateNutritionGoals,
    searchFoodDatabase,
    searchFoodDatabaseMultilingual,
    getFoodsByCategory,
    getDiabetesFriendlyFoods,
    getTraditionalSAFoods,
    getFoodsByOrigin,
    getFoodsByDietaryTags,
    searchByNutrition,
    getNutritionAnalytics
} from '../controllers/foodDiary';

// Import new controllers
import {
    recordRecognitionAttempt,
    getRecognitionHistory,
    getRecognitionAnalytics,
    updateRecognitionFeedback,
    getAccuracyTrends,
    getProblematicFoods,
    getSuccessRateByMethod,
    deleteRecognitionHistory
} from '../controllers/foodRecognitionController';

// Import enhanced AI controllers
import {
    enhancedFoodRecognition,
    trainCustomFood,
    getCustomTrainedFoods,
    updateCustomFood,
    getRecognitionAnalytics as getEnhancedAnalytics,
    submitRecognitionFeedback
} from '../controllers/enhancedFoodRecognitionController';

import {
    getFoodSuggestions,
    getSuggestionsByType,
    getBloodSugarFriendlySuggestions,
    getCulturalSuggestions,
    getMealCompletionSuggestions,
    getSeasonalSuggestions,
    saveSuggestionFeedback,
    getSuggestionAnalytics
} from '../controllers/foodSuggestionController';

import {
    performNutritionAnalysis,
    getMealImpactPredictions,
    getNutrientDeficiencyAnalysis,
    getBloodSugarCorrelations,
    getDietaryPatternAnalysis,
    getMetabolicHealthAssessment,
    getPersonalizedRecommendations,
    generateNutritionReport
} from '../controllers/nutritionAnalysisController';

import { protect } from '../middleware/auth';

const router = express.Router();

console.log('🍽️ Food diary routes module loaded');

// Apply authentication middleware to all routes
router.use(protect);

// Food Entry Routes
// @route   POST /api/food-diary/entries
// @desc    Create a new food entry
// @access  Private
router.post('/entries', createFoodEntry);

// @route   GET /api/food-diary/entries
// @desc    Get food entries (with optional filters)
// @access  Private
router.get('/entries', getFoodEntries);

// @route   PUT /api/food-diary/entries/:id
// @desc    Update a food entry
// @access  Private
router.put('/entries/:id', updateFoodEntry);

// @route   DELETE /api/food-diary/entries/:id
// @desc    Delete a food entry
// @access  Private
router.delete('/entries/:id', deleteFoodEntry);

// Daily Nutrition Routes
// @route   GET /api/food-diary/daily-nutrition
// @desc    Get daily nutrition summary
// @access  Private
router.get('/daily-nutrition', getDailyNutrition);

// @route   PUT /api/food-diary/nutrition-goals
// @desc    Update nutrition goals
// @access  Private
router.put('/nutrition-goals', updateNutritionGoals);

// Food Database Search Routes
// @route   GET /api/food-diary/search
// @desc    Search food database
// @access  Private
router.get('/search', searchFoodDatabase);

// @route   GET /api/food-diary/search/multilingual
// @desc    Search food database with multilingual support
// @access  Private
router.get('/search/multilingual', searchFoodDatabaseMultilingual);

// @route   GET /api/food-diary/search/nutrition
// @desc    Advanced nutrition search
// @access  Private
router.get('/search/nutrition', searchByNutrition);

// @route   GET /api/food-diary/foods/category/:category
// @desc    Get foods by category
// @access  Private
router.get('/foods/category/:category', getFoodsByCategory);

// @route   GET /api/food-diary/foods/diabetes-friendly
// @desc    Get diabetes-friendly foods
// @access  Private
router.get('/foods/diabetes-friendly', getDiabetesFriendlyFoods);

// @route   GET /api/food-diary/foods/traditional-sa
// @desc    Get traditional South African foods
// @access  Private
router.get('/foods/traditional-sa', getTraditionalSAFoods);

// @route   GET /api/food-diary/foods/origin/:origin
// @desc    Get foods by origin (traditional_sa, international, local_adaptation)
// @access  Private
router.get('/foods/origin/:origin', getFoodsByOrigin);

// @route   GET /api/food-diary/foods/dietary
// @desc    Get foods by dietary tags
// @access  Private
router.get('/foods/dietary', getFoodsByDietaryTags);

// Analytics Routes
// @route   GET /api/food-diary/analytics
// @desc    Get nutrition analytics for date range
// @access  Private
router.get('/analytics', getNutritionAnalytics);

// ===== NEW FEATURES =====

// Food Recognition History Routes
// @route   POST /api/food-diary/recognition/record
// @desc    Record a food recognition attempt
// @access  Private
router.post('/recognition/record', recordRecognitionAttempt);

// @route   GET /api/food-diary/recognition/history
// @desc    Get user's food recognition history
// @access  Private
router.get('/recognition/history', getRecognitionHistory);

// @route   GET /api/food-diary/recognition/analytics
// @desc    Get recognition analytics for user
// @access  Private
router.get('/recognition/analytics', getRecognitionAnalytics);

// @route   PUT /api/food-diary/recognition/:recognitionId/feedback
// @desc    Update recognition entry with user feedback
// @access  Private
router.put('/recognition/:recognitionId/feedback', updateRecognitionFeedback);

// @route   GET /api/food-diary/recognition/accuracy-trends
// @desc    Get recognition accuracy trends
// @access  Private
router.get('/recognition/accuracy-trends', getAccuracyTrends);

// @route   GET /api/food-diary/recognition/problematic-foods
// @desc    Get most problematic foods for recognition
// @access  Private
router.get('/recognition/problematic-foods', getProblematicFoods);

// @route   GET /api/food-diary/recognition/success-rate-by-method
// @desc    Get recognition success rate by method
// @access  Private
router.get('/recognition/success-rate-by-method', getSuccessRateByMethod);

// @route   DELETE /api/food-diary/recognition/history
// @desc    Delete recognition history entries
// @access  Private
router.delete('/recognition/history', deleteRecognitionHistory);

// Enhanced AI Food Recognition Routes
// @route   POST /api/food-diary/ai/recognize
// @desc    Enhanced food recognition with multi-detection and confidence scoring
// @access  Private
router.post('/ai/recognize', enhancedFoodRecognition);

// @route   POST /api/food-diary/ai/train-custom
// @desc    Train custom food model
// @access  Private
router.post('/ai/train-custom', trainCustomFood);

// @route   GET /api/food-diary/ai/custom-foods
// @desc    Get user's custom trained foods
// @access  Private
router.get('/ai/custom-foods', getCustomTrainedFoods);

// @route   PUT /api/food-diary/ai/custom-foods/:customFoodId
// @desc    Update custom food training
// @access  Private
router.put('/ai/custom-foods/:customFoodId', updateCustomFood);

// @route   GET /api/food-diary/ai/analytics
// @desc    Get enhanced recognition analytics
// @access  Private
router.get('/ai/analytics', getEnhancedAnalytics);

// @route   POST /api/food-diary/ai/feedback
// @desc    Submit recognition feedback for learning
// @access  Private
router.post('/ai/feedback', submitRecognitionFeedback);

// Food Suggestion Routes
// @route   GET /api/food-diary/suggestions
// @desc    Get personalized food suggestions
// @access  Private
router.get('/suggestions', getFoodSuggestions);

// @route   GET /api/food-diary/suggestions/type/:suggestionType
// @desc    Get suggestions by specific type
// @access  Private
router.get('/suggestions/type/:suggestionType', getSuggestionsByType);

// @route   GET /api/food-diary/suggestions/blood-sugar-friendly
// @desc    Get blood sugar friendly suggestions
// @access  Private
router.get('/suggestions/blood-sugar-friendly', getBloodSugarFriendlySuggestions);

// @route   GET /api/food-diary/suggestions/cultural
// @desc    Get cultural food suggestions
// @access  Private
router.get('/suggestions/cultural', getCulturalSuggestions);

// @route   GET /api/food-diary/suggestions/meal-completion
// @desc    Get meal completion suggestions
// @access  Private
router.get('/suggestions/meal-completion', getMealCompletionSuggestions);

// @route   GET /api/food-diary/suggestions/seasonal
// @desc    Get seasonal food suggestions
// @access  Private
router.get('/suggestions/seasonal', getSeasonalSuggestions);

// @route   POST /api/food-diary/suggestions/feedback
// @desc    Save user feedback on suggestions
// @access  Private
router.post('/suggestions/feedback', saveSuggestionFeedback);

// @route   GET /api/food-diary/suggestions/analytics
// @desc    Get suggestion performance analytics
// @access  Private
router.get('/suggestions/analytics', getSuggestionAnalytics);

// Advanced Nutrition Analysis Routes
// @route   POST /api/food-diary/analysis/perform
// @desc    Perform comprehensive nutrition analysis
// @access  Private
router.post('/analysis/perform', performNutritionAnalysis);

// @route   GET /api/food-diary/analysis/meal-impact
// @desc    Get meal impact predictions
// @access  Private
router.get('/analysis/meal-impact', getMealImpactPredictions);

// @route   GET /api/food-diary/analysis/nutrient-deficiency
// @desc    Get nutrient deficiency analysis
// @access  Private
router.get('/analysis/nutrient-deficiency', getNutrientDeficiencyAnalysis);

// @route   GET /api/food-diary/analysis/blood-sugar-correlations
// @desc    Get blood sugar correlation analysis
// @access  Private
router.get('/analysis/blood-sugar-correlations', getBloodSugarCorrelations);

// @route   GET /api/food-diary/analysis/dietary-patterns
// @desc    Get dietary pattern analysis
// @access  Private
router.get('/analysis/dietary-patterns', getDietaryPatternAnalysis);

// @route   GET /api/food-diary/analysis/metabolic-health
// @desc    Get metabolic health assessment
// @access  Private
router.get('/analysis/metabolic-health', getMetabolicHealthAssessment);

// @route   GET /api/food-diary/analysis/recommendations
// @desc    Get personalized recommendations
// @access  Private
router.get('/analysis/recommendations', getPersonalizedRecommendations);

// @route   POST /api/food-diary/analysis/report
// @desc    Generate comprehensive nutrition report
// @access  Private
router.post('/analysis/report', generateNutritionReport);

// Debug route to test if routes are working
router.get('/test', (req, res) => {
    console.log('🍽️ Food diary test route hit!');
    res.json({
        success: true,
        message: 'Food diary routes are working!',
        timestamp: new Date().toISOString()
    });
});

console.log('🍽️ Food diary routes defined:', router.stack?.length || 'unknown');

export default router;
