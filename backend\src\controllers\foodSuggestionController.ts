import { Response } from 'express';
import mongoose from 'mongoose';
import { AuthRequest } from '../middleware/auth';
import { FoodSuggestionService, ISuggestionContext, SuggestionType } from '../services/foodSuggestionService';
import { MealType, FoodCategory } from '../models/FoodEntry';
import GlucoseReading from '../models/GlucoseReading';
import DailyNutrition from '../models/DailyNutrition';
import { catchAsync } from '../utils/errorHandler';
import logger from '../utils/logger';

/**
 * Get personalized food suggestions
 */
export const getFoodSuggestions = catchAsync(async (req: AuthRequest, res: Response): Promise<void> => {
    const userId = new mongoose.Types.ObjectId(req.user!._id.toString());
    const {
        mealType = MealType.LUNCH,
        maxSuggestions = 10,
        excludeCategories,
        preferredCategories,
        dietaryRestrictions,
        includeBloodSugarPrediction = true
    } = req.query;

    // Validate meal type
    if (!Object.values(MealType).includes(mealType as MealType)) {
        res.status(400).json({
            success: false,
            message: 'Invalid meal type'
        });
        return;
    }

    // Get recent blood sugar reading
    const recentBloodSugar = await getRecentBloodSugar(userId);

    // Get today's nutrition data
    const todayNutrition = await getTodayNutrition(userId);

    // Build suggestion context
    const context: ISuggestionContext = {
        userId,
        mealType: mealType as MealType,
        currentTime: new Date(),
        recentBloodSugar: recentBloodSugar?.value,
        targetBloodSugarRange: recentBloodSugar?.targetRange,
        remainingCalories: todayNutrition?.remainingCalories,
        remainingCarbs: todayNutrition?.remainingCarbs,
        dietaryRestrictions: dietaryRestrictions ? (dietaryRestrictions as string).split(',') : undefined,
        preferredCategories: preferredCategories ? (preferredCategories as string).split(',') as FoodCategory[] : undefined,
        excludeCategories: excludeCategories ? (excludeCategories as string).split(',') as FoodCategory[] : undefined,
        maxSuggestions: Number(maxSuggestions)
    };

    // Generate suggestions
    const suggestions = await FoodSuggestionService.generateSuggestions(context);

    // Log suggestion request
    logger.info('Food suggestions generated', {
        userId: userId.toString(),
        mealType,
        suggestionsCount: suggestions.length,
        recentBloodSugar: recentBloodSugar?.value
    });

    res.json({
        success: true,
        data: {
            suggestions,
            context: {
                mealType,
                currentTime: context.currentTime,
                recentBloodSugar: recentBloodSugar?.value,
                remainingCalories: context.remainingCalories,
                remainingCarbs: context.remainingCarbs
            },
            metadata: {
                totalSuggestions: suggestions.length,
                averageConfidence: suggestions.length > 0 
                    ? suggestions.reduce((sum, s) => sum + s.confidence, 0) / suggestions.length 
                    : 0,
                suggestionTypes: [...new Set(suggestions.map(s => s.suggestionType))]
            }
        }
    });
});

/**
 * Get suggestions by specific type
 */
export const getSuggestionsByType = catchAsync(async (req: AuthRequest, res: Response): Promise<void> => {
    const userId = new mongoose.Types.ObjectId(req.user!._id.toString());
    const { suggestionType } = req.params;
    const {
        mealType = MealType.LUNCH,
        limit = 5
    } = req.query;

    // Validate suggestion type
    if (!Object.values(SuggestionType).includes(suggestionType as SuggestionType)) {
        res.status(400).json({
            success: false,
            message: 'Invalid suggestion type'
        });
        return;
    }

    const context: ISuggestionContext = {
        userId,
        mealType: mealType as MealType,
        currentTime: new Date(),
        maxSuggestions: Number(limit)
    };

    // Generate all suggestions and filter by type
    const allSuggestions = await FoodSuggestionService.generateSuggestions(context);
    const filteredSuggestions = allSuggestions.filter(s => s.suggestionType === suggestionType);

    res.json({
        success: true,
        data: {
            suggestionType,
            suggestions: filteredSuggestions.slice(0, Number(limit)),
            totalAvailable: filteredSuggestions.length
        }
    });
});

/**
 * Get blood sugar friendly suggestions
 */
export const getBloodSugarFriendlySuggestions = catchAsync(async (req: AuthRequest, res: Response): Promise<void> => {
    const userId = new mongoose.Types.ObjectId(req.user!._id.toString());
    const {
        currentBloodSugar,
        targetRange = '80-180',
        mealType = MealType.LUNCH,
        limit = 8
    } = req.query;

    // Parse target range
    const [minTarget, maxTarget] = (targetRange as string).split('-').map(Number);
    
    const context: ISuggestionContext = {
        userId,
        mealType: mealType as MealType,
        currentTime: new Date(),
        recentBloodSugar: currentBloodSugar ? Number(currentBloodSugar) : undefined,
        targetBloodSugarRange: { min: minTarget, max: maxTarget },
        maxSuggestions: Number(limit)
    };

    const suggestions = await FoodSuggestionService.generateSuggestions(context);
    
    // Filter for blood sugar friendly suggestions
    const bloodSugarFriendly = suggestions.filter(s => 
        s.suggestionType === SuggestionType.BLOOD_SUGAR_FRIENDLY ||
        s.suggestionType === SuggestionType.DIABETES_MANAGEMENT
    );

    res.json({
        success: true,
        data: {
            suggestions: bloodSugarFriendly,
            currentBloodSugar: currentBloodSugar ? Number(currentBloodSugar) : null,
            targetRange: { min: minTarget, max: maxTarget },
            recommendations: generateBloodSugarRecommendations(
                currentBloodSugar ? Number(currentBloodSugar) : null,
                { min: minTarget, max: maxTarget }
            )
        }
    });
});

/**
 * Get cultural food suggestions
 */
export const getCulturalSuggestions = catchAsync(async (req: AuthRequest, res: Response): Promise<void> => {
    const userId = new mongoose.Types.ObjectId(req.user!._id.toString());
    const {
        mealType = MealType.LUNCH,
        region = 'south_africa',
        limit = 6
    } = req.query;

    const context: ISuggestionContext = {
        userId,
        mealType: mealType as MealType,
        currentTime: new Date(),
        maxSuggestions: Number(limit)
    };

    const suggestions = await FoodSuggestionService.generateSuggestions(context);
    
    // Filter for cultural suggestions
    const culturalSuggestions = suggestions.filter(s => 
        s.suggestionType === SuggestionType.CULTURAL_PREFERENCE &&
        s.food.isTraditionalSA
    );

    res.json({
        success: true,
        data: {
            region,
            suggestions: culturalSuggestions,
            culturalInfo: {
                traditionalFoodsCount: culturalSuggestions.length,
                availableLanguages: ['en', 'af', 'zu', 'xh'],
                seasonalRecommendations: getSeasonalRecommendations()
            }
        }
    });
});

/**
 * Get meal completion suggestions
 */
export const getMealCompletionSuggestions = catchAsync(async (req: AuthRequest, res: Response): Promise<void> => {
    const userId = new mongoose.Types.ObjectId(req.user!._id.toString());
    const {
        mealType = MealType.LUNCH,
        currentMealItems,
        limit = 5
    } = req.query;

    // Parse current meal items if provided
    const currentItems = currentMealItems ? JSON.parse(currentMealItems as string) : [];

    const context: ISuggestionContext = {
        userId,
        mealType: mealType as MealType,
        currentTime: new Date(),
        maxSuggestions: Number(limit)
    };

    const suggestions = await FoodSuggestionService.generateSuggestions(context);
    
    // Filter for meal completion suggestions
    const completionSuggestions = suggestions.filter(s => 
        s.suggestionType === SuggestionType.MEAL_COMPLETION ||
        s.suggestionType === SuggestionType.NUTRITIONAL_BALANCE
    );

    // Analyze current meal
    const mealAnalysis = analyzeMealBalance(currentItems);

    res.json({
        success: true,
        data: {
            suggestions: completionSuggestions,
            currentMeal: {
                items: currentItems,
                analysis: mealAnalysis
            },
            recommendations: generateMealCompletionRecommendations(mealAnalysis)
        }
    });
});

/**
 * Get seasonal food suggestions
 */
export const getSeasonalSuggestions = catchAsync(async (req: AuthRequest, res: Response): Promise<void> => {
    const userId = new mongoose.Types.ObjectId(req.user!._id.toString());
    const {
        mealType = MealType.LUNCH,
        month,
        limit = 6
    } = req.query;

    const targetMonth = month ? Number(month) : new Date().getMonth() + 1;
    const season = getSeason(targetMonth);

    const context: ISuggestionContext = {
        userId,
        mealType: mealType as MealType,
        currentTime: new Date(),
        maxSuggestions: Number(limit)
    };

    const suggestions = await FoodSuggestionService.generateSuggestions(context);
    
    // Filter for seasonal suggestions
    const seasonalSuggestions = suggestions.filter(s => 
        s.suggestionType === SuggestionType.SEASONAL_AVAILABILITY
    );

    res.json({
        success: true,
        data: {
            season,
            month: targetMonth,
            suggestions: seasonalSuggestions,
            seasonalInfo: {
                inSeasonFoods: getInSeasonFoods(season),
                nutritionalBenefits: getSeasonalNutritionalBenefits(season),
                cookingTips: getSeasonalCookingTips(season)
            }
        }
    });
});

/**
 * Save user feedback on suggestions
 */
export const saveSuggestionFeedback = catchAsync(async (req: AuthRequest, res: Response): Promise<void> => {
    const userId = new mongoose.Types.ObjectId(req.user!._id.toString());
    const {
        suggestionId,
        foodId,
        rating,
        feedback,
        wasUseful,
        selectedSuggestion,
        improvementAreas
    } = req.body;

    // Validate required fields
    if (!suggestionId || rating === undefined || wasUseful === undefined) {
        res.status(400).json({
            success: false,
            message: 'Missing required feedback fields'
        });
        return;
    }

    // Here you would typically save to a SuggestionFeedback model
    // For now, we'll log the feedback
    logger.info('Suggestion feedback received', {
        userId: userId.toString(),
        suggestionId,
        foodId,
        rating,
        wasUseful,
        selectedSuggestion,
        feedback: feedback ? 'provided' : 'none'
    });

    res.json({
        success: true,
        message: 'Feedback saved successfully',
        data: {
            suggestionId,
            rating,
            wasUseful
        }
    });
});

/**
 * Get suggestion performance analytics
 */
export const getSuggestionAnalytics = catchAsync(async (req: AuthRequest, res: Response): Promise<void> => {
    const userId = new mongoose.Types.ObjectId(req.user!._id.toString());
    const { timeframe = 30 } = req.query;

    // This would typically query a SuggestionFeedback model
    // For now, we'll return mock analytics
    const analytics = {
        totalSuggestionsGenerated: 150,
        totalSuggestionsUsed: 45,
        usageRate: 30,
        averageRating: 4.2,
        topSuggestionTypes: [
            { type: SuggestionType.BLOOD_SUGAR_FRIENDLY, count: 35, rating: 4.5 },
            { type: SuggestionType.CULTURAL_PREFERENCE, count: 28, rating: 4.1 },
            { type: SuggestionType.NUTRITIONAL_BALANCE, count: 22, rating: 4.0 }
        ],
        improvementAreas: [
            'More variety in breakfast suggestions',
            'Better portion size recommendations',
            'Include more local ingredients'
        ]
    };

    res.json({
        success: true,
        data: {
            timeframe: Number(timeframe),
            analytics
        }
    });
});

// Helper functions
async function getRecentBloodSugar(userId: mongoose.Types.ObjectId) {
    const recentReading = await GlucoseReading.findOne({
        userId,
        timestamp: { $gte: new Date(Date.now() - 4 * 60 * 60 * 1000) } // Last 4 hours
    }).sort({ timestamp: -1 });

    if (recentReading) {
        return {
            value: recentReading.value,
            timestamp: recentReading.timestamp,
            targetRange: { min: 80, max: 180 } // Default range
        };
    }

    return null;
}

async function getTodayNutrition(userId: mongoose.Types.ObjectId) {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const nutrition = await DailyNutrition.findOne({
        userId,
        date: today
    });

    if (nutrition) {
        return {
            remainingCalories: Math.max(0, nutrition.goals.dailyCalories - nutrition.totalCalories),
            remainingCarbs: Math.max(0, nutrition.goals.dailyCarbohydrates - nutrition.totalCarbohydrates)
        };
    }

    return {
        remainingCalories: 2000, // Default
        remainingCarbs: 250     // Default
    };
}

function generateBloodSugarRecommendations(currentBloodSugar: number | null, targetRange: { min: number; max: number }) {
    const recommendations = [];

    if (currentBloodSugar) {
        if (currentBloodSugar > targetRange.max) {
            recommendations.push('Choose low-carb, high-fiber foods');
            recommendations.push('Avoid sugary foods and refined carbs');
            recommendations.push('Consider smaller portions');
        } else if (currentBloodSugar < targetRange.min) {
            recommendations.push('Include some healthy carbohydrates');
            recommendations.push('Consider a balanced meal with protein');
        } else {
            recommendations.push('Maintain current eating pattern');
            recommendations.push('Focus on balanced nutrition');
        }
    } else {
        recommendations.push('Monitor blood sugar before and after meals');
        recommendations.push('Choose foods with low glycemic index');
    }

    return recommendations;
}

function analyzeMealBalance(currentItems: any[]) {
    // Simplified meal analysis
    return {
        totalCalories: currentItems.reduce((sum, item) => sum + (item.calories || 0), 0),
        totalCarbs: currentItems.reduce((sum, item) => sum + (item.carbs || 0), 0),
        totalProtein: currentItems.reduce((sum, item) => sum + (item.protein || 0), 0),
        missingNutrients: ['fiber', 'vitamins'],
        balance: 'needs_protein' // or 'balanced', 'needs_carbs', etc.
    };
}

function generateMealCompletionRecommendations(analysis: any) {
    const recommendations = [];

    if (analysis.balance === 'needs_protein') {
        recommendations.push('Add a protein source like lean meat, fish, or legumes');
    }
    if (analysis.totalCarbs < 30) {
        recommendations.push('Include some healthy carbohydrates');
    }
    if (analysis.missingNutrients.includes('fiber')) {
        recommendations.push('Add vegetables or whole grains for fiber');
    }

    return recommendations;
}

function getSeason(month: number): string {
    // Southern Hemisphere seasons for South Africa
    if (month >= 12 || month <= 2) return 'summer';
    if (month >= 3 && month <= 5) return 'autumn';
    if (month >= 6 && month <= 8) return 'winter';
    return 'spring';
}

function getSeasonalRecommendations() {
    return [
        'Fresh summer fruits are in season',
        'Root vegetables are at their best',
        'Local citrus fruits are available'
    ];
}

function getInSeasonFoods(season: string) {
    const seasonalFoods = {
        summer: ['watermelon', 'tomatoes', 'corn', 'peaches'],
        autumn: ['pumpkin', 'sweet potato', 'apples', 'butternut'],
        winter: ['citrus fruits', 'cabbage', 'carrots', 'potatoes'],
        spring: ['asparagus', 'spinach', 'strawberries', 'peas']
    };
    
    return seasonalFoods[season as keyof typeof seasonalFoods] || [];
}

function getSeasonalNutritionalBenefits(season: string) {
    return [
        'High in seasonal vitamins and minerals',
        'Fresh and nutrient-dense',
        'Supports seasonal health needs'
    ];
}

function getSeasonalCookingTips(season: string) {
    const tips = {
        summer: ['Grill vegetables', 'Make fresh salads', 'Use minimal cooking'],
        autumn: ['Roast root vegetables', 'Make hearty soups', 'Use warming spices'],
        winter: ['Slow cook meals', 'Use citrus for vitamin C', 'Make warming stews'],
        spring: ['Steam fresh vegetables', 'Make light meals', 'Use fresh herbs']
    };
    
    return tips[season as keyof typeof tips] || [];
}
