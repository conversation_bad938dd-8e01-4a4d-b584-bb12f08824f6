import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../providers/language_provider.dart';
import '../../constants/app_colors.dart';
import 'popia_consent_screen.dart';
import 'features_overview_screen.dart';

class LanguageSelectionScreen extends StatefulWidget {
  final bool isOfflineMode;

  const LanguageSelectionScreen({super.key, this.isOfflineMode = false});

  @override
  State<LanguageSelectionScreen> createState() =>
      _LanguageSelectionScreenState();
}

class _LanguageSelectionScreenState extends State<LanguageSelectionScreen> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  final List<Map<String, String>> allLanguages = [
    {'name': 'Afrikaans', 'code': 'af'},
    {'name': 'English', 'code': 'en'},
    {'name': 'isiNdebele', 'code': 'nr'},
    {'name': 'isiXhosa', 'code': 'xh'},
    {'name': 'isiZulu', 'code': 'zu'},
    {'name': 'Sepedi', 'code': 'nso'},
    {'name': 'Sesotho', 'code': 'st'},
    {'name': 'Setswana', 'code': 'tn'},
    {'name': 'siSwati', 'code': 'ss'},
    {'name': 'Tshivenda', 'code': 've'},
    {'name': 'Xitsonga', 'code': 'ts'},
  ];

  List<Map<String, String>> get filteredLanguages =>
      allLanguages
          .where(
            (language) => language['name']!.toLowerCase().contains(
              _searchQuery.toLowerCase(),
            ),
          )
          .toList();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _navigateNext(BuildContext context, String languageCode) async {
    final provider = Provider.of<LanguageProvider>(context, listen: false);
    await provider.setLanguage(languageCode);

    if (!context.mounted) return;

    Navigator.push(
      context,
      MaterialPageRoute(
        builder:
            (context) =>
                widget.isOfflineMode
                    ? const FeaturesOverviewScreen(isOfflineMode: true)
                    : const POPIAConsentScreen(),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.primary,
      appBar: AppBar(
        title: const Text('Select Language'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              const Text(
                'Choose your preferred language',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.w500,
                  color: Colors.white,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              TextField(
                controller: _searchController,
                style: const TextStyle(color: Colors.white),
                decoration: InputDecoration(
                  hintText: 'Search languages',
                  hintStyle: const TextStyle(color: Colors.white70),
                  prefixIcon: const Icon(Icons.search, color: Colors.white70),
                  filled: true,
                  fillColor: AppColors.primary,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: const BorderSide(
                      color: Colors.white30,
                      width: 1,
                    ),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: const BorderSide(
                      color: Colors.white30,
                      width: 1,
                    ),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: const BorderSide(color: Colors.white, width: 2),
                  ),
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                ),
                onChanged: (value) {
                  setState(() {
                    _searchQuery = value;
                  });
                },
              ),
              const SizedBox(height: 24),
              Expanded(
                child: ListView.builder(
                  itemCount: filteredLanguages.length,
                  itemBuilder: (context, index) {
                    final language = filteredLanguages[index];
                    return Container(
                      margin: const EdgeInsets.only(bottom: 12),
                      decoration: BoxDecoration(
                        color: AppColors.primary,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.white30, width: 1),
                      ),
                      child: ListTile(
                        title: Text(
                          language['name']!,
                          style: const TextStyle(color: Colors.white),
                        ),
                        trailing: const Icon(
                          Icons.chevron_right,
                          color: Colors.white,
                        ),
                        onTap: () => _navigateNext(context, language['code']!),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
