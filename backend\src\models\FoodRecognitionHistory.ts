import mongoose, { Document, Schema } from 'mongoose';
import { FoodCategory } from './FoodEntry';

// Recognition method types
export enum RecognitionMethod {
    BARCODE = 'barcode',
    AI_VISION = 'ai_vision',
    MANUAL_SEARCH = 'manual_search',
    VOICE_INPUT = 'voice_input',
    PHOTO_ANALYSIS = 'photo_analysis'
}

// Recognition status
export enum RecognitionStatus {
    SUCCESS = 'success',
    FAILED = 'failed',
    USER_CORRECTED = 'user_corrected',
    PARTIALLY_CORRECT = 'partially_correct'
}

// Confidence levels
export enum ConfidenceLevel {
    VERY_LOW = 'very_low',    // 0-20%
    LOW = 'low',              // 21-40%
    MEDIUM = 'medium',        // 41-60%
    HIGH = 'high',            // 61-80%
    VERY_HIGH = 'very_high'   // 81-100%
}

// Interface for recognition attempt
export interface IRecognitionAttempt {
    method: RecognitionMethod;
    confidence: number; // 0-100
    confidenceLevel: ConfidenceLevel;
    processingTime: number; // milliseconds
    apiCost?: number; // cost in cents if using paid API
}

// Interface for user correction
export interface IUserCorrection {
    originalResult: string;
    correctedResult: string;
    correctionType: 'food_name' | 'category' | 'nutrition' | 'portion' | 'complete_replacement';
    userFeedback?: string;
    timestamp: Date;
}

// Interface for recognition analytics
export interface IRecognitionAnalytics {
    totalAttempts: number;
    successRate: number; // percentage
    averageConfidence: number;
    mostUsedMethod: RecognitionMethod;
    commonFailures: string[];
    improvementSuggestions: string[];
}

// Food Recognition History interface
export interface IFoodRecognitionHistory extends Document {
    userId: mongoose.Types.ObjectId;
    
    // Recognition details
    recognitionAttempt: IRecognitionAttempt;
    originalQuery?: string; // text query if manual search
    imageMetadata?: {
        size: number; // bytes
        format: string; // jpeg, png, etc.
        dimensions: { width: number; height: number };
        lighting: 'poor' | 'fair' | 'good' | 'excellent';
        clarity: 'blurry' | 'fair' | 'clear' | 'sharp';
    };
    
    // Recognition results
    recognizedFood?: {
        name: string;
        category: FoodCategory;
        confidence: number;
        alternativeMatches?: Array<{
            name: string;
            confidence: number;
        }>;
    };
    
    // Final result after user interaction
    finalResult: {
        foodName: string;
        category: FoodCategory;
        nutritionData: {
            calories: number;
            carbohydrates: number;
            protein: number;
            fat: number;
            fiber: number;
        };
        portionSize: number;
        unit: string;
    };
    
    // Status and corrections
    status: RecognitionStatus;
    userCorrection?: IUserCorrection;
    
    // Context information
    mealContext: {
        mealType: 'breakfast' | 'lunch' | 'dinner' | 'snack';
        timeOfDay: Date;
        location?: string; // home, restaurant, etc.
        socialContext?: 'alone' | 'family' | 'friends' | 'work';
    };
    
    // Learning data
    learningData: {
        wasHelpful: boolean;
        userRating?: number; // 1-5 stars
        improvementNeeded?: string[];
        tags?: string[]; // for categorizing learning patterns
    };
    
    // Metadata
    deviceInfo?: {
        platform: string;
        version: string;
        cameraQuality?: string;
    };
    
    // Timestamps
    createdAt: Date;
    updatedAt: Date;
}

// Schema definition
const recognitionAttemptSchema = new Schema({
    method: {
        type: String,
        enum: Object.values(RecognitionMethod),
        required: true
    },
    confidence: {
        type: Number,
        min: 0,
        max: 100,
        required: true
    },
    confidenceLevel: {
        type: String,
        enum: Object.values(ConfidenceLevel),
        required: true
    },
    processingTime: {
        type: Number,
        min: 0,
        required: true
    },
    apiCost: {
        type: Number,
        min: 0
    }
}, { _id: false });

const userCorrectionSchema = new Schema({
    originalResult: {
        type: String,
        required: true
    },
    correctedResult: {
        type: String,
        required: true
    },
    correctionType: {
        type: String,
        enum: ['food_name', 'category', 'nutrition', 'portion', 'complete_replacement'],
        required: true
    },
    userFeedback: String,
    timestamp: {
        type: Date,
        default: Date.now
    }
}, { _id: false });

const foodRecognitionHistorySchema = new Schema<IFoodRecognitionHistory>({
    userId: {
        type: Schema.Types.ObjectId,
        ref: 'User',
        required: true,
        index: true
    },
    recognitionAttempt: {
        type: recognitionAttemptSchema,
        required: true
    },
    originalQuery: {
        type: String,
        trim: true,
        maxlength: 200
    },
    imageMetadata: {
        size: Number,
        format: String,
        dimensions: {
            width: Number,
            height: Number
        },
        lighting: {
            type: String,
            enum: ['poor', 'fair', 'good', 'excellent']
        },
        clarity: {
            type: String,
            enum: ['blurry', 'fair', 'clear', 'sharp']
        }
    },
    recognizedFood: {
        name: String,
        category: {
            type: String,
            enum: Object.values(FoodCategory)
        },
        confidence: {
            type: Number,
            min: 0,
            max: 100
        },
        alternativeMatches: [{
            name: String,
            confidence: {
                type: Number,
                min: 0,
                max: 100
            }
        }]
    },
    finalResult: {
        foodName: {
            type: String,
            required: true
        },
        category: {
            type: String,
            enum: Object.values(FoodCategory),
            required: true
        },
        nutritionData: {
            calories: {
                type: Number,
                required: true,
                min: 0
            },
            carbohydrates: {
                type: Number,
                required: true,
                min: 0
            },
            protein: {
                type: Number,
                required: true,
                min: 0
            },
            fat: {
                type: Number,
                required: true,
                min: 0
            },
            fiber: {
                type: Number,
                required: true,
                min: 0
            }
        },
        portionSize: {
            type: Number,
            required: true,
            min: 0
        },
        unit: {
            type: String,
            required: true
        }
    },
    status: {
        type: String,
        enum: Object.values(RecognitionStatus),
        required: true,
        index: true
    },
    userCorrection: userCorrectionSchema,
    mealContext: {
        mealType: {
            type: String,
            enum: ['breakfast', 'lunch', 'dinner', 'snack'],
            required: true
        },
        timeOfDay: {
            type: Date,
            required: true
        },
        location: String,
        socialContext: {
            type: String,
            enum: ['alone', 'family', 'friends', 'work']
        }
    },
    learningData: {
        wasHelpful: {
            type: Boolean,
            required: true
        },
        userRating: {
            type: Number,
            min: 1,
            max: 5
        },
        improvementNeeded: [String],
        tags: [String]
    },
    deviceInfo: {
        platform: String,
        version: String,
        cameraQuality: String
    }
}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
});

// Indexes for performance
foodRecognitionHistorySchema.index({ userId: 1, createdAt: -1 });
foodRecognitionHistorySchema.index({ userId: 1, status: 1 });
foodRecognitionHistorySchema.index({ 'recognitionAttempt.method': 1 });
foodRecognitionHistorySchema.index({ 'mealContext.mealType': 1 });
foodRecognitionHistorySchema.index({ 'learningData.wasHelpful': 1 });

// Virtual for success rate calculation
foodRecognitionHistorySchema.virtual('isSuccessful').get(function(this: IFoodRecognitionHistory) {
    return this.status === RecognitionStatus.SUCCESS || this.status === RecognitionStatus.PARTIALLY_CORRECT;
});

// Static methods for analytics
foodRecognitionHistorySchema.statics.getUserAnalytics = async function(userId: mongoose.Types.ObjectId): Promise<IRecognitionAnalytics> {
    const pipeline = [
        { $match: { userId } },
        {
            $group: {
                _id: null,
                totalAttempts: { $sum: 1 },
                successfulAttempts: {
                    $sum: {
                        $cond: [
                            { $in: ['$status', [RecognitionStatus.SUCCESS, RecognitionStatus.PARTIALLY_CORRECT]] },
                            1,
                            0
                        ]
                    }
                },
                averageConfidence: { $avg: '$recognitionAttempt.confidence' },
                methods: { $push: '$recognitionAttempt.method' },
                failures: {
                    $push: {
                        $cond: [
                            { $eq: ['$status', RecognitionStatus.FAILED] },
                            '$recognizedFood.name',
                            null
                        ]
                    }
                }
            }
        }
    ];
    
    const result = await this.aggregate(pipeline);
    if (!result.length) {
        return {
            totalAttempts: 0,
            successRate: 0,
            averageConfidence: 0,
            mostUsedMethod: RecognitionMethod.MANUAL_SEARCH,
            commonFailures: [],
            improvementSuggestions: []
        };
    }
    
    const data = result[0];
    const successRate = (data.successfulAttempts / data.totalAttempts) * 100;
    
    // Find most used method
    const methodCounts = data.methods.reduce((acc: any, method: string) => {
        acc[method] = (acc[method] || 0) + 1;
        return acc;
    }, {});
    const mostUsedMethod = Object.keys(methodCounts).reduce((a, b) => 
        methodCounts[a] > methodCounts[b] ? a : b
    ) as RecognitionMethod;
    
    // Get common failures (filter out nulls and count occurrences)
    const failureCounts = data.failures
        .filter((f: string) => f !== null)
        .reduce((acc: any, failure: string) => {
            acc[failure] = (acc[failure] || 0) + 1;
            return acc;
        }, {});
    
    const commonFailures = Object.entries(failureCounts)
        .sort(([,a], [,b]) => (b as number) - (a as number))
        .slice(0, 5)
        .map(([failure]) => failure);
    
    // Generate improvement suggestions based on data
    const improvementSuggestions = [];
    if (successRate < 70) {
        improvementSuggestions.push('Consider improving image quality for better recognition');
    }
    if (data.averageConfidence < 60) {
        improvementSuggestions.push('Try using multiple recognition methods for better accuracy');
    }
    if (commonFailures.length > 0) {
        improvementSuggestions.push('Focus on improving recognition for commonly failed foods');
    }
    
    return {
        totalAttempts: data.totalAttempts,
        successRate: Math.round(successRate * 100) / 100,
        averageConfidence: Math.round(data.averageConfidence * 100) / 100,
        mostUsedMethod,
        commonFailures,
        improvementSuggestions
    };
};

const FoodRecognitionHistory = mongoose.model<IFoodRecognitionHistory>('FoodRecognitionHistory', foodRecognitionHistorySchema);

export default FoodRecognitionHistory;
