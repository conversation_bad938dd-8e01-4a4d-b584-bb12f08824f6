import { Types } from 'mongoose';
import FoodRecognitionHistory from '../models/FoodRecognitionHistory';
import CustomFoodModel from '../models/CustomFoodModel';
import logger from '../utils/logger';

// Interface for analytics data
export interface RecognitionAnalytics {
    totalRecognitions: number;
    successRate: number;
    averageConfidence: number;
    methodBreakdown: {
        customModel: number;
        aiVision: number;
        patternMatch: number;
    };
    accuracyTrends: {
        date: string;
        accuracy: number;
        recognitionCount: number;
    }[];
    topRecognizedFoods: {
        foodName: string;
        count: number;
        successRate: number;
    }[];
    problematicFoods: {
        foodName: string;
        failureCount: number;
        averageConfidence: number;
    }[];
    customModelPerformance: {
        totalCustomFoods: number;
        averagePerformance: number;
        topPerformers: string[];
        needsImprovement: string[];
    };
}

// Interface for recommendations
export interface AIRecommendations {
    trainingRecommendations: string[];
    recognitionImprovements: string[];
    customModelSuggestions: string[];
    generalTips: string[];
}

/**
 * Analyze recognition accuracy and performance metrics
 */
export async function analyzeRecognitionAccuracy(
    userId: Types.ObjectId,
    timeRange: string = '30d'
): Promise<RecognitionAnalytics> {
    try {
        logger.info('Analyzing recognition accuracy', { userId: userId.toString(), timeRange });

        // Calculate date range
        const endDate = new Date();
        const startDate = new Date();
        
        switch (timeRange) {
            case '7d':
                startDate.setDate(endDate.getDate() - 7);
                break;
            case '30d':
                startDate.setDate(endDate.getDate() - 30);
                break;
            case '90d':
                startDate.setDate(endDate.getDate() - 90);
                break;
            default:
                startDate.setDate(endDate.getDate() - 30);
        }

        // Get recognition history
        const recognitionHistory = await FoodRecognitionHistory.find({
            userId,
            createdAt: { $gte: startDate, $lte: endDate }
        }).sort({ createdAt: -1 });

        // Calculate basic metrics
        const totalRecognitions = recognitionHistory.length;
        const successfulRecognitions = recognitionHistory.filter(
            record => {
                // If no user correction, assume it was correct
                if (!record.userCorrection) return true;

                // If original and corrected results are the same, it was correct
                return record.userCorrection.originalResult === record.userCorrection.correctedResult;
            }
        ).length;
        const successRate = totalRecognitions > 0 ? successfulRecognitions / totalRecognitions : 0;

        // Calculate average confidence
        const totalConfidence = recognitionHistory.reduce(
            (sum, record) => sum + (record.recognitionAttempt.confidence || 0), 0
        );
        const averageConfidence = totalRecognitions > 0 ? totalConfidence / totalRecognitions : 0;

        // Method breakdown
        const methodBreakdown = {
            customModel: 0,
            aiVision: 0,
            patternMatch: 0
        };

        recognitionHistory.forEach(record => {
            const method = record.recognitionAttempt.method;
            if (method?.includes('CUSTOM')) {
                methodBreakdown.customModel++;
            } else if (method?.includes('AI') || method?.includes('VISION')) {
                methodBreakdown.aiVision++;
            } else {
                methodBreakdown.patternMatch++;
            }
        });

        // Accuracy trends (daily)
        const accuracyTrends = await calculateAccuracyTrends(userId, startDate, endDate);

        // Top recognized foods
        const topRecognizedFoods = await getTopRecognizedFoods(userId, startDate, endDate);

        // Problematic foods
        const problematicFoods = await getProblematicFoods(userId, startDate, endDate);

        // Custom model performance
        const customModelPerformance = await analyzeCustomModelPerformance(userId);

        return {
            totalRecognitions,
            successRate,
            averageConfidence,
            methodBreakdown,
            accuracyTrends,
            topRecognizedFoods,
            problematicFoods,
            customModelPerformance
        };

    } catch (error) {
        logger.error('Error analyzing recognition accuracy', {
            userId: userId.toString(),
            error: error.message
        });
        throw new Error('Failed to analyze recognition accuracy');
    }
}

/**
 * Generate personalized recommendations for improving recognition
 */
export async function generateRecommendations(
    userId: Types.ObjectId,
    analytics: RecognitionAnalytics
): Promise<AIRecommendations> {
    try {
        const recommendations: AIRecommendations = {
            trainingRecommendations: [],
            recognitionImprovements: [],
            customModelSuggestions: [],
            generalTips: []
        };

        // Training recommendations
        if (analytics.customModelPerformance.totalCustomFoods < 3) {
            recommendations.trainingRecommendations.push(
                'Consider training custom models for your frequently eaten foods to improve recognition accuracy.'
            );
        }

        if (analytics.customModelPerformance.needsImprovement.length > 0) {
            recommendations.trainingRecommendations.push(
                `Add more training examples for: ${analytics.customModelPerformance.needsImprovement.join(', ')}`
            );
        }

        // Recognition improvements
        if (analytics.averageConfidence < 0.7) {
            recommendations.recognitionImprovements.push(
                'Try taking photos in better lighting conditions to improve recognition confidence.'
            );
            recommendations.recognitionImprovements.push(
                'Ensure food items are clearly visible and not overlapping in photos.'
            );
        }

        if (analytics.successRate < 0.8) {
            recommendations.recognitionImprovements.push(
                'Provide feedback on incorrect recognitions to help improve the AI.'
            );
        }

        // Custom model suggestions
        const frequentFoods = analytics.topRecognizedFoods.slice(0, 3);
        for (const food of frequentFoods) {
            if (food.successRate < 0.8) {
                recommendations.customModelSuggestions.push(
                    `Create a custom model for "${food.foodName}" to improve recognition accuracy.`
                );
            }
        }

        // General tips
        recommendations.generalTips.push(
            'Take photos from directly above the food for best results.'
        );
        recommendations.generalTips.push(
            'Include reference objects (like utensils) to help with portion estimation.'
        );
        recommendations.generalTips.push(
            'Use the multi-detection feature when photographing meals with multiple items.'
        );

        if (analytics.methodBreakdown.customModel > analytics.methodBreakdown.aiVision) {
            recommendations.generalTips.push(
                'Great job using custom models! They typically provide better accuracy for your specific foods.'
            );
        }

        return recommendations;

    } catch (error) {
        logger.error('Error generating recommendations', {
            userId: userId.toString(),
            error: error.message
        });
        throw new Error('Failed to generate recommendations');
    }
}

/**
 * Calculate accuracy trends over time
 */
async function calculateAccuracyTrends(
    userId: Types.ObjectId,
    startDate: Date,
    endDate: Date
) {
    const trends = await FoodRecognitionHistory.aggregate([
        {
            $match: {
                userId,
                createdAt: { $gte: startDate, $lte: endDate }
            }
        },
        {
            $group: {
                _id: {
                    $dateToString: {
                        format: '%Y-%m-%d',
                        date: '$createdAt'
                    }
                },
                totalRecognitions: { $sum: 1 },
                successfulRecognitions: {
                    $sum: {
                        $cond: [
                            {
                                $or: [
                                    { $eq: ['$userCorrection', null] },
                                    { $eq: ['$userCorrection.originalResult', '$userCorrection.correctedResult'] }
                                ]
                            },
                            1,
                            0
                        ]
                    }
                }
            }
        },
        {
            $project: {
                date: '$_id',
                recognitionCount: '$totalRecognitions',
                accuracy: {
                    $cond: [
                        { $gt: ['$totalRecognitions', 0] },
                        { $divide: ['$successfulRecognitions', '$totalRecognitions'] },
                        0
                    ]
                }
            }
        },
        { $sort: { date: 1 } }
    ]);

    return trends;
}

/**
 * Get top recognized foods
 */
async function getTopRecognizedFoods(
    userId: Types.ObjectId,
    startDate: Date,
    endDate: Date
) {
    const topFoods = await FoodRecognitionHistory.aggregate([
        {
            $match: {
                userId,
                createdAt: { $gte: startDate, $lte: endDate },
                'recognizedFood.name': { $exists: true }
            }
        },
        {
            $group: {
                _id: '$recognizedFood.name',
                count: { $sum: 1 },
                successfulRecognitions: {
                    $sum: {
                        $cond: [
                            {
                                $or: [
                                    { $eq: ['$userCorrection', null] },
                                    { $eq: ['$userCorrection.originalResult', '$userCorrection.correctedResult'] }
                                ]
                            },
                            1,
                            0
                        ]
                    }
                }
            }
        },
        {
            $project: {
                foodName: '$_id',
                count: 1,
                successRate: {
                    $cond: [
                        { $gt: ['$count', 0] },
                        { $divide: ['$successfulRecognitions', '$count'] },
                        0
                    ]
                }
            }
        },
        { $sort: { count: -1 } },
        { $limit: 10 }
    ]);

    return topFoods;
}

/**
 * Get problematic foods (low success rate)
 */
async function getProblematicFoods(
    userId: Types.ObjectId,
    startDate: Date,
    endDate: Date
) {
    const problematicFoods = await FoodRecognitionHistory.aggregate([
        {
            $match: {
                userId,
                createdAt: { $gte: startDate, $lte: endDate },
                $and: [
                    { 'userCorrection': { $exists: true } },
                    { $expr: { $ne: ['$userCorrection.originalResult', '$userCorrection.correctedResult'] } }
                ]
            }
        },
        {
            $group: {
                _id: '$recognizedFood.name',
                failureCount: { $sum: 1 },
                averageConfidence: { $avg: '$recognitionAttempt.confidence' }
            }
        },
        {
            $project: {
                foodName: '$_id',
                failureCount: 1,
                averageConfidence: { $round: ['$averageConfidence', 2] }
            }
        },
        { $sort: { failureCount: -1 } },
        { $limit: 5 }
    ]);

    return problematicFoods;
}

/**
 * Analyze custom model performance
 */
async function analyzeCustomModelPerformance(userId: Types.ObjectId) {
    const customFoods = await CustomFoodModel.find({ userId });
    
    const totalCustomFoods = customFoods.length;
    const averagePerformance = totalCustomFoods > 0 
        ? customFoods.reduce((sum, food) => sum + food.successRate, 0) / totalCustomFoods 
        : 0;

    const topPerformers = customFoods
        .filter(food => food.successRate > 0.8)
        .sort((a, b) => b.successRate - a.successRate)
        .slice(0, 5)
        .map(food => food.foodName);

    const needsImprovement = customFoods
        .filter(food => food.successRate < 0.6 || food.trainingFeatures.length < 3)
        .map(food => food.foodName);

    return {
        totalCustomFoods,
        averagePerformance,
        topPerformers,
        needsImprovement
    };
}
