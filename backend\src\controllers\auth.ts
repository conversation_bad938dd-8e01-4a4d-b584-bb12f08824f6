import { Request, Response } from 'express';
import crypto from 'crypto';
import User, { IUser } from '../models/User';
import {
    generateOTP,
    sendOTP,
    checkPhoneVerificationLimit,
    verifyOTP as verifyOTPService,
    clearRateLimit as clearRateLimitService
} from '../services/messaging';

// Extend Request interface to include user
interface AuthRequest extends Request {
    user?: IUser;
}

// Helper type for Express handlers that can return early
type AsyncHandler = (req: Request | AuthRequest, res: Response) => Promise<any>;

// @desc    Start phone verification process
// @route   POST /api/auth/verify-phone
// @access  Public
export const startPhoneVerification: AsyncHandler = async (req: Request, res: Response) => {
    try {
        const { phoneNumber, language = 'en' } = req.body;
        
        if (!phoneNumber) {
            return res.status(400).json({
                success: false,
                message: 'Phone number is required'
            });
        }

        console.log(`Verification request received for ${phoneNumber}`);

        // Check rate limiting
        const canSendOTP = await checkPhoneVerificationLimit(phoneNumber);
        if (!canSendOTP) {
            console.log(`Rate limit check failed for ${phoneNumber}`);
            return res.status(429).json({
                success: false,
                message: 'Too many attempts. Please wait for one hour and try again.'
            });
        }

        // Find or create user
        let user = await User.findOne({ phoneNumber });
        if (!user) {
            console.log(`Creating new user for ${phoneNumber}`);
            user = new User({ phoneNumber });
            await user.save();
        }

        // Generate and send OTP
        const otp = generateOTP();
        console.log(`Attempting to send OTP to ${phoneNumber}`);
        const otpSent = await sendOTP(phoneNumber, otp, language);

        if (!otpSent) {
            console.log(`Failed to send OTP to ${phoneNumber}`);
            return res.status(500).json({
                success: false,
                message: 'Failed to send verification code. Please try again.'
            });
        }

        // Update user's OTP attempts
        user.otpAttempts += 1;
        user.lastOtpSent = new Date();
        await user.save();

        console.log(`OTP sent successfully to ${phoneNumber}`);
        return res.status(200).json({
            success: true,
            message: 'Verification code sent successfully'
        });
    } catch (error) {
        console.error('Phone Verification Error:', error);
        return res.status(500).json({
            success: false,
            message: (error as Error).message,
            details: process.env.NODE_ENV === 'development' ? (error as Error).stack : undefined
        });
    }
};

// @desc    Verify OTP and complete authentication
// @route   POST /api/auth/verify-otp
// @access  Public
export const verifyOTP: AsyncHandler = async (req: Request, res: Response) => {
    try {
        const { phoneNumber, otp } = req.body;
        console.log('OTP verification request:', { phoneNumber, otp });

        // Find user by phone number
        let user = await User.findOne({ phoneNumber });
        
        if (!user) {
            console.log('No user found for phone number:', phoneNumber);
            return res.status(400).json({
                success: false,
                message: 'No verification code was sent to this number'
            });
        }

        console.log('Found user:', { id: user._id, phoneNumber: user.phoneNumber });

        // Verify OTP from Redis
        const isValid = await verifyOTPService(phoneNumber, otp);
        console.log('OTP verification result:', isValid);

        if (!isValid) {
            return res.status(400).json({
                success: false,
                message: 'Invalid or expired verification code'
            });
        }

        // Update user verification status
        user.isPhoneVerified = true;
        await user.save();
        console.log('User verification status updated');        // Generate token
        const token = user.getSignedJwtToken();

        return res.status(200).json({
            success: true,
            token,
            user: {
                name: user.name,
                ageGroup: user.ageGroup,
                diabetesType: user.diabetesType,
                language: user.language
            }
        });
    } catch (error) {
        console.error('OTP Verification Error:', error);
        return res.status(500).json({
            success: false,
            message: (error as Error).message
        });
    }
};

// @desc    Update user profile (for authenticated users)
// @route   PUT /api/auth/profile
// @access  Private
export const updateProfile: AsyncHandler = async (req: AuthRequest, res: Response) => {
    try {
        const { name, ageGroup, diabetesType, language, givePopiaConsent, profilePicturePath, guardianName, guardianPhone, guardianEmail } = req.body;

        // Get user from JWT token (set by protect middleware)
        const user = await User.findById(req.user?.id);

        if (!user) {
            return res.status(404).json({
                success: false,
                message: 'User not found'
            });
        }

        // Update fields if provided
        if (name !== undefined) user.name = name;
        if (ageGroup !== undefined) user.ageGroup = ageGroup;
        if (diabetesType !== undefined) user.diabetesType = diabetesType;
        if (language !== undefined) user.language = language;

        if (givePopiaConsent) {
            user.popiaConsent = new Date();
        }

        // Update new profile fields
        if (profilePicturePath !== undefined) user.profilePicturePath = profilePicturePath;
        if (guardianName !== undefined) user.guardianName = guardianName;
        if (guardianPhone !== undefined) user.guardianPhone = guardianPhone;
        if (guardianEmail !== undefined) user.guardianEmail = guardianEmail;

        await user.save();

        console.log(`Profile updated for user ${user.email}`);
        return res.status(200).json({
            success: true,
            message: 'Profile updated successfully',
            user: {
                id: user._id,
                email: user.email,
                name: user.name,
                phoneNumber: user.phoneNumber,
                ageGroup: user.ageGroup,
                diabetesType: user.diabetesType,
                language: user.language,
                popiaConsent: user.popiaConsent,
                profilePicturePath: user.profilePicturePath,
                guardianName: user.guardianName,
                guardianPhone: user.guardianPhone,
                guardianEmail: user.guardianEmail
            }
        });
    } catch (error) {
        console.error('Update profile error:', error);
        return res.status(500).json({
            success: false,
            message: 'Server error during profile update'
        });
    }
};

// @desc    Update or create user profile (legacy phone-based)
// @route   POST /api/auth/update-profile
// @access  Public
export const updateProfileLegacy: AsyncHandler = async (req: Request, res: Response) => {
    try {
        const { phoneNumber, name, ageGroup, diabetesType, language, givePopiaConsent } = req.body;

        // Find user or create new one
        let user = await User.findOne({ phoneNumber });

        if (!user) {
            user = new User({ phoneNumber });
        }

        // Update fields
        user.name = name;
        user.ageGroup = ageGroup;
        user.diabetesType = diabetesType;
        user.language = language;

        if (givePopiaConsent) {
            user.popiaConsent = new Date();
        }

        await user.save();

        // Generate token
        const token = user.getSignedJwtToken();

        res.status(200).json({
            success: true,
            token,
            user
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: (error as Error).message
        });
    }
};

// @desc    Get current user profile
// @route   GET /api/auth/me
// @access  Private
export const getMe: AsyncHandler = async (req: AuthRequest, res: Response) => {
    try {
        const user = await User.findById(req.user?.id);

        res.status(200).json({
            success: true,
            user
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: (error as Error).message
        });
    }
};

// @desc    Clear rate limit for testing
// @route   POST /api/auth/clear-rate-limit
// @access  Public (only in development)
export const clearRateLimit: AsyncHandler = async (req: Request, res: Response) => {
    if (process.env.NODE_ENV !== 'development') {
        return res.status(403).json({
            success: false,
            message: 'This endpoint is only available in development mode'
        });
    }

    try {
        const { phoneNumber } = req.body;
        
        if (!phoneNumber) {
            return res.status(400).json({
                success: false,
                message: 'Phone number is required'
            });
        }

        // Clear rate limit
        const cleared = await clearRateLimitService(phoneNumber);
        
        if (cleared) {
            return res.status(200).json({
                success: true,
                message: 'Rate limit cleared successfully'
            });
        } else {
            return res.status(500).json({
                success: false,
                message: 'Failed to clear rate limit'
            });
        }
    } catch (error) {
        console.error('Clear Rate Limit Error:', error);
        return res.status(500).json({
            success: false,
            message: (error as Error).message
        });
    }
};

// @desc    Register user with email/password and send phone OTP
// @route   POST /api/auth/register
// @access  Public
export const register: AsyncHandler = async (req: Request, res: Response) => {
    try {
        const { email, password, phoneNumber, name, language = 'en' } = req.body;

        if (!email || !password || !phoneNumber) {
            return res.status(400).json({
                success: false,
                message: 'Email, password, and phone number are required'
            });
        }

        console.log(`Registration request received for ${email}, phone: ${phoneNumber}`);

        // Check if user already exists
        const existingUser = await User.findOne({
            $or: [{ email }, { phoneNumber }]
        });

        if (existingUser) {
            return res.status(400).json({
                success: false,
                message: 'User already exists with this email or phone number'
            });
        }

        // Check rate limiting for phone
        const canSendOTP = await checkPhoneVerificationLimit(phoneNumber);
        if (!canSendOTP) {
            console.log(`Rate limit check failed for ${phoneNumber}`);
            return res.status(429).json({
                success: false,
                message: 'Too many attempts. Please wait for one hour and try again.'
            });
        }

        // Create new user
        const user = new User({
            email,
            password,
            phoneNumber,
            name
        });

        await user.save();
        console.log(`User created with ID: ${user._id}`);

        // Generate and send OTP for phone verification
        const otp = generateOTP();
        console.log(`Attempting to send OTP to ${phoneNumber}`);
        const otpSent = await sendOTP(phoneNumber, otp, language);

        if (!otpSent) {
            console.log(`Failed to send OTP to ${phoneNumber}`);
            // Delete the user if OTP sending fails
            await User.findByIdAndDelete(user._id);
            return res.status(500).json({
                success: false,
                message: 'Failed to send verification code. Please try again.'
            });
        }

        console.log(`OTP sent successfully to ${phoneNumber}`);
        return res.status(201).json({
            success: true,
            message: 'User registered successfully. Please verify your phone number.',
            userId: user._id
        });

    } catch (error) {
        console.error('Registration error:', error);
        return res.status(500).json({
            success: false,
            message: 'Server error during registration'
        });
    }
};

// @desc    Login user with email/password
// @route   POST /api/auth/login
// @access  Public
export const login: AsyncHandler = async (req: Request, res: Response) => {
    try {
        const { email, password } = req.body;

        if (!email || !password) {
            return res.status(400).json({
                success: false,
                message: 'Email and password are required'
            });
        }

        console.log(`Login request received for ${email}`);

        // Find user by email
        const user = await User.findOne({ email }).select('+password');

        if (!user) {
            return res.status(401).json({
                success: false,
                message: 'Invalid email or password'
            });
        }

        // Note: For email/password login, we don't require phone verification
        // Phone verification is only required during registration for security
        // Users can log in with verified email/password credentials

        // Check password
        const isPasswordMatch = await user.matchPassword(password);

        if (!isPasswordMatch) {
            return res.status(401).json({
                success: false,
                message: 'Invalid email or password'
            });
        }

        // Generate token
        const token = user.getSignedJwtToken();

        console.log(`User ${email} logged in successfully`);
        return res.status(200).json({
            success: true,
            token,
            user: {
                id: user._id,
                email: user.email,
                name: user.name,
                phoneNumber: user.phoneNumber,
                ageGroup: user.ageGroup,
                diabetesType: user.diabetesType,
                language: user.language
            }
        });

    } catch (error) {
        console.error('Login error:', error);
        return res.status(500).json({
            success: false,
            message: 'Server error during login'
        });
    }
};

// @desc    Forgot password - send reset token via SMS
// @route   POST /api/auth/forgot-password
// @access  Public
export const forgotPassword: AsyncHandler = async (req: Request, res: Response) => {
    try {
        const { email } = req.body;

        if (!email) {
            return res.status(400).json({
                success: false,
                message: 'Email is required'
            });
        }

        console.log(`Forgot password request for ${email}`);

        // Find user by email
        const user = await User.findOne({ email });

        if (!user) {
            return res.status(404).json({
                success: false,
                message: 'No user found with this email address'
            });
        }

        // Check if phone is verified (required for password reset)
        if (!user.isPhoneVerified) {
            return res.status(400).json({
                success: false,
                message: 'Phone number must be verified before password reset'
            });
        }

        // Generate reset token
        const resetToken = user.getResetPasswordToken();
        await user.save();

        // Send reset token via SMS to verified phone
        // For development, we'll use the same SMS service
        const otpSent = await sendOTP(user.phoneNumber, resetToken);

        if (!otpSent) {
            // Clear the reset token if SMS fails
            user.resetPasswordToken = undefined;
            user.resetPasswordExpire = undefined;
            await user.save();

            return res.status(500).json({
                success: false,
                message: 'Failed to send reset code. Please try again.'
            });
        }

        console.log(`Password reset code sent to ${user.phoneNumber}`);
        return res.status(200).json({
            success: true,
            message: 'Password reset code sent to your registered phone number'
        });

    } catch (error) {
        console.error('Forgot password error:', error);
        return res.status(500).json({
            success: false,
            message: 'Server error during password reset request'
        });
    }
};

// @desc    Reset password using token
// @route   POST /api/auth/reset-password
// @access  Public
export const resetPassword: AsyncHandler = async (req: Request, res: Response) => {
    try {
        const { email, resetToken, newPassword } = req.body;

        if (!email || !resetToken || !newPassword) {
            return res.status(400).json({
                success: false,
                message: 'Email, reset token, and new password are required'
            });
        }

        console.log(`Password reset attempt for ${email}`);

        // Hash the token to compare with stored hash
        const hashedToken = crypto
            .createHash('sha256')
            .update(resetToken)
            .digest('hex');

        // Find user by email and valid reset token
        const user = await User.findOne({
            email,
            resetPasswordToken: hashedToken,
            resetPasswordExpire: { $gt: Date.now() }
        });

        if (!user) {
            return res.status(400).json({
                success: false,
                message: 'Invalid or expired reset token'
            });
        }

        // Validate new password
        if (newPassword.length < 8) {
            return res.status(400).json({
                success: false,
                message: 'Password must be at least 8 characters long'
            });
        }

        // Set new password
        user.password = newPassword;
        user.resetPasswordToken = undefined;
        user.resetPasswordExpire = undefined;
        await user.save();

        console.log(`Password reset successful for ${email}`);
        return res.status(200).json({
            success: true,
            message: 'Password reset successful. You can now login with your new password.'
        });

    } catch (error) {
        console.error('Reset password error:', error);
        return res.status(500).json({
            success: false,
            message: 'Server error during password reset'
        });
    }
};
