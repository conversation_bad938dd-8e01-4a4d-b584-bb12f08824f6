import { Router } from 'express';
import {
    getHealthStatus,
    getDetailedHealthStatus,
    getDatabaseHealth,
    getRedisHealth,
    getReadinessProbe,
    getLivenessProbe
} from '../controllers/health';
import { protect, authorize } from '../middleware/auth';

const router = Router();

// @route   GET /api/health
// @desc    Get basic health status
// @access  Public
router.get('/', getHealthStatus);

// @route   GET /api/health/detailed
// @desc    Get detailed health status with system information
// @access  Private (Admin only)
router.get('/detailed', protect, authorize('admin'), getDetailedHealthStatus);

// @route   GET /api/health/database
// @desc    Get database health status
// @access  Private
router.get('/database', protect, getDatabaseHealth);

// @route   GET /api/health/redis
// @desc    Get Redis health status
// @access  Private
router.get('/redis', protect, getRedisHealth);

// @route   GET /api/health/ready
// @desc    Readiness probe for container orchestration
// @access  Public
router.get('/ready', getReadinessProbe);

// @route   GET /api/health/live
// @desc    Liveness probe for container orchestration
// @access  Public
router.get('/live', getLivenessProbe);

export default router;
