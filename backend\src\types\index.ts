import { Request } from 'express';
import { Document } from 'mongoose';

// User interface - matches the actual User model
export interface IUser extends Document {
  _id: string;
  email: string;
  password: string;
  phoneNumber: string; // Fixed: was 'phone', now matches model
  isPhoneVerified: boolean; // Fixed: was 'isVerified', now matches model
  otpAttempts: number;
  lastOtpSent?: Date;
  currentOTP?: string;
  otpExpiresAt?: Date;
  name?: string;
  ageGroup?: 'Under 18' | '18-30' | '31-45' | '46-60' | 'Over 60';
  diabetesType?: 'Type 1' | 'Type 2' | 'Gestational' | 'Pre-diabetes' | 'Not sure' | null;
  language: 'af' | 'en' | 'nr' | 'xh' | 'zu' | 'nso' | 'st' | 'tn' | 'ss' | 've' | 'ts';
  popiaConsent?: Date;
  resetPasswordToken?: string;
  resetPasswordExpire?: Date; // Fixed: was 'resetPasswordExpires', now matches model
  createdAt: Date;
  updatedAt: Date;

  // Methods
  getSignedJwtToken(): string;
  matchPassword(enteredPassword: string): Promise<boolean>;
  setOTP(otp: string): Promise<void>;
  matchOTP(enteredOtp: string): Promise<boolean>;
  getResetPasswordToken(): string;
}

// Auth request interface
export interface AuthRequest extends Request {
  user?: IUser;
}

// API Response interface
export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  error?: string;
}

// JWT Payload interface
export interface JWTPayload {
  userId: string;
  email: string;
  iat?: number;
  exp?: number;
}

// Registration request body
export interface RegisterRequestBody {
  email: string;
  password: string;
  phoneNumber: string; // Fixed: was 'phone', now consistent
  name: string;
}

// Login request body
export interface LoginRequestBody {
  email: string;
  password: string;
}

// Verify OTP request body
export interface VerifyOTPRequestBody {
  phoneNumber: string; // Fixed: was 'email', should be phoneNumber for OTP verification
  otp: string;
}

// Forgot password request body
export interface ForgotPasswordRequestBody {
  email: string;
}

// Reset password request body
export interface ResetPasswordRequestBody {
  email: string; // Added: need email to identify user
  resetToken: string; // Fixed: was 'token', now more descriptive
  newPassword: string;
}

// Profile update request body
export interface UpdateProfileRequestBody {
  name?: string;
  diabetesType?: 'Type 1' | 'Type 2' | 'Gestational' | 'Pre-diabetes' | 'Not sure' | null;
  language?: 'af' | 'en' | 'nr' | 'xh' | 'zu' | 'nso' | 'st' | 'tn' | 'ss' | 've' | 'ts';
  popiaConsent?: boolean;
}

// SMS service interface
export interface SMSService {
  sendOTP(phone: string, otp: string): Promise<boolean>;
}

// Glucose Reading interfaces
export interface IGlucoseReading extends Document {
  _id: string;
  userId: string;
  value: number;
  timestamp: Date;
  mealTiming?: 'before_breakfast' | 'after_breakfast' | 'before_lunch' | 'after_lunch' | 'before_dinner' | 'after_dinner' | 'bedtime' | 'other';
  notes?: string;
  tags?: string[];
  context?: 'fasting' | 'post_meal' | 'exercise' | 'stress' | 'illness' | 'medication' | 'other';
  symptoms?: string[];
  location?: string;
  deviceId?: string;
  isManual: boolean;
  confidence?: 'low' | 'medium' | 'high';
  category?: 'low' | 'normal' | 'high' | 'very_high';
  timeCategory?: 'morning' | 'afternoon' | 'evening' | 'night';
  trend?: 'rising' | 'falling' | 'stable';
  createdAt: Date;
  updatedAt: Date;
}

// Create glucose reading request body
export interface CreateGlucoseReadingRequestBody {
  value: number;
  timestamp?: Date;
  mealTiming?: 'before_breakfast' | 'after_breakfast' | 'before_lunch' | 'after_lunch' | 'before_dinner' | 'after_dinner' | 'bedtime' | 'other';
  notes?: string;
  tags?: string[];
  context?: 'fasting' | 'post_meal' | 'exercise' | 'stress' | 'illness' | 'medication' | 'other';
  symptoms?: string[];
  location?: string;
  deviceId?: string;
  isManual?: boolean;
  confidence?: 'low' | 'medium' | 'high';
}

// Update glucose reading request body
export interface UpdateGlucoseReadingRequestBody {
  value?: number;
  timestamp?: Date;
  mealTiming?: 'before_breakfast' | 'after_breakfast' | 'before_lunch' | 'after_lunch' | 'before_dinner' | 'after_dinner' | 'bedtime' | 'other';
  notes?: string;
  tags?: string[];
  context?: 'fasting' | 'post_meal' | 'exercise' | 'stress' | 'illness' | 'medication' | 'other';
  symptoms?: string[];
  location?: string;
  deviceId?: string;
  isManual?: boolean;
  confidence?: 'low' | 'medium' | 'high';
}

// Glucose reading query parameters
export interface GlucoseReadingQueryParams {
  startDate?: string;
  endDate?: string;
  mealTiming?: string;
  context?: string;
  category?: string;
  timeCategory?: string;
  limit?: string;
  page?: string;
  sortBy?: 'timestamp' | 'value' | 'category';
  sortOrder?: 'asc' | 'desc';
  format?: 'json' | 'csv'; // For export functionality
}

// Advanced analytics query parameters
export interface AnalyticsQueryParams {
  startDate?: string;
  endDate?: string;
  period?: 'day' | 'week' | 'month' | 'year';
  groupBy?: 'hour' | 'day' | 'week' | 'month';
  includePatterns?: boolean;
  includeTrends?: boolean;
}

// Chart data export parameters
export interface ChartDataParams {
  startDate?: string;
  endDate?: string;
  chartType?: 'line' | 'area' | 'bar' | 'scatter';
  aggregation?: 'none' | 'hourly' | 'daily' | 'weekly';
  format?: 'json' | 'csv';
}

// Glucose insights response
export interface GlucoseInsights {
  averageByTimeOfDay: { [key: string]: number };
  patterns: {
    type: 'meal_response' | 'exercise_impact' | 'stress_correlation' | 'time_pattern';
    description: string;
    confidence: 'low' | 'medium' | 'high';
    recommendation?: string;
  }[];
  trends: {
    period: string;
    direction: 'improving' | 'worsening' | 'stable';
    change: number;
    significance: 'low' | 'medium' | 'high';
  }[];
  alerts: {
    type: 'frequent_lows' | 'frequent_highs' | 'high_variability' | 'missing_data';
    severity: 'low' | 'medium' | 'high';
    message: string;
    actionRequired: boolean;
  }[];
}

// Advanced analytics response
export interface AdvancedAnalytics {
  timeInRange: {
    low: number; // percentage
    normal: number;
    high: number;
    veryHigh: number;
  };
  variabilityMetrics: {
    standardDeviation: number;
    coefficientOfVariation: number;
    meanAmplitudeOfGlycemicExcursions: number;
  };
  dailyPatterns: {
    [hour: string]: {
      average: number;
      count: number;
      category: 'low' | 'normal' | 'high' | 'very_high';
    };
  };
  weeklyTrends: {
    [day: string]: {
      average: number;
      count: number;
      trend: 'rising' | 'falling' | 'stable';
    };
  };
  correlations: {
    mealTiming: { [timing: string]: number };
    context: { [context: string]: number };
    symptoms: { [symptom: string]: number };
  };
}

// Environment variables interface
export interface EnvConfig {
  NODE_ENV: string;
  PORT: string;
  MONGODB_URI: string;
  JWT_SECRET: string;
  JWT_EXPIRE: string;
  REDIS_URL?: string;
  TWILIO_ACCOUNT_SID?: string;
  TWILIO_AUTH_TOKEN?: string;
  TWILIO_PHONE_NUMBER?: string;
}
